<?php
/**
 * Order Tracking Management View
 */

if (!defined('ABSPATH')) {
    exit;
}

// Initialize order tracker
$order_tracker = new Hozi_Akadly_Order_Tracker();

// Handle form submissions
if (isset($_POST['update_tracking_status']) && wp_verify_nonce($_POST['_wpnonce'], 'hozi_tracking_update')) {
    $order_id = intval($_POST['order_id']);
    $status = sanitize_text_field($_POST['status']);
    $reason_category = sanitize_text_field($_POST['reason_category']);
    $reason_details = sanitize_textarea_field($_POST['reason_details']);
    $notes = sanitize_textarea_field($_POST['notes']);

    $result = $order_tracker->update_tracking_status($order_id, $status, $reason_category, $reason_details, $notes);
    
    if ($result) {
        echo '<div class="notice notice-success"><p>' . __('تم تحديث حالة الطلب بنجاح', 'hozi-akadly') . '</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>' . __('فشل في تحديث حالة الطلب', 'hozi-akadly') . '</p></div>';
    }
}

// Get filter parameters
$status_filter = isset($_GET['status_filter']) ? sanitize_text_field($_GET['status_filter']) : '';
$agent_filter = isset($_GET['agent_filter']) ? intval($_GET['agent_filter']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';
$search_query = isset($_GET['search_query']) ? sanitize_text_field($_GET['search_query']) : '';
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
$current_page = isset($_GET['paged']) ? intval($_GET['paged']) : 1;

// Get all confirmed orders from delivery tracking with enhanced filtering
global $wpdb;

// Build the WHERE clause for filtering
$where_conditions = array("oa.confirmation_status = 'confirmed'");
$where_params = array();

if ($status_filter) {
    $where_conditions[] = "ot.status = %s";
    $where_params[] = $status_filter;
}

if ($agent_filter) {
    $where_conditions[] = "oa.agent_id = %d";
    $where_params[] = $agent_filter;
}

if ($date_from) {
    $where_conditions[] = "DATE(oa.confirmed_at) >= %s";
    $where_params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(oa.confirmed_at) <= %s";
    $where_params[] = $date_to;
}

if ($search_query) {
    $where_conditions[] = "(oa.order_id LIKE %s OR u.display_name LIKE %s)";
    $where_params[] = '%' . $search_query . '%';
    $where_params[] = '%' . $search_query . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
if (!empty($where_params)) {
    $count_query = "
        SELECT COUNT(DISTINCT oa.order_id)
        FROM {$wpdb->prefix}hozi_order_assignments oa
        LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
        LEFT JOIN {$wpdb->users} u ON oa.agent_id = u.ID
        WHERE {$where_clause}
    ";
    $total_orders = $wpdb->get_var($wpdb->prepare($count_query, $where_params));
} else {
    $count_query = "
        SELECT COUNT(DISTINCT oa.order_id)
        FROM {$wpdb->prefix}hozi_order_assignments oa
        LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
        LEFT JOIN {$wpdb->users} u ON oa.agent_id = u.ID
        WHERE {$where_clause}
    ";
    $total_orders = $wpdb->get_var($count_query);
}

// Calculate pagination
$offset = ($current_page - 1) * $per_page;
$total_pages = ceil($total_orders / $per_page);

// Get orders with pagination
$orders_query = "
    SELECT DISTINCT
        oa.order_id,
        oa.agent_id,
        oa.confirmed_at,
        oa.notes as confirmation_notes,
        u.display_name as agent_name,
        ot.status as tracking_status,
        ot.reason_category,
        ot.reason_details,
        ot.notes as tracking_notes,
        ot.updated_at as last_update,
        ot.updated_by
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
    LEFT JOIN {$wpdb->users} u ON oa.agent_id = u.ID
    WHERE {$where_clause}
    ORDER BY oa.confirmed_at DESC
    LIMIT %d OFFSET %d
";

$final_params = array_merge($where_params, array($per_page, $offset));

if (!empty($final_params)) {
    $confirmed_orders = $wpdb->get_results($wpdb->prepare($orders_query, $final_params));
} else {
    // If no parameters, execute query directly
    $orders_query_no_params = "
        SELECT DISTINCT
            oa.order_id,
            oa.agent_id,
            oa.confirmed_at,
            oa.notes as confirmation_notes,
            u.display_name as agent_name,
            ot.status as tracking_status,
            ot.reason_category,
            ot.reason_details,
            ot.notes as tracking_notes,
            ot.updated_at as last_update,
            ot.updated_by
        FROM {$wpdb->prefix}hozi_order_assignments oa
        LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
        LEFT JOIN {$wpdb->users} u ON oa.agent_id = u.ID
        WHERE oa.confirmation_status = 'confirmed'
        ORDER BY oa.confirmed_at DESC
        LIMIT {$per_page} OFFSET {$offset}
    ";
    $confirmed_orders = $wpdb->get_results($orders_query_no_params);
}

// Get tracking statistics
$tracking_stats = $order_tracker->get_tracking_statistics();
$statuses = Hozi_Akadly_Order_Tracker::get_tracking_statuses();
$reason_categories = Hozi_Akadly_Order_Tracker::get_reason_categories();

// Get all agents for filter dropdown
$agent_manager = new Hozi_Akadly_Agent_Manager();
$all_agents = $agent_manager->get_agents();
?>

<div class="wrap hozi-tracking-wrap">
    <h1><?php _e('تتبع الطلبيات - أكدلي', 'hozi-akadly'); ?></h1>

    <!-- Statistics Cards -->
    <div class="hozi-tracking-stats">
        <h2><?php _e('إحصائيات التتبع', 'hozi-akadly'); ?></h2>
        <div class="hozi-stats-grid">
            <div class="hozi-stat-card total">
                <div class="hozi-stat-icon">📊</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats['total']); ?></h3>
                    <p><?php _e('إجمالي الطلبيات المتتبعة', 'hozi-akadly'); ?></p>
                </div>
            </div>

            <?php foreach ($tracking_stats['stats'] as $stat) : ?>
                <div class="hozi-stat-card" style="border-left-color: <?php echo esc_attr($stat['color']); ?>">
                    <div class="hozi-stat-content">
                        <h3><?php echo esc_html($stat['count']); ?></h3>
                        <p><?php echo esc_html($stat['label']); ?></p>
                        <span class="hozi-percentage"><?php echo esc_html($stat['percentage']); ?>%</span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="hozi-filters-section">
        <div class="hozi-filters-header">
            <h3>🔍 فلترة وبحث الطلبيات</h3>
            <button type="button" class="button" id="toggle-filters">
                <span class="dashicons dashicons-filter"></span>
                <?php echo ($status_filter || $agent_filter || $date_from || $date_to || $search_query) ? 'إخفاء الفلاتر' : 'إظهار الفلاتر'; ?>
            </button>
        </div>

        <div class="hozi-filters-content" <?php echo (!$status_filter && !$agent_filter && !$date_from && !$date_to && !$search_query) ? 'style="display: none;"' : ''; ?>>
            <form method="GET" action="" class="hozi-filters-form">
                <input type="hidden" name="page" value="hozi-akadly-tracking">

                <div class="hozi-filters-grid">
                    <!-- Search -->
                    <div class="hozi-filter-group">
                        <label for="search_query">🔍 البحث:</label>
                        <input type="text" id="search_query" name="search_query"
                               value="<?php echo esc_attr($search_query); ?>"
                               placeholder="رقم الطلب أو اسم الوكيل...">
                    </div>

                    <!-- Status Filter -->
                    <div class="hozi-filter-group">
                        <label for="status_filter">📊 الحالة:</label>
                        <select id="status_filter" name="status_filter">
                            <option value="">جميع الحالات</option>
                            <option value="" <?php selected($status_filter, ''); ?>>⏳ بانتظار المتابعة</option>
                            <?php foreach ($statuses as $status_key => $status_info): ?>
                                <option value="<?php echo esc_attr($status_key); ?>" <?php selected($status_filter, $status_key); ?>>
                                    <?php echo esc_html($status_info['icon'] . ' ' . $status_info['label']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Agent Filter -->
                    <div class="hozi-filter-group">
                        <label for="agent_filter">👤 الوكيل:</label>
                        <select id="agent_filter" name="agent_filter">
                            <option value="">جميع الوكلاء</option>
                            <?php foreach ($all_agents as $agent): ?>
                                <option value="<?php echo esc_attr($agent->user_id); ?>" <?php selected($agent_filter, $agent->user_id); ?>>
                                    <?php echo esc_html($agent->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Date From -->
                    <div class="hozi-filter-group">
                        <label for="date_from">📅 من تاريخ:</label>
                        <input type="date" id="date_from" name="date_from" value="<?php echo esc_attr($date_from); ?>">
                    </div>

                    <!-- Date To -->
                    <div class="hozi-filter-group">
                        <label for="date_to">📅 إلى تاريخ:</label>
                        <input type="date" id="date_to" name="date_to" value="<?php echo esc_attr($date_to); ?>">
                    </div>

                    <!-- Per Page -->
                    <div class="hozi-filter-group">
                        <label for="per_page">📄 عدد النتائج:</label>
                        <select id="per_page" name="per_page">
                            <option value="10" <?php selected($per_page, 10); ?>>10</option>
                            <option value="20" <?php selected($per_page, 20); ?>>20</option>
                            <option value="50" <?php selected($per_page, 50); ?>>50</option>
                            <option value="100" <?php selected($per_page, 100); ?>>100</option>
                        </select>
                    </div>
                </div>

                <div class="hozi-filters-actions">
                    <button type="submit" class="button button-primary">
                        <span class="dashicons dashicons-search"></span>
                        تطبيق الفلاتر
                    </button>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-tracking'); ?>" class="button">
                        <span class="dashicons dashicons-dismiss"></span>
                        مسح الفلاتر
                    </a>
                    <button type="button" class="button button-secondary" id="export-results">
                        <span class="dashicons dashicons-download"></span>
                        تصدير النتائج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Results -->
    <div class="hozi-tracking-orders">
        <div class="hozi-orders-header">
            <h2>📋 الطلبيات المؤكدة من جميع الوكلاء</h2>
            <div class="hozi-results-info">
                <span class="hozi-results-count">
                    عرض <?php echo ($offset + 1); ?>-<?php echo min($offset + $per_page, $total_orders); ?> من <?php echo $total_orders; ?> طلب
                    <?php if ($status_filter || $agent_filter || $date_from || $date_to || $search_query): ?>
                        <span class="hozi-filtered">(مفلتر)</span>
                    <?php endif; ?>
                </span>
                <button type="button" class="button" onclick="location.reload()">
                    <span class="dashicons dashicons-update"></span>
                    تحديث
                </button>
            </div>
        </div>
        
        <?php if (!empty($confirmed_orders)) : ?>
            <div class="hozi-orders-table-container">
                <table class="hozi-orders-table">
                    <thead>
                        <tr>
                            <th>📋 الطلب</th>
                            <th>👤 العميل</th>
                            <th>🏠 العنوان</th>
                            <th>💰 المبلغ</th>
                            <th>👨‍💼 الوكيل</th>
                            <th>📊 الحالة</th>
                            <th>📅 آخر تحديث</th>
                            <th>⚡ إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($confirmed_orders as $order_data) :
                            $order = wc_get_order($order_data->order_id);
                            if (!$order) continue;

                            // Get status info
                            $status_info = null;
                            $status_class = 'pending';
                            $status_text = '⏳ بانتظار المتابعة';

                            if ($order_data->tracking_status && isset($statuses[$order_data->tracking_status])) {
                                $status_info = $statuses[$order_data->tracking_status];
                                $status_class = $order_data->tracking_status;
                                $status_text = $status_info['icon'] . ' ' . $status_info['label'];
                            }
                        ?>
                            <tr class="hozi-order-row" data-order-id="<?php echo esc_attr($order_data->order_id); ?>">
                                <td class="hozi-order-info">
                                    <div class="hozi-order-number">
                                        <a href="<?php echo admin_url('post.php?post=' . $order_data->order_id . '&action=edit'); ?>" target="_blank">
                                            <strong>#<?php echo esc_html($order_data->order_id); ?></strong>
                                        </a>
                                    </div>
                                    <div class="hozi-order-date">
                                        <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->confirmed_at))); ?>
                                    </div>
                                </td>

                                <td class="hozi-customer-info">
                                    <div class="hozi-customer-name">
                                        <strong><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></strong>
                                    </div>
                                    <div class="hozi-customer-phone">
                                        <a href="tel:<?php echo esc_attr($order->get_billing_phone()); ?>">
                                            📞 <?php echo esc_html($order->get_billing_phone()); ?>
                                        </a>
                                    </div>
                                </td>

                                <td class="hozi-address-info">
                                    <div class="hozi-address-text">
                                        <?php
                                        $address = $order->get_billing_address_1();
                                        if ($order->get_billing_address_2()) {
                                            $address .= ', ' . $order->get_billing_address_2();
                                        }
                                        if ($order->get_billing_city()) {
                                            $address .= ', ' . $order->get_billing_city();
                                        }
                                        echo esc_html(wp_trim_words($address, 8));
                                        ?>
                                    </div>
                                </td>

                                <td class="hozi-total-info">
                                    <div class="hozi-total-amount">
                                        <strong><?php echo $order->get_formatted_order_total(); ?></strong>
                                    </div>
                                    <div class="hozi-items-count">
                                        <?php echo $order->get_item_count(); ?> منتج
                                    </div>
                                </td>

                                <td class="hozi-agent-info">
                                    <div class="hozi-agent-name">
                                        <strong><?php echo esc_html($order_data->agent_name); ?></strong>
                                    </div>
                                </td>

                                <td class="hozi-status-info">
                                    <span class="hozi-status-badge hozi-status-<?php echo esc_attr($status_class); ?>">
                                        <?php echo $status_text; ?>
                                    </span>
                                    <?php if ($order_data->reason_category): ?>
                                        <div class="hozi-reason-category">
                                            <?php echo esc_html($order_data->reason_category); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>

                                <td class="hozi-update-info">
                                    <?php if ($order_data->last_update): ?>
                                        <div class="hozi-last-update">
                                            <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->last_update))); ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="hozi-no-update">لم يتم التحديث</span>
                                    <?php endif; ?>
                                </td>

                                <td class="hozi-actions-info">
                                    <div class="hozi-action-buttons">
                                        <button type="button" class="button button-small hozi-view-order"
                                                data-order-id="<?php echo esc_attr($order_data->order_id); ?>"
                                                title="عرض تفاصيل الطلب">
                                            👁️
                                        </button>
                                        <button type="button" class="button button-small button-primary hozi-update-status"
                                                data-order-id="<?php echo esc_attr($order_data->order_id); ?>"
                                                title="تحديث الحالة">
                                            ✏️
                                        </button>
                                        <?php if ($order_data->tracking_notes): ?>
                                            <button type="button" class="button button-small hozi-view-notes"
                                                    data-notes="<?php echo esc_attr($order_data->tracking_notes); ?>"
                                                    title="عرض الملاحظات">
                                                📝
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="hozi-pagination">
                    <?php
                    $base_url = admin_url('admin.php?page=hozi-akadly-tracking');
                    $query_params = array();

                    if ($status_filter) $query_params['status_filter'] = $status_filter;
                    if ($agent_filter) $query_params['agent_filter'] = $agent_filter;
                    if ($date_from) $query_params['date_from'] = $date_from;
                    if ($date_to) $query_params['date_to'] = $date_to;
                    if ($search_query) $query_params['search_query'] = $search_query;
                    if ($per_page != 20) $query_params['per_page'] = $per_page;

                    // Previous page
                    if ($current_page > 1):
                        $prev_params = array_merge($query_params, array('paged' => $current_page - 1));
                        $prev_url = add_query_arg($prev_params, $base_url);
                    ?>
                        <a href="<?php echo esc_url($prev_url); ?>" class="button">« السابق</a>
                    <?php endif; ?>

                    <!-- Page numbers -->
                    <span class="hozi-page-info">
                        صفحة <?php echo $current_page; ?> من <?php echo $total_pages; ?>
                    </span>

                    <!-- Next page -->
                    <?php if ($current_page < $total_pages):
                        $next_params = array_merge($query_params, array('paged' => $current_page + 1));
                        $next_url = add_query_arg($next_params, $base_url);
                    ?>
                        <a href="<?php echo esc_url($next_url); ?>" class="button">التالي »</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php else : ?>
            <div class="hozi-no-orders">
                <div class="hozi-no-orders-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <h3><?php _e('لا توجد طلبيات مؤكدة', 'hozi-akadly'); ?></h3>
                <p><?php _e('لا توجد طلبيات مؤكدة تطابق معايير البحث المحددة.', 'hozi-akadly'); ?></p>
                <?php if ($status_filter || $agent_filter || $date_from || $date_to || $search_query): ?>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-tracking'); ?>" class="button button-primary">
                        مسح الفلاتر وعرض جميع الطلبيات
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Detailed Update Modal -->
<div id="hozi-tracking-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content">
        <div class="hozi-modal-header">
            <h2><?php _e('تحديث حالة الطلب', 'hozi-akadly'); ?></h2>
            <button type="button" class="hozi-modal-close">&times;</button>
        </div>
        
        <form method="post" class="hozi-tracking-form">
            <?php wp_nonce_field('hozi_tracking_update'); ?>
            <input type="hidden" name="update_tracking_status" value="1">
            <input type="hidden" name="order_id" id="modal_order_id" value="">
            
            <div class="hozi-form-row">
                <label for="status"><?php _e('الحالة الجديدة:', 'hozi-akadly'); ?></label>
                <select name="status" id="status" required>
                    <option value=""><?php _e('اختر الحالة', 'hozi-akadly'); ?></option>
                    <?php foreach ($statuses as $status_key => $status_info) : ?>
                        <option value="<?php echo esc_attr($status_key); ?>" data-type="<?php echo esc_attr($status_info['type']); ?>">
                            <?php echo esc_html($status_info['label']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="hozi-form-row" id="reason_category_row" style="display: none;">
                <label for="reason_category"><?php _e('سبب الحالة:', 'hozi-akadly'); ?></label>
                <select name="reason_category" id="reason_category">
                    <option value=""><?php _e('اختر السبب', 'hozi-akadly'); ?></option>
                </select>
            </div>

            <div class="hozi-form-row" id="reason_details_row" style="display: none;">
                <label for="reason_details"><?php _e('تفاصيل السبب:', 'hozi-akadly'); ?></label>
                <textarea name="reason_details" id="reason_details" rows="3" placeholder="<?php _e('أضف تفاصيل إضافية عن السبب...', 'hozi-akadly'); ?>"></textarea>
            </div>

            <div class="hozi-form-row">
                <label for="notes"><?php _e('ملاحظات إضافية:', 'hozi-akadly'); ?></label>
                <textarea name="notes" id="notes" rows="3" placeholder="<?php _e('أضف أي ملاحظات حول تحديث الحالة...', 'hozi-akadly'); ?>"></textarea>
            </div>

            <div class="hozi-form-actions">
                <button type="submit" class="button button-primary">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('تحديث الحالة', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-modal-close">
                    <?php _e('إلغاء', 'hozi-akadly'); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Filters Section */
.hozi-filters-section {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.hozi-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e5e9;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.hozi-filters-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.hozi-filters-content {
    padding: 20px;
}

.hozi-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.hozi-filter-group {
    display: flex;
    flex-direction: column;
}

.hozi-filter-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
    font-size: 14px;
}

.hozi-filter-group input,
.hozi-filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.hozi-filters-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e1e5e9;
}

/* Orders Table */
.hozi-orders-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border: 1px solid #e1e5e9;
}

.hozi-orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.hozi-orders-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 8px;
    text-align: right;
    font-weight: 600;
    font-size: 13px;
    border-bottom: 2px solid #5a67d8;
}

.hozi-orders-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
}

.hozi-order-row:hover {
    background: #f8f9fa;
}

.hozi-order-row:nth-child(even) {
    background: #fafbfc;
}

.hozi-order-row:nth-child(even):hover {
    background: #f0f2f5;
}

/* Table Cell Styles */
.hozi-order-number a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.hozi-order-number a:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.hozi-order-date {
    color: #666;
    font-size: 12px;
    margin-top: 2px;
}

.hozi-customer-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.hozi-customer-phone a {
    color: #28a745;
    text-decoration: none;
    font-size: 13px;
}

.hozi-customer-phone a:hover {
    text-decoration: underline;
}

.hozi-address-text {
    color: #666;
    font-size: 13px;
    line-height: 1.3;
}

.hozi-total-amount {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.hozi-items-count {
    color: #666;
    font-size: 12px;
}

.hozi-agent-name {
    font-weight: 600;
    color: #333;
}

/* Status Badges */
.hozi-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 80px;
}

.hozi-status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.hozi-status-delivered {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.hozi-status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hozi-status-postponed {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.hozi-status-exchange {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.hozi-reason-category {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
    font-style: italic;
}

.hozi-last-update {
    color: #666;
    font-size: 12px;
}

.hozi-no-update {
    color: #999;
    font-size: 12px;
    font-style: italic;
}

/* Action Buttons */
.hozi-action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.hozi-action-buttons .button {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1;
    min-height: auto;
    height: auto;
}

/* Pagination */
.hozi-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.hozi-page-info {
    font-weight: 600;
    color: #333;
    margin: 0 15px;
}

/* Results Info */
.hozi-orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.hozi-results-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.hozi-results-count {
    color: #666;
    font-size: 14px;
}

.hozi-filtered {
    color: #667eea;
    font-weight: 600;
}

.hozi-tracking-wrap {
    background: #f8f9fa;
    margin: 0 0 0 -20px;
    padding: 20px;
    min-height: 100vh;
}

.hozi-tracking-stats {
    margin-bottom: 30px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.hozi-stat-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #0073aa;
}

.hozi-stat-card.total {
    border-left-color: #0073aa;
}

.hozi-stat-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.hozi-stat-content h3 {
    font-size: 2em;
    margin: 0 0 5px 0;
    color: #333;
}

.hozi-stat-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.hozi-percentage {
    display: block;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.hozi-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.hozi-tracking-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.hozi-tracking-header h3 {
    margin: 0;
    color: #0073aa;
}

.hozi-order-date {
    color: #666;
    font-size: 14px;
}

.hozi-order-info {
    margin-bottom: 15px;
}

.hozi-customer-details {
    margin-bottom: 10px;
}

.hozi-order-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.hozi-total {
    font-weight: bold;
    color: #0073aa;
    font-size: 16px;
}

.hozi-quick-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-quick-status {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    font-size: 13px;
}

.hozi-detailed-update {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    font-size: 13px;
    background: #0073aa !important;
    color: white !important;
    border-color: #0073aa !important;
}

.hozi-no-orders {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.hozi-no-orders-icon {
    font-size: 48px;
    color: #4CAF50;
    margin-bottom: 20px;
}

.hozi-no-orders h3 {
    color: #4CAF50;
    margin-bottom: 10px;
}

.hozi-no-orders p {
    color: #666;
}

/* Modal Styles */
.hozi-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hozi-modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.hozi-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.hozi-modal-header h2 {
    margin: 0;
    color: #333;
}

.hozi-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.hozi-tracking-form {
    padding: 20px;
}

.hozi-form-row {
    margin-bottom: 15px;
}

.hozi-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.hozi-form-row select,
.hozi-form-row textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.hozi-form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hozi-filters-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .hozi-orders-table {
        font-size: 13px;
    }

    .hozi-orders-table th,
    .hozi-orders-table td {
        padding: 8px 6px;
    }
}

@media (max-width: 768px) {
    .hozi-filters-grid {
        grid-template-columns: 1fr;
    }

    .hozi-orders-table-container {
        overflow-x: auto;
    }

    .hozi-orders-table {
        min-width: 800px;
    }

    .hozi-orders-header {
        flex-direction: column;
        align-items: stretch;
    }

    .hozi-results-info {
        justify-content: space-between;
    }

    .hozi-filters-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .hozi-filters-actions .button {
        justify-content: center;
    }

    .hozi-orders-grid {
        grid-template-columns: 1fr;
    }

    .hozi-quick-actions {
        flex-direction: column;
    }

    .hozi-modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* No Orders State */
.hozi-no-orders {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.hozi-no-orders-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.hozi-no-orders h3 {
    color: #333;
    margin-bottom: 10px;
}

.hozi-no-orders p {
    color: #666;
    margin-bottom: 20px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Reason categories data
    const reasonCategories = <?php echo json_encode($reason_categories); ?>;

    // Toggle filters
    $('#toggle-filters').on('click', function() {
        const $content = $('.hozi-filters-content');
        const $button = $(this);

        if ($content.is(':visible')) {
            $content.slideUp();
            $button.html('<span class="dashicons dashicons-filter"></span> إظهار الفلاتر');
        } else {
            $content.slideDown();
            $button.html('<span class="dashicons dashicons-filter"></span> إخفاء الفلاتر');
        }
    });

    // View order details
    $('.hozi-view-order').on('click', function() {
        const orderId = $(this).data('order-id');
        const url = '<?php echo admin_url('post.php'); ?>?post=' + orderId + '&action=edit';
        window.open(url, '_blank');
    });

    // Update status
    $('.hozi-update-status').on('click', function() {
        const orderId = $(this).data('order-id');
        $('#modal_order_id').val(orderId);
        $('#hozi-tracking-modal').show();
    });

    // View notes
    $('.hozi-view-notes').on('click', function() {
        const notes = $(this).data('notes');
        alert('الملاحظات:\n\n' + notes);
    });

    // Export results
    $('#export-results').on('click', function() {
        const currentUrl = window.location.href;
        const exportUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'export=csv';
        window.location.href = exportUrl;
    });
    
    // Quick status update
    $('.hozi-quick-status').on('click', function() {
        const status = $(this).data('status');
        const orderId = $(this).data('order');
        
        if (confirm('هل أنت متأكد من تحديث حالة هذا الطلب؟')) {
            updateOrderStatus(orderId, status);
        }
    });
    
    // Detailed update modal
    $('.hozi-detailed-update').on('click', function() {
        const orderId = $(this).data('order');
        $('#modal_order_id').val(orderId);
        $('#hozi-tracking-modal').show();
    });
    
    // Close modal
    $('.hozi-modal-close').on('click', function() {
        $('#hozi-tracking-modal').hide();
        resetForm();
    });
    
    // Status change handler
    $('#status').on('change', function() {
        const status = $(this).val();
        const $reasonRow = $('#reason_category_row');
        const $reasonSelect = $('#reason_category');
        const $detailsRow = $('#reason_details_row');
        
        if (status && reasonCategories[status]) {
            // Populate reason categories
            $reasonSelect.empty().append('<option value="">اختر السبب</option>');
            
            $.each(reasonCategories[status], function(key, value) {
                $reasonSelect.append(`<option value="${key}">${value}</option>`);
            });
            
            $reasonRow.show();
        } else {
            $reasonRow.hide();
            $detailsRow.hide();
        }
    });
    
    // Reason category change handler
    $('#reason_category').on('change', function() {
        const $detailsRow = $('#reason_details_row');
        
        if ($(this).val() === 'other') {
            $detailsRow.show();
        } else {
            $detailsRow.hide();
        }
    });
    
    // Quick status update function
    function updateOrderStatus(orderId, status) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_quick_tracking_update',
                order_id: orderId,
                status: status,
                nonce: '<?php echo wp_create_nonce("hozi_quick_tracking"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('فشل في تحديث الحالة: ' + response.data.message);
                }
            },
            error: function() {
                alert('حدث خطأ أثناء تحديث الحالة');
            }
        });
    }
    
    // Reset form
    function resetForm() {
        $('#status').val('');
        $('#reason_category').val('');
        $('#reason_details').val('');
        $('#notes').val('');
        $('#reason_category_row').hide();
        $('#reason_details_row').hide();
    }
    
    // Close modal on outside click
    $('#hozi-tracking-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
            resetForm();
        }
    });
});
</script>
