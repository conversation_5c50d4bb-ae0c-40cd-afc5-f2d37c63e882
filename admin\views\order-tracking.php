<?php
/**
 * Order Tracking Management View
 */

if (!defined('ABSPATH')) {
    exit;
}

// Initialize order tracker
$order_tracker = new Hozi_Akadly_Order_Tracker();

// Handle form submissions
if (isset($_POST['update_tracking_status']) && wp_verify_nonce($_POST['_wpnonce'], 'hozi_tracking_update')) {
    $order_id = intval($_POST['order_id']);
    $status = sanitize_text_field($_POST['status']);
    $reason_category = sanitize_text_field($_POST['reason_category']);
    $reason_details = sanitize_textarea_field($_POST['reason_details']);
    $notes = sanitize_textarea_field($_POST['notes']);

    $result = $order_tracker->update_tracking_status($order_id, $status, $reason_category, $reason_details, $notes);
    
    if ($result) {
        echo '<div class="notice notice-success"><p>' . __('تم تحديث حالة الطلب بنجاح', 'hozi-akadly') . '</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>' . __('فشل في تحديث حالة الطلب', 'hozi-akadly') . '</p></div>';
    }
}

// Get filter parameters
$status_filter = isset($_GET['status_filter']) ? sanitize_text_field($_GET['status_filter']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';

// Get orders for tracking
$orders_for_tracking = $order_tracker->get_orders_for_tracking(20);

// Get tracking statistics
$tracking_stats = $order_tracker->get_tracking_statistics();
$statuses = Hozi_Akadly_Order_Tracker::get_tracking_statuses();
$reason_categories = Hozi_Akadly_Order_Tracker::get_reason_categories();
?>

<div class="wrap hozi-tracking-wrap">
    <h1><?php _e('تتبع الطلبيات - أكدلي', 'hozi-akadly'); ?></h1>

    <!-- Statistics Cards -->
    <div class="hozi-tracking-stats">
        <h2><?php _e('إحصائيات التتبع', 'hozi-akadly'); ?></h2>
        <div class="hozi-stats-grid">
            <div class="hozi-stat-card total">
                <div class="hozi-stat-icon">📊</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats['total']); ?></h3>
                    <p><?php _e('إجمالي الطلبيات المتتبعة', 'hozi-akadly'); ?></p>
                </div>
            </div>

            <?php foreach ($tracking_stats['stats'] as $stat) : ?>
                <div class="hozi-stat-card" style="border-left-color: <?php echo esc_attr($stat['color']); ?>">
                    <div class="hozi-stat-content">
                        <h3><?php echo esc_html($stat['count']); ?></h3>
                        <p><?php echo esc_html($stat['label']); ?></p>
                        <span class="hozi-percentage"><?php echo esc_html($stat['percentage']); ?>%</span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Orders Requiring Tracking -->
    <div class="hozi-tracking-orders">
        <h2><?php _e('الطلبيات المؤكدة التي تحتاج متابعة', 'hozi-akadly'); ?></h2>
        
        <?php if (!empty($orders_for_tracking)) : ?>
            <div class="hozi-orders-grid">
                <?php foreach ($orders_for_tracking as $order_data) : 
                    $order = wc_get_order($order_data->order_id);
                    if (!$order) continue;
                ?>
                    <div class="hozi-tracking-card" data-order-id="<?php echo esc_attr($order_data->order_id); ?>">
                        <div class="hozi-tracking-header">
                            <h3>
                                <?php _e('طلب رقم:', 'hozi-akadly'); ?> 
                                <a href="<?php echo admin_url('post.php?post=' . $order_data->order_id . '&action=edit'); ?>">
                                    #<?php echo esc_html($order_data->order_id); ?>
                                </a>
                            </h3>
                            <span class="hozi-order-date">
                                <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->order_date))); ?>
                            </span>
                        </div>

                        <div class="hozi-order-info">
                            <div class="hozi-customer-details">
                                <strong><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></strong>
                                <br>
                                <a href="tel:<?php echo esc_attr($order->get_billing_phone()); ?>">
                                    <?php echo esc_html($order->get_billing_phone()); ?>
                                </a>
                            </div>
                            <div class="hozi-order-details">
                                <span class="hozi-total"><?php echo $order->get_formatted_order_total(); ?></span>
                                <span class="hozi-agent"><?php _e('الوكيل:', 'hozi-akadly'); ?> <?php echo esc_html($order_data->agent_name); ?></span>
                                <span class="hozi-confirmed"><?php _e('تم التأكيد:', 'hozi-akadly'); ?> <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->confirmed_at))); ?></span>
                            </div>
                        </div>

                        <!-- Quick Status Update -->
                        <div class="hozi-quick-actions">
                            <button type="button" class="button hozi-quick-status" data-status="delivered" data-order="<?php echo esc_attr($order_data->order_id); ?>">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('تم التوصيل', 'hozi-akadly'); ?>
                            </button>
                            <button type="button" class="button hozi-quick-status" data-status="postponed_customer" data-order="<?php echo esc_attr($order_data->order_id); ?>">
                                <span class="dashicons dashicons-clock"></span>
                                <?php _e('مؤجلة', 'hozi-akadly'); ?>
                            </button>
                            <button type="button" class="button hozi-detailed-update" data-order="<?php echo esc_attr($order_data->order_id); ?>">
                                <span class="dashicons dashicons-edit"></span>
                                <?php _e('تحديث مفصل', 'hozi-akadly'); ?>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else : ?>
            <div class="hozi-no-orders">
                <div class="hozi-no-orders-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <h3><?php _e('ممتاز! جميع الطلبيات المؤكدة تم تتبعها', 'hozi-akadly'); ?></h3>
                <p><?php _e('لا توجد طلبيات مؤكدة تحتاج إلى تحديث حالة التتبع', 'hozi-akadly'); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Detailed Update Modal -->
<div id="hozi-tracking-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content">
        <div class="hozi-modal-header">
            <h2><?php _e('تحديث حالة الطلب', 'hozi-akadly'); ?></h2>
            <button type="button" class="hozi-modal-close">&times;</button>
        </div>
        
        <form method="post" class="hozi-tracking-form">
            <?php wp_nonce_field('hozi_tracking_update'); ?>
            <input type="hidden" name="update_tracking_status" value="1">
            <input type="hidden" name="order_id" id="modal_order_id" value="">
            
            <div class="hozi-form-row">
                <label for="status"><?php _e('الحالة الجديدة:', 'hozi-akadly'); ?></label>
                <select name="status" id="status" required>
                    <option value=""><?php _e('اختر الحالة', 'hozi-akadly'); ?></option>
                    <?php foreach ($statuses as $status_key => $status_info) : ?>
                        <option value="<?php echo esc_attr($status_key); ?>" data-type="<?php echo esc_attr($status_info['type']); ?>">
                            <?php echo esc_html($status_info['label']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="hozi-form-row" id="reason_category_row" style="display: none;">
                <label for="reason_category"><?php _e('سبب الحالة:', 'hozi-akadly'); ?></label>
                <select name="reason_category" id="reason_category">
                    <option value=""><?php _e('اختر السبب', 'hozi-akadly'); ?></option>
                </select>
            </div>

            <div class="hozi-form-row" id="reason_details_row" style="display: none;">
                <label for="reason_details"><?php _e('تفاصيل السبب:', 'hozi-akadly'); ?></label>
                <textarea name="reason_details" id="reason_details" rows="3" placeholder="<?php _e('أضف تفاصيل إضافية عن السبب...', 'hozi-akadly'); ?>"></textarea>
            </div>

            <div class="hozi-form-row">
                <label for="notes"><?php _e('ملاحظات إضافية:', 'hozi-akadly'); ?></label>
                <textarea name="notes" id="notes" rows="3" placeholder="<?php _e('أضف أي ملاحظات حول تحديث الحالة...', 'hozi-akadly'); ?>"></textarea>
            </div>

            <div class="hozi-form-actions">
                <button type="submit" class="button button-primary">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('تحديث الحالة', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-modal-close">
                    <?php _e('إلغاء', 'hozi-akadly'); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.hozi-tracking-wrap {
    background: #f8f9fa;
    margin: 0 0 0 -20px;
    padding: 20px;
    min-height: 100vh;
}

.hozi-tracking-stats {
    margin-bottom: 30px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.hozi-stat-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #0073aa;
}

.hozi-stat-card.total {
    border-left-color: #0073aa;
}

.hozi-stat-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.hozi-stat-content h3 {
    font-size: 2em;
    margin: 0 0 5px 0;
    color: #333;
}

.hozi-stat-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.hozi-percentage {
    display: block;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.hozi-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.hozi-tracking-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.hozi-tracking-header h3 {
    margin: 0;
    color: #0073aa;
}

.hozi-order-date {
    color: #666;
    font-size: 14px;
}

.hozi-order-info {
    margin-bottom: 15px;
}

.hozi-customer-details {
    margin-bottom: 10px;
}

.hozi-order-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.hozi-total {
    font-weight: bold;
    color: #0073aa;
    font-size: 16px;
}

.hozi-quick-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-quick-status {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    font-size: 13px;
}

.hozi-detailed-update {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    font-size: 13px;
    background: #0073aa !important;
    color: white !important;
    border-color: #0073aa !important;
}

.hozi-no-orders {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.hozi-no-orders-icon {
    font-size: 48px;
    color: #4CAF50;
    margin-bottom: 20px;
}

.hozi-no-orders h3 {
    color: #4CAF50;
    margin-bottom: 10px;
}

.hozi-no-orders p {
    color: #666;
}

/* Modal Styles */
.hozi-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hozi-modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.hozi-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.hozi-modal-header h2 {
    margin: 0;
    color: #333;
}

.hozi-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.hozi-tracking-form {
    padding: 20px;
}

.hozi-form-row {
    margin-bottom: 15px;
}

.hozi-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.hozi-form-row select,
.hozi-form-row textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.hozi-form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

@media (max-width: 768px) {
    .hozi-orders-grid {
        grid-template-columns: 1fr;
    }
    
    .hozi-quick-actions {
        flex-direction: column;
    }
    
    .hozi-modal-content {
        width: 95%;
        margin: 20px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Reason categories data
    const reasonCategories = <?php echo json_encode($reason_categories); ?>;
    
    // Quick status update
    $('.hozi-quick-status').on('click', function() {
        const status = $(this).data('status');
        const orderId = $(this).data('order');
        
        if (confirm('هل أنت متأكد من تحديث حالة هذا الطلب؟')) {
            updateOrderStatus(orderId, status);
        }
    });
    
    // Detailed update modal
    $('.hozi-detailed-update').on('click', function() {
        const orderId = $(this).data('order');
        $('#modal_order_id').val(orderId);
        $('#hozi-tracking-modal').show();
    });
    
    // Close modal
    $('.hozi-modal-close').on('click', function() {
        $('#hozi-tracking-modal').hide();
        resetForm();
    });
    
    // Status change handler
    $('#status').on('change', function() {
        const status = $(this).val();
        const $reasonRow = $('#reason_category_row');
        const $reasonSelect = $('#reason_category');
        const $detailsRow = $('#reason_details_row');
        
        if (status && reasonCategories[status]) {
            // Populate reason categories
            $reasonSelect.empty().append('<option value="">اختر السبب</option>');
            
            $.each(reasonCategories[status], function(key, value) {
                $reasonSelect.append(`<option value="${key}">${value}</option>`);
            });
            
            $reasonRow.show();
        } else {
            $reasonRow.hide();
            $detailsRow.hide();
        }
    });
    
    // Reason category change handler
    $('#reason_category').on('change', function() {
        const $detailsRow = $('#reason_details_row');
        
        if ($(this).val() === 'other') {
            $detailsRow.show();
        } else {
            $detailsRow.hide();
        }
    });
    
    // Quick status update function
    function updateOrderStatus(orderId, status) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_quick_tracking_update',
                order_id: orderId,
                status: status,
                nonce: '<?php echo wp_create_nonce("hozi_quick_tracking"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('فشل في تحديث الحالة: ' + response.data.message);
                }
            },
            error: function() {
                alert('حدث خطأ أثناء تحديث الحالة');
            }
        });
    }
    
    // Reset form
    function resetForm() {
        $('#status').val('');
        $('#reason_category').val('');
        $('#reason_details').val('');
        $('#notes').val('');
        $('#reason_category_row').hide();
        $('#reason_details_row').hide();
    }
    
    // Close modal on outside click
    $('#hozi-tracking-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
            resetForm();
        }
    });
});
</script>
