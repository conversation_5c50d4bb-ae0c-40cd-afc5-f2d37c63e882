<?php
/**
 * Customer Orders Management
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Customer_Orders {

    public function __construct() {
        // Only initialize if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            return;
        }

        try {
            // Add custom order statuses to customer view
            add_filter('woocommerce_my_account_my_orders_query', array($this, 'include_custom_statuses'));

            // Add confirmation status column to customer orders
            add_filter('woocommerce_my_account_my_orders_columns', array($this, 'add_confirmation_column'));
            add_action('woocommerce_my_account_my_orders_column_confirmation-status', array($this, 'show_confirmation_status'));

            // Add order notes for confirmation status
            add_action('woocommerce_order_status_changed', array($this, 'add_confirmation_note'), 10, 4);

            // Remove the problematic endpoint - moved to admin area
            // add_action('init', array($this, 'add_my_account_endpoint'));
            // add_filter('woocommerce_account_menu_items', array($this, 'add_my_account_menu_item'));
            // add_action('woocommerce_account_order-confirmation_endpoint', array($this, 'order_confirmation_content'));

            // Add CSS for confirmation status
            add_action('wp_head', array($this, 'add_confirmation_status_css'));

            // Add shortcode for filtered orders
            add_shortcode('hozi_customer_orders', array($this, 'customer_orders_shortcode'));
        } catch (Exception $e) {
            // Silently handle initialization errors
        }
    }

    /**
     * Include custom order statuses in customer orders query
     */
    public function include_custom_statuses($args) {
        // Include all order statuses that customers should see
        $customer_statuses = array(
            'pending',
            'processing',
            'on-hold',
            'completed',
            'cancelled',
            'refunded',
            'failed'
        );

        $args['post_status'] = array_map(function($status) {
            return 'wc-' . $status;
        }, $customer_statuses);

        return $args;
    }

    /**
     * Add confirmation status column to customer orders table
     */
    public function add_confirmation_column($columns) {
        $new_columns = array();

        foreach ($columns as $key => $column) {
            $new_columns[$key] = $column;

            if ($key === 'order-status') {
                $new_columns['confirmation-status'] = __('حالة التأكيد', 'hozi-akadly');
            }
        }

        return $new_columns;
    }

    /**
     * Show confirmation status in customer orders table
     */
    public function show_confirmation_status($order) {
        try {
            $assignment = $this->get_order_assignment($order->get_id());

            if ($assignment) {
                $status_labels = array(
                    'pending_confirmation' => array('label' => 'في انتظار التأكيد', 'class' => 'pending'),
                    'confirmed' => array('label' => 'تم التأكيد', 'class' => 'confirmed'),
                    'rejected' => array('label' => 'تم الرفض', 'class' => 'rejected'),
                    'no_answer' => array('label' => 'لم يرد', 'class' => 'no-answer'),
                    'callback_later' => array('label' => 'إعادة اتصال', 'class' => 'callback')
                );

                $status_info = $status_labels[$assignment->confirmation_status] ?? array('label' => 'غير محدد', 'class' => 'unknown');

                echo '<span class="hozi-confirmation-status ' . esc_attr($status_info['class']) . '">';
                echo esc_html($status_info['label']);
                echo '</span>';

                if ($assignment->notes) {
                    echo '<br><small class="hozi-notes">' . esc_html($assignment->notes) . '</small>';
                }
            } else {
                echo '<span class="hozi-confirmation-status not-assigned">غير مخصص</span>';
            }
        } catch (Exception $e) {
            // Silently handle database errors
            echo '<span class="hozi-confirmation-status not-assigned">-</span>';
        }
    }

    /**
     * Add confirmation note when order status changes
     */
    public function add_confirmation_note($order_id, $old_status, $new_status, $order) {
        try {
            $assignment = $this->get_order_assignment($order_id);

            if ($assignment) {
                $status_messages = array(
                    'processing' => 'تم تأكيد طلبكم وسيتم تجهيزه قريباً',
                    'cancelled' => 'تم إلغاء الطلب بناءً على طلبكم',
                    'on-hold' => 'طلبكم في انتظار المراجعة'
                );

                if (isset($status_messages[$new_status])) {
                    $order->add_order_note($status_messages[$new_status], 1); // 1 = customer note
                }
            }
        } catch (Exception $e) {
            // Silently handle errors
        }
    }

    /**
     * Add CSS for confirmation status badges
     */
    public function add_confirmation_status_css() {
        if (is_account_page()) {
            ?>
            <style>
            .hozi-confirmation-status {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                display: inline-block;
                text-align: center;
                min-width: 80px;
            }

            .hozi-confirmation-status.pending {
                background: #ffc107;
                color: #000;
            }
            .hozi-confirmation-status.confirmed {
                background: #28a745;
                color: white;
            }
            .hozi-confirmation-status.rejected {
                background: #dc3545;
                color: white;
            }
            .hozi-confirmation-status.no-answer {
                background: #fd7e14;
                color: white;
            }
            .hozi-confirmation-status.callback {
                background: #17a2b8;
                color: white;
            }
            .hozi-confirmation-status.not-assigned {
                background: #6c757d;
                color: white;
            }

            .hozi-notes {
                color: #666;
                font-style: italic;
                font-size: 11px;
                margin-top: 4px;
                display: block;
            }
            </style>
            <?php
        }
    }

    /**
     * Add custom endpoint to My Account
     */
    public function add_my_account_endpoint() {
        add_rewrite_endpoint('order-confirmation', EP_ROOT | EP_PAGES);
    }

    /**
     * Add menu item to My Account
     */
    public function add_my_account_menu_item($items) {
        // Insert after orders
        $new_items = array();
        foreach ($items as $key => $item) {
            $new_items[$key] = $item;
            if ($key === 'orders') {
                $new_items['order-confirmation'] = __('حالة التأكيد', 'hozi-akadly');
            }
        }
        return $new_items;
    }

    /**
     * Content for order confirmation endpoint
     */
    public function order_confirmation_content() {
        $filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : 'all';

        // Map filter to status
        $status_map = array(
            'all' => 'all',
            'confirmed' => 'confirmed',
            'pending' => 'pending_confirmation',
            'cancelled' => 'rejected',
            'no-answer' => 'no_answer',
            'callback' => 'callback_later'
        );

        $status = $status_map[$filter] ?? 'all';

        $customer_id = get_current_user_id();

        // Pagination settings
        $per_page = 10;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;

        $orders = $this->get_customer_orders_with_confirmation($customer_id, $status, $per_page, $offset);
        $total_orders = $this->get_customer_orders_count($customer_id, $status);

        // Get counts for each status
        $counts = $this->get_customer_order_counts($customer_id);

        // Calculate pagination info
        $total_pages = ceil($total_orders / $per_page);
        $pagination_info = array(
            'current_page' => $current_page,
            'total_pages' => $total_pages,
            'per_page' => $per_page,
            'total_orders' => $total_orders,
            'showing_from' => $offset + 1,
            'showing_to' => min($offset + $per_page, $total_orders)
        );

        ?>
        <div class="hozi-order-confirmation-page">
            <h3>حالة تأكيد الطلبات</h3>

            <!-- Quick Summary -->
            <?php if ($counts->confirmed > 0 || $counts->rejected > 0 || $counts->callback > 0) : ?>
            <div class="hozi-quick-summary">
                <?php if ($counts->confirmed > 0) : ?>
                    <div class="summary-item confirmed">
                        <span class="summary-icon">✅</span>
                        <div class="summary-content">
                            <strong><?php echo $counts->confirmed; ?></strong>
                            <span>طلب تم تأكيده</span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($counts->rejected > 0) : ?>
                    <div class="summary-item rejected">
                        <span class="summary-icon">❌</span>
                        <div class="summary-content">
                            <strong><?php echo $counts->rejected; ?></strong>
                            <span>طلب تم إلغاؤه</span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($counts->callback > 0) : ?>
                    <div class="summary-item callback">
                        <span class="summary-icon">🔄</span>
                        <div class="summary-content">
                            <strong><?php echo $counts->callback; ?></strong>
                            <span>طلب تم تأجيله</span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Status Filter Buttons -->
            <div class="hozi-status-filters">
                <a href="<?php echo esc_url(wc_get_account_endpoint_url('order-confirmation')); ?>"
                   class="filter-btn <?php echo $filter === 'all' ? 'active' : ''; ?>">
                    جميع الطلبات (<?php echo $counts->total; ?>)
                </a>
                <a href="<?php echo esc_url(add_query_arg('filter', 'pending', wc_get_account_endpoint_url('order-confirmation'))); ?>"
                   class="filter-btn <?php echo $filter === 'pending' ? 'active' : ''; ?>">
                    في انتظار التأكيد (<?php echo $counts->pending; ?>)
                </a>
                <a href="<?php echo esc_url(add_query_arg('filter', 'confirmed', wc_get_account_endpoint_url('order-confirmation'))); ?>"
                   class="filter-btn confirmed <?php echo $filter === 'confirmed' ? 'active' : ''; ?>">
                    ✅ تم التأكيد (<?php echo $counts->confirmed; ?>)
                </a>
                <a href="<?php echo esc_url(add_query_arg('filter', 'cancelled', wc_get_account_endpoint_url('order-confirmation'))); ?>"
                   class="filter-btn cancelled <?php echo $filter === 'cancelled' ? 'active' : ''; ?>">
                    ❌ تم الإلغاء (<?php echo $counts->rejected; ?>)
                </a>
                <a href="<?php echo esc_url(add_query_arg('filter', 'callback', wc_get_account_endpoint_url('order-confirmation'))); ?>"
                   class="filter-btn callback <?php echo $filter === 'callback' ? 'active' : ''; ?>">
                    🔄 تم التأجيل (<?php echo $counts->callback; ?>)
                </a>
                <a href="<?php echo esc_url(add_query_arg('filter', 'no-answer', wc_get_account_endpoint_url('order-confirmation'))); ?>"
                   class="filter-btn no-answer <?php echo $filter === 'no-answer' ? 'active' : ''; ?>">
                    📞 لم يرد (<?php echo $counts->no_answer; ?>)
                </a>
            </div>

            <?php if (!empty($orders)) : ?>
                <div class="hozi-orders-table">
                    <table class="woocommerce-orders-table woocommerce-MyAccount-orders shop_table shop_table_responsive my_account_orders account-orders-table">
                        <thead>
                            <tr>
                                <th class="woocommerce-orders-table__header">الطلب</th>
                                <th class="woocommerce-orders-table__header">التاريخ</th>
                                <th class="woocommerce-orders-table__header">الحالة</th>
                                <th class="woocommerce-orders-table__header">حالة التأكيد</th>
                                <th class="woocommerce-orders-table__header">المجموع</th>
                                <th class="woocommerce-orders-table__header">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order_data) :
                                $order = wc_get_order($order_data->order_id);
                                if (!$order) continue;
                            ?>
                                <tr class="woocommerce-orders-table__row">
                                    <td class="woocommerce-orders-table__cell" data-title="الطلب">
                                        <a href="<?php echo esc_url($order->get_view_order_url()); ?>">
                                            #<?php echo $order->get_order_number(); ?>
                                        </a>
                                    </td>
                                    <td class="woocommerce-orders-table__cell" data-title="التاريخ">
                                        <time datetime="<?php echo esc_attr($order->get_date_created()->date('c')); ?>">
                                            <?php echo esc_html($order->get_date_created()->format('Y/m/d')); ?>
                                        </time>
                                    </td>
                                    <td class="woocommerce-orders-table__cell" data-title="الحالة">
                                        <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                                    </td>
                                    <td class="woocommerce-orders-table__cell" data-title="حالة التأكيد">
                                        <?php
                                        $status_labels = array(
                                            'pending_confirmation' => array('label' => 'في انتظار التأكيد', 'class' => 'pending', 'icon' => '⏳'),
                                            'confirmed' => array('label' => 'تم التأكيد ✅', 'class' => 'confirmed', 'icon' => '✅'),
                                            'rejected' => array('label' => 'تم الإلغاء ❌', 'class' => 'rejected', 'icon' => '❌'),
                                            'no_answer' => array('label' => 'لم يرد 📞', 'class' => 'no-answer', 'icon' => '📞'),
                                            'callback_later' => array('label' => 'تم التأجيل 🔄', 'class' => 'callback', 'icon' => '🔄')
                                        );

                                        $confirmation_status = $order_data->confirmation_status ?? 'pending_confirmation';
                                        $status_info = $status_labels[$confirmation_status] ?? array('label' => 'غير محدد', 'class' => 'unknown');
                                        ?>
                                        <span class="hozi-confirmation-status <?php echo esc_attr($status_info['class']); ?>">
                                            <?php echo esc_html($status_info['label']); ?>
                                        </span>

                                        <?php if ($order_data->confirmed_at && $confirmation_status !== 'pending_confirmation') : ?>
                                            <br><small class="hozi-date">
                                                <?php
                                                $action_text = '';
                                                switch($confirmation_status) {
                                                    case 'confirmed':
                                                        $action_text = 'تم التأكيد في:';
                                                        break;
                                                    case 'rejected':
                                                        $action_text = 'تم الإلغاء في:';
                                                        break;
                                                    case 'callback_later':
                                                        $action_text = 'تم التأجيل في:';
                                                        break;
                                                    case 'no_answer':
                                                        $action_text = 'آخر محاولة:';
                                                        break;
                                                }
                                                echo esc_html($action_text . ' ' . date('Y/m/d H:i', strtotime($order_data->confirmed_at)));
                                                ?>
                                            </small>
                                        <?php endif; ?>

                                        <?php if ($order_data->notes) : ?>
                                            <br><small class="hozi-notes">
                                                <strong>ملاحظة:</strong> <?php echo esc_html($order_data->notes); ?>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="woocommerce-orders-table__cell" data-title="المجموع">
                                        <?php echo $order->get_formatted_order_total(); ?>
                                    </td>
                                    <td class="woocommerce-orders-table__cell" data-title="الإجراءات">
                                        <a href="<?php echo esc_url($order->get_view_order_url()); ?>" class="woocommerce-button button view">
                                            عرض
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($pagination_info && $pagination_info['total_pages'] > 1) : ?>
                    <div class="hozi-pagination">
                        <div class="pagination-info">
                            <span>
                                عرض <?php echo $pagination_info['showing_from']; ?> - <?php echo $pagination_info['showing_to']; ?>
                                من <?php echo $pagination_info['total_orders']; ?> طلب
                            </span>
                        </div>

                        <div class="pagination-links">
                            <?php
                            $current_page = $pagination_info['current_page'];
                            $total_pages = $pagination_info['total_pages'];

                            // Build base URL
                            $base_url = wc_get_account_endpoint_url('order-confirmation');
                            $url_params = array();
                            if (isset($_GET['filter']) && $_GET['filter'] !== 'all') {
                                $url_params['filter'] = $_GET['filter'];
                            }

                            // Previous page
                            if ($current_page > 1) {
                                $prev_url = add_query_arg(array_merge($url_params, array('paged' => $current_page - 1)), $base_url);
                                echo '<a href="' . esc_url($prev_url) . '" class="pagination-link prev">« السابق</a>';
                            }

                            // Page numbers
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);

                            if ($start_page > 1) {
                                $first_url = add_query_arg($url_params, $base_url);
                                echo '<a href="' . esc_url($first_url) . '" class="pagination-link">1</a>';
                                if ($start_page > 2) {
                                    echo '<span class="pagination-dots">...</span>';
                                }
                            }

                            for ($i = $start_page; $i <= $end_page; $i++) {
                                if ($i == $current_page) {
                                    echo '<span class="pagination-link current">' . $i . '</span>';
                                } else {
                                    $page_url = add_query_arg(array_merge($url_params, array('paged' => $i)), $base_url);
                                    echo '<a href="' . esc_url($page_url) . '" class="pagination-link">' . $i . '</a>';
                                }
                            }

                            if ($end_page < $total_pages) {
                                if ($end_page < $total_pages - 1) {
                                    echo '<span class="pagination-dots">...</span>';
                                }
                                $last_url = add_query_arg(array_merge($url_params, array('paged' => $total_pages)), $base_url);
                                echo '<a href="' . esc_url($last_url) . '" class="pagination-link">' . $total_pages . '</a>';
                            }

                            // Next page
                            if ($current_page < $total_pages) {
                                $next_url = add_query_arg(array_merge($url_params, array('paged' => $current_page + 1)), $base_url);
                                echo '<a href="' . esc_url($next_url) . '" class="pagination-link next">التالي »</a>';
                            }
                            ?>
                        </div>
                    </div>
                <?php endif; ?>

            <?php else : ?>
                <div class="woocommerce-message woocommerce-message--info woocommerce-Message woocommerce-Message--info woocommerce-info">
                    <p>لا توجد طلبات تطابق الفلتر المحدد.</p>
                </div>
            <?php endif; ?>
        </div>

        <style>
        .hozi-quick-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .summary-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .summary-item:hover {
            transform: translateY(-2px);
        }

        .summary-item.confirmed {
            border-right: 4px solid #28a745;
        }

        .summary-item.rejected {
            border-right: 4px solid #dc3545;
        }

        .summary-item.callback {
            border-right: 4px solid #17a2b8;
        }

        .summary-icon {
            font-size: 24px;
            margin-left: 15px;
        }

        .summary-content strong {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .summary-content span {
            font-size: 14px;
            color: #666;
        }

        .hozi-status-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .filter-btn {
            padding: 8px 16px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #0073aa;
            color: white;
            border-color: #0073aa;
            text-decoration: none;
        }

        .filter-btn.confirmed:hover,
        .filter-btn.confirmed.active {
            background: #28a745;
            border-color: #28a745;
        }

        .filter-btn.cancelled:hover,
        .filter-btn.cancelled.active {
            background: #dc3545;
            border-color: #dc3545;
        }

        .filter-btn.callback:hover,
        .filter-btn.callback.active {
            background: #17a2b8;
            border-color: #17a2b8;
        }

        .filter-btn.no-answer:hover,
        .filter-btn.no-answer.active {
            background: #fd7e14;
            border-color: #fd7e14;
        }

        .hozi-confirmation-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
        }

        .hozi-confirmation-status.pending {
            background: #ffc107;
            color: #000;
        }
        .hozi-confirmation-status.confirmed {
            background: #28a745;
            color: white;
        }
        .hozi-confirmation-status.rejected {
            background: #dc3545;
            color: white;
        }
        .hozi-confirmation-status.no-answer {
            background: #fd7e14;
            color: white;
        }
        .hozi-confirmation-status.callback {
            background: #17a2b8;
            color: white;
        }
        .hozi-confirmation-status.unknown {
            background: #6c757d;
            color: white;
        }

        .hozi-notes {
            color: #666;
            font-style: italic;
            font-size: 11px;
            margin-top: 4px;
            display: block;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 3px;
            border-right: 3px solid #007cba;
        }

        .hozi-date {
            color: #555;
            font-size: 11px;
            margin-top: 4px;
            display: block;
            font-weight: bold;
        }

        /* Pagination Styles */
        .hozi-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #ddd;
            margin-top: 20px;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .pagination-links {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination-link {
            display: inline-block;
            padding: 8px 12px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            font-size: 14px;
            transition: all 0.3s ease;
            min-width: 40px;
            text-align: center;
        }

        .pagination-link:hover {
            background: #0073aa;
            color: white;
            border-color: #0073aa;
            text-decoration: none;
        }

        .pagination-link.current {
            background: #0073aa;
            color: white;
            border-color: #0073aa;
            font-weight: bold;
        }

        .pagination-link.prev,
        .pagination-link.next {
            font-weight: bold;
            min-width: auto;
            padding: 8px 16px;
        }

        .pagination-dots {
            padding: 8px 4px;
            color: #666;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .hozi-quick-summary {
                grid-template-columns: 1fr;
                padding: 15px;
            }

            .summary-item {
                padding: 12px;
            }

            .summary-icon {
                font-size: 20px;
                margin-left: 10px;
            }

            .summary-content strong {
                font-size: 20px;
            }

            .hozi-status-filters {
                flex-direction: column;
            }

            .filter-btn {
                text-align: center;
            }

            .hozi-pagination {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .pagination-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination-link {
                padding: 6px 10px;
                font-size: 13px;
                min-width: 35px;
            }

            .pagination-link.prev,
            .pagination-link.next {
                padding: 6px 12px;
            }
        }
        </style>
        <?php
    }

    /**
     * Customer orders shortcode with filtering
     */
    public function customer_orders_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>يجب تسجيل الدخول لعرض الطلبات</p>';
        }

        $atts = shortcode_atts(array(
            'status' => 'all',
            'limit' => 10
        ), $atts);

        // Get filter from URL
        $filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : $atts['status'];

        // Map filter to status
        $status_map = array(
            'all' => 'all',
            'confirmed' => 'confirmed',
            'pending' => 'pending_confirmation',
            'cancelled' => 'rejected'
        );

        $status = $status_map[$filter] ?? 'all';

        $customer_id = get_current_user_id();
        $orders = $this->get_customer_orders_with_confirmation($customer_id, $status, $atts['limit']);

        ob_start();
        ?>
        <div class="hozi-customer-orders">
            <div class="hozi-orders-filter">
                <h3>طلباتي</h3>
                <div class="filter-buttons">
                    <a href="?filter=all" class="filter-btn <?php echo $filter === 'all' ? 'active' : ''; ?>">جميع الطلبات</a>
                    <a href="?filter=confirmed" class="filter-btn <?php echo $filter === 'confirmed' ? 'active' : ''; ?>">المؤكدة</a>
                    <a href="?filter=pending" class="filter-btn <?php echo $filter === 'pending' ? 'active' : ''; ?>">في الانتظار</a>
                    <a href="?filter=cancelled" class="filter-btn <?php echo $filter === 'cancelled' ? 'active' : ''; ?>">الملغية</a>
                </div>
            </div>

            <?php if (!empty($orders)) : ?>
                <div class="orders-list">
                    <?php foreach ($orders as $order_data) :
                        $order = wc_get_order($order_data->order_id);
                        if (!$order) continue;
                    ?>
                        <div class="order-item">
                            <div class="order-header">
                                <h4>طلب #<?php echo $order->get_order_number(); ?></h4>
                                <span class="order-date"><?php echo $order->get_date_created()->format('Y/m/d'); ?></span>
                            </div>

                            <div class="order-details">
                                <div class="order-status">
                                    <strong>حالة الطلب:</strong>
                                    <?php echo wc_get_order_status_name($order->get_status()); ?>
                                </div>

                                <div class="confirmation-status">
                                    <strong>حالة التأكيد:</strong>
                                    <?php
                                    $status_labels = array(
                                        'pending_confirmation' => 'في انتظار التأكيد',
                                        'confirmed' => 'تم التأكيد',
                                        'rejected' => 'تم الرفض',
                                        'no_answer' => 'لم يرد',
                                        'callback_later' => 'إعادة اتصال'
                                    );
                                    echo $status_labels[$order_data->confirmation_status] ?? 'غير محدد';
                                    ?>
                                </div>

                                <div class="order-total">
                                    <strong>المجموع:</strong> <?php echo $order->get_formatted_order_total(); ?>
                                </div>

                                <?php if ($order_data->notes) : ?>
                                    <div class="order-notes">
                                        <strong>ملاحظات:</strong> <?php echo esc_html($order_data->notes); ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="order-actions">
                                <a href="<?php echo $order->get_view_order_url(); ?>" class="view-order-btn">عرض التفاصيل</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <div class="no-orders">
                    <p>لا توجد طلبات</p>
                </div>
            <?php endif; ?>
        </div>

        <style>
        .hozi-customer-orders {
            max-width: 800px;
            margin: 20px auto;
        }

        .hozi-orders-filter {
            margin-bottom: 30px;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #007cba;
            color: white;
            border-color: #007cba;
        }

        .order-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .order-header h4 {
            margin: 0;
            color: #007cba;
        }

        .order-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .order-actions {
            text-align: right;
        }

        .view-order-btn {
            background: #007cba;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .view-order-btn:hover {
            background: #005a87;
        }

        .hozi-confirmation-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .hozi-confirmation-status.pending { background: #ffc107; color: #000; }
        .hozi-confirmation-status.confirmed { background: #28a745; color: white; }
        .hozi-confirmation-status.rejected { background: #dc3545; color: white; }
        .hozi-confirmation-status.no-answer { background: #fd7e14; color: white; }
        .hozi-confirmation-status.callback { background: #17a2b8; color: white; }
        .hozi-confirmation-status.not-assigned { background: #6c757d; color: white; }

        .no-orders {
            text-align: center;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        @media (max-width: 768px) {
            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .order-details {
                grid-template-columns: 1fr;
            }

            .filter-buttons {
                flex-direction: column;
            }
        }
        </style>
        <?php
        return ob_get_clean();
    }

    /**
     * Get customer orders with confirmation status (HPOS Compatible)
     */
    public function get_customer_orders_with_confirmation($customer_id, $status = 'all', $limit = 10, $offset = 0) {
        global $wpdb;

        try {
            // Use WooCommerce HPOS compatible method
            $orders = wc_get_orders(array(
                'customer_id' => $customer_id,
                'limit' => $limit,
                'offset' => $offset,
                'orderby' => 'date',
                'order' => 'DESC',
                'return' => 'ids'
            ));

            if (empty($orders)) {
                return array();
            }

            // Get assignment data for these orders
            $order_ids_placeholders = implode(',', array_fill(0, count($orders), '%d'));
            $assignments = $wpdb->get_results($wpdb->prepare(
                "SELECT order_id, confirmation_status, notes, confirmed_at
                 FROM {$wpdb->prefix}hozi_order_assignments
                 WHERE order_id IN ($order_ids_placeholders)",
                $orders
            ), OBJECT_K);

            $results = array();
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if (!$order) continue;

                $assignment = isset($assignments[$order_id]) ? $assignments[$order_id] : null;
                $confirmation_status = $assignment ? $assignment->confirmation_status : null;

                // Filter by status if specified
                if ($status !== 'all') {
                    if ($confirmation_status !== $status && !($confirmation_status === null && $status === 'pending_confirmation')) {
                        continue;
                    }
                }

                $results[] = (object) array(
                    'order_id' => $order_id,
                    'post_date' => $order->get_date_created()->date('Y-m-d H:i:s'),
                    'post_status' => 'wc-' . $order->get_status(),
                    'confirmation_status' => $confirmation_status,
                    'notes' => $assignment ? $assignment->notes : null,
                    'confirmed_at' => $assignment ? $assignment->confirmed_at : null
                );
            }

            return $results;
        } catch (Exception $e) {
            return array();
        }
    }

    /**
     * Get customer orders count for pagination (HPOS Compatible)
     */
    public function get_customer_orders_count($customer_id, $status = 'all') {
        global $wpdb;

        try {
            // Use WooCommerce HPOS compatible method
            $orders = wc_get_orders(array(
                'customer_id' => $customer_id,
                'limit' => -1, // Get all orders
                'return' => 'ids'
            ));

            if (empty($orders)) {
                return 0;
            }

            // If no status filter, return total count
            if ($status === 'all') {
                return count($orders);
            }

            // Get assignment data for filtering
            $order_ids_placeholders = implode(',', array_fill(0, count($orders), '%d'));
            $assignments = $wpdb->get_results($wpdb->prepare(
                "SELECT order_id, confirmation_status
                 FROM {$wpdb->prefix}hozi_order_assignments
                 WHERE order_id IN ($order_ids_placeholders)",
                $orders
            ), OBJECT_K);

            $count = 0;
            foreach ($orders as $order_id) {
                $assignment = isset($assignments[$order_id]) ? $assignments[$order_id] : null;
                $confirmation_status = $assignment ? $assignment->confirmation_status : null;

                // Check if matches status filter
                if ($confirmation_status === $status || ($confirmation_status === null && $status === 'pending_confirmation')) {
                    $count++;
                }
            }

            return $count;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Get customer order counts by status
     */
    public function get_customer_order_counts($customer_id) {
        global $wpdb;

        try {
            $counts = $wpdb->get_row($wpdb->prepare(
                "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN a.confirmation_status = 'pending_confirmation' OR a.confirmation_status IS NULL THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN a.confirmation_status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                    SUM(CASE WHEN a.confirmation_status = 'rejected' THEN 1 ELSE 0 END) as rejected,
                    SUM(CASE WHEN a.confirmation_status = 'no_answer' THEN 1 ELSE 0 END) as no_answer,
                    SUM(CASE WHEN a.confirmation_status = 'callback_later' THEN 1 ELSE 0 END) as callback
                 FROM {$wpdb->posts} p
                 LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_customer_user'
                 LEFT JOIN {$wpdb->prefix}hozi_order_assignments a ON p.ID = a.order_id
                 WHERE (p.post_author = %d OR pm.meta_value = %d)
                 AND p.post_type = 'shop_order'",
                $customer_id, $customer_id
            ));

            return $counts ?: (object) array(
                'total' => 0,
                'pending' => 0,
                'confirmed' => 0,
                'rejected' => 0,
                'no_answer' => 0,
                'callback' => 0
            );
        } catch (Exception $e) {
            return (object) array(
                'total' => 0,
                'pending' => 0,
                'confirmed' => 0,
                'rejected' => 0,
                'no_answer' => 0,
                'callback' => 0
            );
        }
    }

    /**
     * Get order assignment
     */
    private function get_order_assignment($order_id) {
        global $wpdb;

        // Check if table exists first
        $table_name = $wpdb->prefix . 'hozi_order_assignments';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            return null;
        }

        try {
            return $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
                $order_id
            ));
        } catch (Exception $e) {
            return null;
        }
    }
}

// Initialize the class
new Hozi_Akadly_Customer_Orders();
