<?php
/**
 * Agent management class
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Agent_Manager {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }

    /**
     * Initialize
     */
    public function init() {
        // Hook into user creation/update
        add_action('user_register', array($this, 'maybe_create_agent'));
        add_action('profile_update', array($this, 'maybe_update_agent'));
        add_action('delete_user', array($this, 'delete_agent'));
    }

    /**
     * Create a new agent
     */
    public function create_agent($user_id, $data = array()) {
        global $wpdb;

        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }

        // Remove all existing roles and set only confirmation_agent role
        $user->set_role('confirmation_agent');

        // Ensure the role has correct capabilities
        $this->ensure_agent_capabilities($user_id);

        $agent_data = array(
            'user_id' => $user_id,
            'name' => $data['name'] ?? $user->display_name,
            'phone' => $data['phone'] ?? get_user_meta($user_id, 'phone', true),
            'email' => $data['email'] ?? $user->user_email,
            'is_active' => $data['is_active'] ?? 1,
            'max_orders_per_day' => $data['max_orders_per_day'] ?? 0,
            'created_at' => current_time('mysql')
        );

        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_agents',
            $agent_data
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Ensure agent has correct capabilities
     */
    private function ensure_agent_capabilities($user_id) {
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }

        // Get the confirmation_agent role
        $role = get_role('confirmation_agent');
        if (!$role) {
            // Create the role if it doesn't exist
            hozi_akadly_create_agent_role();
            $role = get_role('confirmation_agent');
        }

        // Ensure user has the role
        if (!in_array('confirmation_agent', $user->roles)) {
            $user->add_role('confirmation_agent');
        }

        return true;
    }

    /**
     * Update agent
     */
    public function update_agent($agent_id, $data) {
        global $wpdb;

        $data['updated_at'] = current_time('mysql');

        return $wpdb->update(
            $wpdb->prefix . 'hozi_agents',
            $data,
            array('id' => $agent_id)
        );
    }

    /**
     * Delete agent
     */
    public function delete_agent($user_id) {
        global $wpdb;

        // Remove agent role from user
        $user = get_user_by('id', $user_id);
        if ($user) {
            $user->remove_role('confirmation_agent');
        }

        // Delete agent record (cascades to assignments and logs)
        return $wpdb->delete(
            $wpdb->prefix . 'hozi_agents',
            array('user_id' => $user_id)
        );
    }

    /**
     * Get agent by ID
     */
    public function get_agent($agent_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT a.*, u.user_login, u.user_email as wp_email
             FROM {$wpdb->prefix}hozi_agents a
             LEFT JOIN {$wpdb->users} u ON a.user_id = u.ID
             WHERE a.id = %d",
            $agent_id
        ));
    }

    /**
     * Get all agents
     */
    public function get_agents($active_only = false) {
        global $wpdb;

        $where = $active_only ? "WHERE a.is_active = 1" : "";

        return $wpdb->get_results(
            "SELECT a.*, u.user_login, u.user_email as wp_email
             FROM {$wpdb->prefix}hozi_agents a
             LEFT JOIN {$wpdb->users} u ON a.user_id = u.ID
             $where
             ORDER BY a.name ASC"
        );
    }

    /**
     * Get agent by user ID
     */
    public function get_agent_by_user_id($user_id) {
        return Hozi_Akadly_Database::get_agent_by_user_id($user_id);
    }

    /**
     * Check if user is agent
     */
    public function is_agent($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        $user = get_user_by('id', $user_id);
        return $user && in_array('confirmation_agent', $user->roles);
    }

    /**
     * Get current agent
     */
    public function get_current_agent() {
        if (!$this->is_agent()) {
            return false;
        }

        return $this->get_agent_by_user_id(get_current_user_id());
    }

    /**
     * Toggle agent status
     */
    public function toggle_agent_status($agent_id) {
        global $wpdb;

        $agent = $this->get_agent($agent_id);
        if (!$agent) {
            return false;
        }

        $new_status = $agent->is_active ? 0 : 1;

        return $this->update_agent($agent_id, array('is_active' => $new_status));
    }

    /**
     * Get agent statistics
     */
    public function get_agent_stats($agent_id, $date_from = null, $date_to = null) {
        global $wpdb;

        $date_where = '';
        if ($date_from && $date_to) {
            $date_where = $wpdb->prepare(
                " AND assigned_at BETWEEN %s AND %s",
                $date_from,
                $date_to
            );
        }

        return $wpdb->get_row($wpdb->prepare(
            "SELECT
                COUNT(*) as total_assignments,
                COUNT(CASE WHEN confirmation_status = 'confirmed' THEN 1 END) as confirmed,
                COUNT(CASE WHEN confirmation_status = 'rejected' THEN 1 END) as rejected,
                COUNT(CASE WHEN confirmation_status = 'no_answer' THEN 1 END) as no_answer,
                COUNT(CASE WHEN confirmation_status = 'callback_later' THEN 1 END) as callback_later,
                COUNT(CASE WHEN confirmation_status = 'pending_confirmation' THEN 1 END) as pending
             FROM {$wpdb->prefix}hozi_order_assignments
             WHERE agent_id = %d $date_where",
            $agent_id
        ));
    }

    /**
     * Maybe create agent when user is created
     */
    public function maybe_create_agent($user_id) {
        $user = get_user_by('id', $user_id);
        if ($user && in_array('confirmation_agent', $user->roles)) {
            $this->create_agent($user_id);
        }
    }

    /**
     * Maybe update agent when user is updated
     */
    public function maybe_update_agent($user_id) {
        $agent = $this->get_agent_by_user_id($user_id);
        if ($agent) {
            $user = get_user_by('id', $user_id);
            $this->update_agent($agent->id, array(
                'name' => $user->display_name,
                'email' => $user->user_email
            ));
        }
    }

    /**
     * Get next agent for round robin distribution
     */
    public function get_next_agent_for_round_robin() {
        global $wpdb;

        // Get active agents ordered by last assignment time
        $agents = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}hozi_agents
             WHERE is_active = 1
             AND (max_orders_per_day = 0 OR current_orders_count < max_orders_per_day)
             ORDER BY last_assigned_at ASC, id ASC
             LIMIT 1"
        );

        return !empty($agents) ? $agents[0] : null;
    }

    /**
     * Update agent last assigned time
     */
    public function update_last_assigned($agent_id) {
        return $this->update_agent($agent_id, array(
            'last_assigned_at' => current_time('mysql')
        ));
    }

    /**
     * Increment agent current orders count
     */
    public function increment_current_orders($agent_id) {
        global $wpdb;

        return $wpdb->query($wpdb->prepare(
            "UPDATE {$wpdb->prefix}hozi_agents
             SET current_orders_count = current_orders_count + 1
             WHERE id = %d",
            $agent_id
        ));
    }

    /**
     * Decrement agent current orders count
     */
    public function decrement_current_orders($agent_id) {
        global $wpdb;

        return $wpdb->query($wpdb->prepare(
            "UPDATE {$wpdb->prefix}hozi_agents
             SET current_orders_count = GREATEST(current_orders_count - 1, 0)
             WHERE id = %d",
            $agent_id
        ));
    }
}
