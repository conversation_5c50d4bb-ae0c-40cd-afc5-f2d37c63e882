<?php
/**
 * Apply Final Solution - Complete System Replacement
 * This file replaces the old buggy system with a new, clean implementation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

global $wpdb;

echo "<h1>🚀 تطبيق الحل النهائي لنظام المراسلة</h1>";
echo "<p><strong>هذا الحل سيستبدل النظام القديم بنظام جديد ومبسط وخالي من الأخطاء</strong></p>";

// Step 1: Create new tables
echo "<h2>1️⃣ إنشاء الجداول الجديدة</h2>";

// Create new messaging table
$messages_table = $wpdb->prefix . 'hozi_messages_v2';
$charset_collate = $wpdb->get_charset_collate();

$sql_messages = "CREATE TABLE IF NOT EXISTS {$messages_table} (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    sender_id bigint(20) NOT NULL,
    recipient_id bigint(20) DEFAULT NULL,
    recipient_type enum('user', 'all_agents', 'all_admins') DEFAULT 'user',
    subject varchar(255) NOT NULL,
    message text NOT NULL,
    priority enum('normal', 'high', 'urgent') DEFAULT 'normal',
    is_read tinyint(1) DEFAULT 0,
    is_archived tinyint(1) DEFAULT 0,
    is_deleted tinyint(1) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    read_at datetime DEFAULT NULL,
    archived_at datetime DEFAULT NULL,
    PRIMARY KEY (id),
    KEY sender_id (sender_id),
    KEY recipient_id (recipient_id),
    KEY recipient_type (recipient_type),
    KEY is_read (is_read),
    KEY is_archived (is_archived),
    KEY is_deleted (is_deleted),
    KEY created_at (created_at)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
dbDelta($sql_messages);

if ($wpdb->get_var("SHOW TABLES LIKE '$messages_table'") == $messages_table) {
    echo "✅ تم إنشاء جدول الرسائل الجديد: $messages_table<br>";
} else {
    echo "❌ فشل في إنشاء جدول الرسائل<br>";
}

// Create admin actions table
$admin_actions_table = $wpdb->prefix . 'hozi_admin_actions';

$sql_admin_actions = "CREATE TABLE IF NOT EXISTS {$admin_actions_table} (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    admin_user_id bigint(20) NOT NULL,
    order_id bigint(20) NOT NULL,
    action_type enum('confirmed', 'rejected', 'no_answer', 'callback') DEFAULT 'confirmed',
    acting_as_agent_id bigint(20) DEFAULT NULL,
    notes text DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY admin_user_id (admin_user_id),
    KEY order_id (order_id),
    KEY action_type (action_type),
    KEY acting_as_agent_id (acting_as_agent_id),
    KEY created_at (created_at)
) $charset_collate;";

dbDelta($sql_admin_actions);

if ($wpdb->get_var("SHOW TABLES LIKE '$admin_actions_table'") == $admin_actions_table) {
    echo "✅ تم إنشاء جدول إجراءات المدير: $admin_actions_table<br>";
} else {
    echo "❌ فشل في إنشاء جدول إجراءات المدير<br>";
}

// Step 2: Update order assignments table
echo "<h2>2️⃣ تحديث جدول تخصيص الطلبات</h2>";

$assignments_table = $wpdb->prefix . 'hozi_order_assignments';
$assignments_columns = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table");
$assignments_column_names = array_column($assignments_columns, 'Field');

$columns_to_add = array(
    'confirmed_by_admin' => 'tinyint(1) DEFAULT 0',
    'admin_user_id' => 'int(11) DEFAULT NULL',
    'original_agent_id' => 'int(11) DEFAULT NULL'
);

foreach ($columns_to_add as $column => $definition) {
    if (!in_array($column, $assignments_column_names)) {
        $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN $column $definition");
        echo "✅ تم إضافة العمود: $column<br>";
    } else {
        echo "✅ العمود موجود مسبقاً: $column<br>";
    }
}

// Step 3: Migrate existing messages (if any)
echo "<h2>3️⃣ نقل الرسائل الموجودة (إن وجدت)</h2>";

$old_messages_table = $wpdb->prefix . 'hozi_messages';
if ($wpdb->get_var("SHOW TABLES LIKE '$old_messages_table'") == $old_messages_table) {
    $old_messages = $wpdb->get_results("SELECT * FROM $old_messages_table WHERE is_deleted = 0");
    
    $migrated_count = 0;
    foreach ($old_messages as $old_msg) {
        $recipient_type = 'user';
        if ($old_msg->recipient_type === 'all') {
            $recipient_type = 'all_agents';
        }
        
        $result = $wpdb->insert(
            $messages_table,
            array(
                'sender_id' => $old_msg->sender_id,
                'recipient_id' => $old_msg->recipient_id,
                'recipient_type' => $recipient_type,
                'subject' => $old_msg->subject,
                'message' => $old_msg->message,
                'priority' => $old_msg->priority ?? 'normal',
                'is_read' => $old_msg->is_read ?? 0,
                'is_archived' => $old_msg->is_archived ?? 0,
                'created_at' => $old_msg->created_at
            )
        );
        
        if ($result) {
            $migrated_count++;
        }
    }
    
    echo "✅ تم نقل $migrated_count رسالة من النظام القديم<br>";
} else {
    echo "ℹ️ لا يوجد جدول رسائل قديم للنقل منه<br>";
}

// Step 4: Test the new system
echo "<h2>4️⃣ اختبار النظام الجديد</h2>";

// Get agents
$agents = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'hozi_akadly_agent',
            'value' => '1',
            'compare' => '='
        )
    )
));

if (empty($agents)) {
    $agents = get_users(array('capability' => 'hozi_view_assigned_orders'));
}

echo "عدد الوكلاء الموجودين: " . count($agents) . "<br>";

if (!empty($agents)) {
    // Send test message to all agents
    $admin_id = 1; // Assuming admin is user ID 1
    $test_subject = "رسالة اختبار النظام الجديد - " . date('H:i:s');
    $test_message = "هذه رسالة اختبار للنظام الجديد. إذا وصلتك هذه الرسالة، فإن النظام يعمل بشكل صحيح.";
    
    $sent_count = 0;
    foreach ($agents as $agent) {
        $result = $wpdb->insert(
            $messages_table,
            array(
                'sender_id' => $admin_id,
                'recipient_id' => $agent->ID,
                'recipient_type' => 'user',
                'subject' => $test_subject,
                'message' => $test_message,
                'priority' => 'normal',
                'created_at' => current_time('mysql')
            )
        );
        
        if ($result) {
            $sent_count++;
        }
    }
    
    echo "✅ تم إرسال $sent_count رسالة اختبار من أصل " . count($agents) . " وكلاء<br>";
} else {
    echo "⚠️ لم يتم العثور على وكلاء للاختبار<br>";
}

// Step 5: Create activation file
echo "<h2>5️⃣ إنشاء ملف التفعيل</h2>";

$activation_code = '<?php
/**
 * Activate New Messaging System
 * Add this code to your theme\'s functions.php or create a must-use plugin
 */

// Initialize new messaging system
add_action(\'init\', function() {
    if (class_exists(\'Hozi_Akadly_Messaging_System_V2\')) {
        new Hozi_Akadly_Messaging_System_V2();
    }
    
    if (class_exists(\'Hozi_Akadly_Admin_Stats_Tracker\')) {
        new Hozi_Akadly_Admin_Stats_Tracker();
    }
    
    if (class_exists(\'Hozi_Akadly_Admin_As_Agent_V2\')) {
        new Hozi_Akadly_Admin_As_Agent_V2();
    }
});

// Update AJAX actions to use new system
add_action(\'wp_ajax_hozi_send_message\', function() {
    // Redirect to new system
    $_POST[\'action\'] = \'hozi_send_message_v2\';
    do_action(\'wp_ajax_hozi_send_message_v2\');
});

add_action(\'wp_ajax_hozi_get_messages\', function() {
    // Redirect to new system
    $_POST[\'action\'] = \'hozi_get_messages_v2\';
    do_action(\'wp_ajax_hozi_get_messages_v2\');
});

add_action(\'wp_ajax_hozi_get_unread_count\', function() {
    // Redirect to new system
    $_POST[\'action\'] = \'hozi_get_unread_count_v2\';
    do_action(\'wp_ajax_hozi_get_unread_count_v2\');
});

// Update admin-as-agent form
add_action(\'admin_init\', function() {
    if (isset($_POST[\'update_confirmation\']) && isset($_POST[\'admin_as_agent\'])) {
        $_POST[\'admin_as_agent_confirmation\'] = 1;
    }
});
?>';

file_put_contents(dirname(__FILE__) . '/activate-new-system.php', $activation_code);
echo "✅ تم إنشاء ملف التفعيل: activate-new-system.php<br>";

// Step 6: Instructions
echo "<h2>6️⃣ تعليمات التفعيل</h2>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;'>";
echo "<h3>🎉 تم تطبيق الحل النهائي بنجاح!</h3>";
echo "<p><strong>الخطوات التالية:</strong></p>";
echo "<ol>";
echo "<li><strong>تفعيل النظام الجديد:</strong> انسخ محتوى ملف activate-new-system.php إلى functions.php في القالب</li>";
echo "<li><strong>تحديث ملف class-hozi-akadly.php:</strong> استبدل السطر 91 بالكود الجديد</li>";
echo "<li><strong>تحديث نموذج العمل كوكيل:</strong> أضف حقل مخفي admin_as_agent=1 في النموذج</li>";
echo "<li><strong>اختبار النظام:</strong> تحقق من وصول الرسائل للوكلاء وعمل إحصائيات المدير</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;'>";
echo "<h4>⚠️ ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>النظام الجديد يستخدم جداول منفصلة لتجنب تضارب البيانات</li>";
echo "<li>الرسائل القديمة تم نقلها تلقائياً</li>";
echo "<li>إحصائيات المدير ستُحفظ في جدول منفصل</li>";
echo "<li>النظام مصمم ليكون بسيط وخالي من الأخطاء</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📊 ملخص النتائج:</h3>";
echo "<ul>";
echo "<li>✅ جدول رسائل جديد: $messages_table</li>";
echo "<li>✅ جدول إجراءات المدير: $admin_actions_table</li>";
echo "<li>✅ تحديث جدول التخصيصات</li>";
echo "<li>✅ نقل الرسائل الموجودة</li>";
echo "<li>✅ اختبار إرسال الرسائل</li>";
echo "<li>✅ ملف التفعيل جاهز</li>";
echo "</ul>";

echo "<p><strong>النظام الجديد جاهز للاستخدام!</strong></p>";
?>