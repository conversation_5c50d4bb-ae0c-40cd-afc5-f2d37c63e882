<?php
/**
 * Delivery Tracking Page - NEW APPROACH
 * Simple page for tracking completed orders
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get current agent - with better error handling and permissions check
$agent_manager = new Hozi_Akadly_Agent_Manager();

// Check if current_agent is passed from admin class (for admins)
if (!isset($current_agent)) {
    try {
        $current_agent = $agent_manager->get_current_agent();
    } catch (Exception $e) {
        // Fallback: manual agent detection
        $user_id = get_current_user_id();
        $agent_data = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d AND is_active = 1",
            $user_id
        ));

        if ($agent_data) {
            $current_agent = (object) $agent_data;
        } else {
            $current_agent = null;
        }
    }
}

// For non-admin users, require agent data
if (!current_user_can('manage_options') && !$current_agent) {
    wp_die(__('لم يتم العثور على بيانات الوكيل.', 'hozi-akadly'));
}

// Display access control information
$access_description = Hozi_Akadly_Delivery_Tracking_Permissions::get_access_control_description();
$is_admin = current_user_can('manage_options');

global $wpdb;

// Check if tracking table exists and create if missing
$tracking_table = $wpdb->prefix . 'hozi_order_tracking';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;

// 🛠️ CRITICAL FIX: Create table if missing
if (!$table_exists) {
    error_log("Hozi Akadly: Tracking table missing in delivery-tracking page, creating it...");

    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE $tracking_table (
        id int(11) NOT NULL AUTO_INCREMENT,
        order_id bigint(20) NOT NULL,
        agent_id int(11) NOT NULL,
        status varchar(50) NOT NULL,
        previous_status varchar(50) DEFAULT NULL,
        reason_category varchar(100) DEFAULT NULL,
        reason_details text DEFAULT NULL,
        notes text DEFAULT NULL,
        updated_by int(11) NOT NULL,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY order_id (order_id),
        KEY agent_id (agent_id),
        KEY status (status),
        KEY updated_at (updated_at)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Verify table was created
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
    if ($table_exists) {
        error_log("Hozi Akadly: Tracking table created successfully in delivery-tracking page");
    } else {
        error_log("Hozi Akadly: Failed to create tracking table in delivery-tracking page");
    }
}

// Show warning if table still doesn't exist
if (!$table_exists) {
    ?>
    <div class="wrap">
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h2 style="color: #856404; margin-top: 0;">⚠️ خطأ في قاعدة البيانات</h2>
            <p style="color: #856404; margin-bottom: 15px;">
                جدول متابعة التوصيل غير موجود في قاعدة البيانات. يرجى الاتصال بالمطور أو إعادة تفعيل الإضافة.
            </p>
            <div style="margin-top: 15px;">
                <a href="<?php echo admin_url('admin.php?page=hozi-akadly-settings'); ?>" class="button button-primary">
                    الذهاب إلى الإعدادات
                </a>
                <a href="<?php echo admin_url('plugins.php'); ?>" class="button button-secondary">
                    إدارة الإضافات
                </a>
            </div>
        </div>
    </div>
    <?php
    return;
}

// Archived columns are now handled in main plugin file

// Handle status updates
if (isset($_POST['update_delivery_status']) && wp_verify_nonce($_POST['_wpnonce'], 'hozi_delivery_update')) {
    $order_id = intval($_POST['order_id']);
    $status = sanitize_text_field($_POST['status']);
    $notes = sanitize_textarea_field($_POST['notes']);

    // Determine agent_id for tracking
    $tracking_agent_id = null;
    if ($current_agent) {
        $tracking_agent_id = $current_agent->id;
    } else if ($is_admin) {
        // For admin users, try to get the original agent who confirmed the order
        $original_assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT agent_id FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d AND confirmation_status = 'confirmed' LIMIT 1",
            $order_id
        ));
        if ($original_assignment) {
            $tracking_agent_id = $original_assignment->agent_id;
        }
    }

    if (!$tracking_agent_id) {
        echo '<div class="notice notice-error"><p>❌ لا يمكن تحديد الوكيل المسؤول عن هذا الطلب!</p></div>';
        return;
    }

    // Update delivery status in the correct table
    $result = $wpdb->insert(
        $wpdb->prefix . 'hozi_order_tracking',
        array(
            'order_id' => $order_id,
            'agent_id' => $tracking_agent_id,
            'status' => $status,
            'notes' => $notes,
            'updated_at' => current_time('mysql'),
            'created_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%s', '%s', '%s')
    );

    if ($result) {
        // Update order note
        $order = wc_get_order($order_id);
        if ($order) {
            $status_labels = array(
                'delivered' => 'تم التوصيل بنجاح',
                'rejected' => 'تم الرفض',
                'postponed' => 'تم التأجيل',
                'exchange' => 'طلب استبدال'
            );

            // Get agent name for the note
            $agent_name = 'غير معروف';
            if ($current_agent) {
                $agent_name = $current_agent->name;
            } else if ($is_admin) {
                $agent_data = $wpdb->get_row($wpdb->prepare(
                    "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                    $tracking_agent_id
                ));
                if ($agent_data) {
                    $agent_name = $agent_data->name;
                }
            }

            $order->add_order_note(
                sprintf(
                    '📦 تحديث حالة التوصيل: %s%s%s%s',
                    $status_labels[$status] ?? $status,
                    "\n👤 الوكيل: " . $agent_name,
                    $is_admin ? "\n🔧 تم التحديث بواسطة المشرف" : "",
                    $notes ? "\n📝 ملاحظات: " . $notes : ''
                ),
                0
            );
        }

        // Auto-archive orders with final statuses (delivered/rejected)
        $should_archive = in_array($status, ['delivered', 'rejected']);

        if ($should_archive) {
            // First, check if the assignment exists
            $assignment_check = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d AND agent_id = %d",
                $order_id, $tracking_agent_id
            ));

            if ($assignment_check) {
                // Archive using notes field since archived columns don't exist
                $archive_result = $wpdb->update(
                    $wpdb->prefix . 'hozi_order_assignments',
                    array('notes' => 'ARCHIVED:' . $status . ':' . current_time('mysql')),
                    array('order_id' => $order_id, 'agent_id' => $tracking_agent_id),
                    array('%s'),
                    array('%d', '%d')
                );

                if ($archive_result !== false) {
                    // Verify archiving worked by checking notes field
                    $verify_archive = $wpdb->get_var($wpdb->prepare(
                        "SELECT notes FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d AND agent_id = %d",
                        $order_id, $tracking_agent_id
                    ));

                    if (strpos($verify_archive, 'ARCHIVED:') === 0) {
                        echo '<div class="notice notice-success"><p>✅ تم تحديث حالة التوصيل وأرشفة الطلب بنجاح! الطلب متوفر الآن في صفحة الأرشيف.</p></div>';

                        // Add JavaScript to refresh page immediately
                        echo '<script>
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        </script>';
                    } else {
                        echo '<div class="notice notice-error"><p>❌ فشلت الأرشفة! النوتس الحالية: ' . $verify_archive . '</p></div>';
                    }
                } else {
                    echo '<div class="notice notice-warning"><p>⚠️ تم تحديث حالة التوصيل لكن فشلت الأرشفة. الطلب ID: ' . $order_id . ' - خطأ SQL: ' . $wpdb->last_error . '</p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p>❌ لم يتم العثور على تخصيص الطلب للوكيل!</p></div>';
            }
        } else {
            echo '<div class="notice notice-success"><p>✅ تم تحديث حالة التوصيل بنجاح!</p></div>';
        }
    } else {
        echo '<div class="notice notice-error"><p>❌ فشل في تحديث حالة التوصيل!</p></div>';
    }
}

// Get trackable orders based on permissions
$trackable_orders = Hozi_Akadly_Delivery_Tracking_Permissions::get_trackable_orders_for_user();

// Convert to assignment format for compatibility
$confirmed_assignments = array();
if (!empty($trackable_orders)) {
    $order_ids = array_column($trackable_orders, 'order_id');
    $order_ids_str = implode(',', array_map('intval', $order_ids));

    if ($order_ids_str) {
        $confirmed_assignments = $wpdb->get_results("
            SELECT * FROM {$wpdb->prefix}hozi_order_assignments
            WHERE order_id IN ({$order_ids_str})
            AND confirmation_status = 'confirmed'
        ");
    }
}

// Step 2: Convert to simple array and validate orders exist
$completed_orders = array();
foreach ($confirmed_assignments as $assignment) {
    $order = wc_get_order($assignment->order_id);
    if ($order) {
        $order_data = new stdClass();
        $order_data->order_id = $assignment->order_id;
        $order_data->order_date = $assignment->confirmed_at;
        $order_data->confirmation_notes = $assignment->notes;
        $order_data->archived = (strpos($assignment->notes, 'ARCHIVED:') === 0);
        $order_data->delivery_status = null;
        $order_data->delivery_notes = '';
        $order_data->delivery_date = null;

        $completed_orders[] = $order_data;
    }
}

// Step 3: Get tracking data if tracking table exists
if (!empty($completed_orders) && $table_exists) {
    $order_ids = array_column($completed_orders, 'order_id');
    $order_ids_str = implode(',', array_map('intval', $order_ids));

    if ($order_ids_str) {
        $tracking_data = $wpdb->get_results("
            SELECT order_id, status, notes, updated_at
            FROM {$wpdb->prefix}hozi_order_tracking
            WHERE order_id IN ({$order_ids_str})
            ORDER BY updated_at DESC
        ");

        // Apply tracking data to orders
        foreach ($completed_orders as $order_data) {
            foreach ($tracking_data as $track) {
                if ($track->order_id == $order_data->order_id) {
                    $order_data->delivery_status = $track->status;
                    $order_data->delivery_notes = $track->notes;
                    $order_data->delivery_date = $track->updated_at;
                    break; // Take the latest one
                }
            }
        }
    }
}

// Step 4: Filter out archived orders (show only active ones)
$completed_orders = array_filter($completed_orders, function($order) {
    return !$order->archived;
});

// Step 5: Apply advanced filters
$status_filter = isset($_GET['status_filter']) ? sanitize_text_field($_GET['status_filter']) : '';
$date_filter = isset($_GET['date_filter']) ? sanitize_text_field($_GET['date_filter']) : '';
$search_filter = isset($_GET['search_filter']) ? sanitize_text_field($_GET['search_filter']) : '';

if ($status_filter || $date_filter || $search_filter) {
    $completed_orders = array_filter($completed_orders, function($order_data) use ($status_filter, $date_filter, $search_filter) {
        // Status filter
        if ($status_filter) {
            if ($status_filter === 'pending' && $order_data->delivery_status !== null) {
                return false;
            }
            if ($status_filter !== 'pending' && $order_data->delivery_status !== $status_filter) {
                return false;
            }
        }

        // Date filter
        if ($date_filter && $order_data->order_date) {
            $order_date = strtotime($order_data->order_date);
            $today = strtotime('today');

            switch ($date_filter) {
                case 'today':
                    if ($order_date < $today) return false;
                    break;
                case 'yesterday':
                    $yesterday = strtotime('yesterday');
                    if ($order_date < $yesterday || $order_date >= $today) return false;
                    break;
                case 'this_week':
                    $week_start = strtotime('monday this week');
                    if ($order_date < $week_start) return false;
                    break;
                case 'last_week':
                    $last_week_start = strtotime('monday last week');
                    $last_week_end = strtotime('sunday last week');
                    if ($order_date < $last_week_start || $order_date > $last_week_end) return false;
                    break;
                case 'this_month':
                    $month_start = strtotime('first day of this month');
                    if ($order_date < $month_start) return false;
                    break;
            }
        }

        // Search filter
        if ($search_filter) {
            $wc_order = wc_get_order($order_data->order_id);
            if ($wc_order) {
                $customer_name = $wc_order->get_billing_first_name() . ' ' . $wc_order->get_billing_last_name();
                $order_id_str = (string)$order_data->order_id;

                if (stripos($customer_name, $search_filter) === false &&
                    stripos($order_id_str, $search_filter) === false) {
                    return false;
                }
            }
        }

        return true;
    });
}

// Enrich order data using WooCommerce functions
foreach ($completed_orders as $order_data) {
    $order = wc_get_order($order_data->order_id);
    if ($order) {
        $order_data->order_total = $order->get_total();
        $order_data->billing_first_name = $order->get_billing_first_name();
        $order_data->billing_last_name = $order->get_billing_last_name();
        $order_data->billing_phone = $order->get_billing_phone();
        $order_data->billing_address = $order->get_billing_address_1();

        // Set default delivery status for orders without tracking data
        if (!isset($order_data->delivery_status) || $order_data->delivery_status === null) {
            $order_data->delivery_status = null; // Keep as null for pending orders
            $order_data->delivery_notes = '';
            $order_data->delivery_date = null;
        }
    }
}

// Get statistics (after filtering)
$stats = array(
    'total' => count($completed_orders),
    'pending' => 0,
    'delivered' => 0,
    'rejected' => 0,
    'postponed' => 0,
    'exchange' => 0
);

foreach ($completed_orders as $order) {
    if (!$order->delivery_status) {
        $stats['pending']++;
    } else {
        switch ($order->delivery_status) {
            case 'delivered':
                $stats['delivered']++;
                break;
            case 'rejected':
                $stats['rejected']++;
                break;
            case 'postponed':
                $stats['postponed']++;
                break;
            case 'exchange':
                $stats['exchange']++;
                break;
        }
    }
}

// Add filter indicator to stats
$filter_active = !empty($status_filter) || !empty($date_filter) || !empty($search_filter);

// Pagination setup
$orders_per_page = 10; // عدد الطلبات في كل صفحة
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$total_orders = count($completed_orders);
$total_pages = ceil($total_orders / $orders_per_page);
$offset = ($current_page - 1) * $orders_per_page;

// Apply pagination
$paginated_orders = array_slice($completed_orders, $offset, $orders_per_page);
?>

<div class="wrap hozi-delivery-tracking-wrap">
    <!-- Header -->
    <div class="hozi-header">
        <h1>📦 متابعة التوصيل</h1>
        <p>متابعة وتحديث حالة الطلبات المكتملة</p>

        <!-- Access Control Information -->
        <div class="hozi-access-info">
            <div class="hozi-access-badge <?php echo $is_admin ? 'admin' : 'agent'; ?>">
                <span class="dashicons <?php echo $is_admin ? 'dashicons-admin-users' : 'dashicons-businessman'; ?>"></span>
                <span><?php echo $access_description; ?></span>
            </div>
            <?php if ($is_admin): ?>
                <div class="hozi-admin-note">
                    <span class="dashicons dashicons-info"></span>
                    <span>كمشرف، يمكنك رؤية جميع الطلبات وتعديل إعدادات الوصول من صفحة الإعدادات</span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Navigation Tabs - 3 Steps Only -->
    <?php if (!$is_admin): // Only show navigation for agents ?>
    <div class="hozi-nav-tabs">
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-orders'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-cart"></span>
            <span class="hozi-step-number">1</span>
            <?php _e('الطلبات المخصصة لي', 'hozi-akadly'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-delivery-tracking'); ?>" class="hozi-nav-tab hozi-nav-tab-active">
            <span class="dashicons dashicons-truck"></span>
            <span class="hozi-step-number">2</span>
            <?php _e('متابعة التوصيل', 'hozi-akadly'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-archived'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-archive"></span>
            <span class="hozi-step-number">3</span>
            <?php _e('الطلبات المؤرشفة', 'hozi-akadly'); ?>
        </a>
    </div>
    <?php else: // Show admin navigation ?>
    <div class="hozi-admin-nav">
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-settings'); ?>" class="button button-secondary">
            <span class="dashicons dashicons-admin-settings"></span>
            <?php _e('إعدادات متابعة التوصيل', 'hozi-akadly'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly'); ?>" class="button button-secondary">
            <span class="dashicons dashicons-dashboard"></span>
            <?php _e('لوحة التحكم الرئيسية', 'hozi-akadly'); ?>
        </a>
    </div>
    <?php endif; ?>

    <!-- Statistics -->
    <div class="hozi-stats-section">
        <div class="hozi-stats-grid">
            <div class="hozi-stat-card hozi-total-card <?php echo $filter_active ? 'filtered' : ''; ?>">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo $stats['total']; ?></h3>
                    <p>إجمالي المكتملة <?php echo $filter_active ? '(مفلتر)' : ''; ?></p>
                </div>
            </div>

            <div class="hozi-stat-card hozi-pending-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-clock"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo $stats['pending']; ?></h3>
                    <p>بانتظار التوصيل</p>
                </div>
            </div>

            <div class="hozi-stat-card hozi-delivered-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo $stats['delivered']; ?></h3>
                    <p>تم التوصيل</p>
                </div>
            </div>

            <div class="hozi-stat-card hozi-issues-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-warning"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo ($stats['rejected'] + $stats['postponed'] + $stats['exchange']); ?></h3>
                    <p>تحتاج متابعة</p>
                </div>
            </div>
        </div>
    </div>



    <!-- Advanced Filters -->
    <div class="hozi-filters-section">
        <div class="hozi-filters-header">
            <h3>🎯 فلترة الطلبات</h3>
            <button type="button" class="hozi-toggle-filters" onclick="toggleFilters()">
                <span class="dashicons dashicons-filter"></span>
                إظهار/إخفاء الفلاتر
            </button>
        </div>

        <div class="hozi-filters-container" id="filtersContainer">
            <form method="GET" action="" class="hozi-filters-form">
                <input type="hidden" name="page" value="hozi-akadly-delivery-tracking">

                <div class="hozi-filters-grid">
                    <!-- Status Filter -->
                    <div class="hozi-filter-group">
                        <label>حالة التوصيل:</label>
                        <select name="status_filter" onchange="this.form.submit()">
                            <option value="">جميع الحالات</option>
                            <option value="pending" <?php echo (isset($_GET['status_filter']) && $_GET['status_filter'] == 'pending') ? 'selected' : ''; ?>>
                                🟡 بانتظار التوصيل
                            </option>
                            <option value="delivered" <?php echo (isset($_GET['status_filter']) && $_GET['status_filter'] == 'delivered') ? 'selected' : ''; ?>>
                                ✅ تم التوصيل
                            </option>
                            <option value="rejected" <?php echo (isset($_GET['status_filter']) && $_GET['status_filter'] == 'rejected') ? 'selected' : ''; ?>>
                                ❌ مرفوض
                            </option>
                            <option value="postponed" <?php echo (isset($_GET['status_filter']) && $_GET['status_filter'] == 'postponed') ? 'selected' : ''; ?>>
                                ⏳ مؤجل
                            </option>
                            <option value="exchange" <?php echo (isset($_GET['status_filter']) && $_GET['status_filter'] == 'exchange') ? 'selected' : ''; ?>>
                                🔄 استبدال
                            </option>
                        </select>
                    </div>

                    <!-- Date Filter -->
                    <div class="hozi-filter-group">
                        <label>الفترة الزمنية:</label>
                        <select name="date_filter" onchange="this.form.submit()">
                            <option value="">جميع التواريخ</option>
                            <option value="today" <?php echo (isset($_GET['date_filter']) && $_GET['date_filter'] == 'today') ? 'selected' : ''; ?>>
                                📅 اليوم
                            </option>
                            <option value="yesterday" <?php echo (isset($_GET['date_filter']) && $_GET['date_filter'] == 'yesterday') ? 'selected' : ''; ?>>
                                📅 أمس
                            </option>
                            <option value="this_week" <?php echo (isset($_GET['date_filter']) && $_GET['date_filter'] == 'this_week') ? 'selected' : ''; ?>>
                                📅 هذا الأسبوع
                            </option>
                            <option value="last_week" <?php echo (isset($_GET['date_filter']) && $_GET['date_filter'] == 'last_week') ? 'selected' : ''; ?>>
                                📅 الأسبوع الماضي
                            </option>
                            <option value="this_month" <?php echo (isset($_GET['date_filter']) && $_GET['date_filter'] == 'this_month') ? 'selected' : ''; ?>>
                                📅 هذا الشهر
                            </option>
                        </select>
                    </div>

                    <!-- Search Filter -->
                    <div class="hozi-filter-group">
                        <label>البحث:</label>
                        <input type="text" name="search_filter"
                               value="<?php echo isset($_GET['search_filter']) ? esc_attr($_GET['search_filter']) : ''; ?>"
                               placeholder="رقم الطلب أو اسم العميل..."
                               onkeypress="if(event.key==='Enter') this.form.submit()">
                    </div>

                    <!-- Action Buttons -->
                    <div class="hozi-filter-group hozi-filter-actions">
                        <button type="submit" class="button button-primary">
                            <span class="dashicons dashicons-search"></span>
                            تطبيق الفلاتر
                        </button>
                        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-delivery-tracking'); ?>" class="button">
                            <span class="dashicons dashicons-dismiss"></span>
                            إزالة الفلاتر
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    <div class="hozi-orders-section">
        <div class="hozi-section-header">
            <h2>📋 الطلبات المكتملة</h2>
            <div class="hozi-header-actions">
                <span class="hozi-results-count">
                    <?php
                    $showing_start = $offset + 1;
                    $showing_end = min($offset + $orders_per_page, $total_orders);
                    echo "عرض {$showing_start}-{$showing_end} من {$total_orders} طلب";
                    if ($filter_active) {
                        echo " (مفلتر)";
                    }
                    ?>
                </span>
                <button type="button" class="button button-primary" onclick="location.reload()">
                    <span class="dashicons dashicons-update"></span>
                    تحديث
                </button>
            </div>
        </div>



        <?php if (!empty($paginated_orders)) : ?>
            <div class="hozi-orders-list">
                <?php foreach ($paginated_orders as $order_data) :
                    // Get WooCommerce order data
                    $wc_order = wc_get_order($order_data->order_id);
                    if ($wc_order) {
                        $customer_name = trim($wc_order->get_billing_first_name() . ' ' . $wc_order->get_billing_last_name());
                        $customer_phone = $wc_order->get_billing_phone();
                        $customer_address = $wc_order->get_billing_address_1() . ' ' . $wc_order->get_billing_address_2();
                        $order_total = $wc_order->get_total();
                    } else {
                        $customer_name = 'غير متوفر';
                        $customer_phone = '';
                        $customer_address = 'غير متوفر';
                        $order_total = '0';
                    }

                    $status_class = '';
                    $status_text = 'بانتظار التوصيل';
                    $status_color = '#ff9800';

                    if ($order_data->delivery_status) {
                        switch ($order_data->delivery_status) {
                            case 'delivered':
                                $status_class = 'delivered';
                                $status_text = 'تم التوصيل';
                                $status_color = '#4caf50';
                                break;
                            case 'rejected':
                                $status_class = 'rejected';
                                $status_text = 'تم الرفض';
                                $status_color = '#f44336';
                                break;
                            case 'postponed':
                                $status_class = 'postponed';
                                $status_text = 'تم التأجيل';
                                $status_color = '#ff9800';
                                break;
                            case 'exchange':
                                $status_class = 'exchange';
                                $status_text = 'طلب استبدال';
                                $status_color = '#2196f3';
                                break;
                        }
                    }
                ?>
                    <?php
                    // Enhanced card styling with progress and animations
                    $card_classes = ['hozi-order-card'];
                    $progress_percent = 50;
                    $status_icon = '🟡';
                    $priority_class = '';

                    if ($order_data->delivery_status) {
                        switch ($order_data->delivery_status) {
                            case 'delivered':
                                $card_classes[] = 'hozi-card-delivered';
                                $progress_percent = 100;
                                $status_icon = '✅';
                                break;
                            case 'rejected':
                                $card_classes[] = 'hozi-card-rejected';
                                $progress_percent = 25;
                                $status_icon = '❌';
                                break;
                            case 'postponed':
                                $card_classes[] = 'hozi-card-postponed';
                                $progress_percent = 75;
                                $status_icon = '⏳';
                                $priority_class = 'hozi-priority-urgent';
                                break;
                            case 'exchange':
                                $card_classes[] = 'hozi-card-exchange';
                                $progress_percent = 60;
                                $status_icon = '🔄';
                                break;
                        }
                    } else {
                        $card_classes[] = 'hozi-card-pending';
                    }

                    $card_class_str = implode(' ', $card_classes);
                    ?>

                    <div class="<?php echo $card_class_str; ?> <?php echo $priority_class; ?>"
                         data-order-id="<?php echo $order_data->order_id; ?>"
                         data-status="<?php echo $order_data->delivery_status ?: 'pending'; ?>">

                        <!-- Progress Bar -->
                        <div class="hozi-progress-container">
                            <div class="hozi-progress-bar">
                                <div class="hozi-progress-fill" style="width: <?php echo $progress_percent; ?>%"></div>
                            </div>
                            <span class="hozi-progress-text"><?php echo $progress_percent; ?>%</span>
                        </div>

                        <div class="hozi-order-header">
                            <div class="hozi-order-title">
                                <div class="hozi-status-icon-container">
                                    <span class="hozi-status-icon"><?php echo $status_icon; ?></span>
                                    <?php if ($priority_class) : ?>
                                        <span class="hozi-priority-badge">عاجل</span>
                                    <?php endif; ?>
                                </div>
                                <div class="hozi-order-info">
                                    <h3>طلب #<?php echo $order_data->order_id; ?></h3>
                                    <span class="hozi-order-date">
                                        <span class="dashicons dashicons-clock"></span>
                                        <?php echo date('Y/m/d H:i', strtotime($order_data->order_date)); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="hozi-order-status-badge">
                                <span class="hozi-status-text"><?php echo $status_text; ?></span>
                                <div class="hozi-status-indicator" style="background-color: <?php echo $status_color; ?>"></div>
                            </div>
                        </div>

                        <div class="hozi-order-details">
                            <div class="hozi-customer-info">
                                <p><strong>العميل:</strong> <?php echo esc_html($customer_name ?: 'غير محدد'); ?></p>
                                <p><strong>الهاتف:</strong>
                                    <?php if ($customer_phone) : ?>
                                        <a href="tel:<?php echo esc_attr($customer_phone); ?>" class="hozi-phone-link">
                                            <?php echo esc_html($customer_phone); ?>
                                        </a>
                                    <?php else : ?>
                                        غير محدد
                                    <?php endif; ?>
                                </p>
                                <p><strong>العنوان:</strong> <?php echo esc_html($customer_address ?: 'غير محدد'); ?></p>
                                <p><strong>المبلغ:</strong> <?php echo wc_price($order_total); ?></p>
                            </div>

                            <?php if ($order_data->delivery_status && $order_data->delivery_notes) : ?>
                                <div class="hozi-delivery-notes">
                                    <p><strong>ملاحظات التوصيل:</strong></p>
                                    <p><?php echo esc_html($order_data->delivery_notes); ?></p>
                                    <small>تاريخ التحديث: <?php echo date('Y/m/d H:i', strtotime($order_data->delivery_date)); ?></small>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="hozi-order-actions">
                            <!-- Customer Contact Actions -->
                            <div class="hozi-actions-section">
                                <h4 class="hozi-actions-title">📞 التواصل مع العميل</h4>
                                <div class="hozi-contact-actions">
                                <?php if ($customer_phone) : ?>
                                    <button type="button" class="hozi-action-btn hozi-call-btn"
                                            onclick="window.open('tel:<?php echo esc_attr($customer_phone); ?>', '_self')"
                                            title="اتصال بالعميل">
                                        <span class="dashicons dashicons-phone"></span>
                                        اتصال
                                    </button>
                                <?php endif; ?>

                                <button type="button" class="hozi-action-btn hozi-whatsapp-btn"
                                        onclick="openWhatsApp('<?php echo esc_attr($customer_phone); ?>', '<?php echo $order_data->order_id; ?>')"
                                        title="واتساب">
                                    <span class="dashicons dashicons-format-chat"></span>
                                    واتساب
                                </button>
                                </div>
                            </div>

                            <!-- Quick Status Actions -->
                            <div class="hozi-actions-section">
                                <h4 class="hozi-actions-title">🚚 حالة التوصيل</h4>
                                <div class="hozi-status-actions">
                                <?php if (!$order_data->delivery_status) : ?>
                                    <!-- Quick Actions for Pending Orders -->
                                    <button type="button" class="hozi-quick-btn hozi-delivered-btn"
                                            onclick="quickUpdateStatus(<?php echo $order_data->order_id; ?>, 'delivered', 'تم التوصيل بنجاح')"
                                            title="تم التوصيل بنجاح">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        ✅ تم التوصيل
                                    </button>

                                    <button type="button" class="hozi-quick-btn hozi-postponed-btn"
                                            onclick="showQuickModal(<?php echo $order_data->order_id; ?>, 'postponed')"
                                            title="تأجيل التوصيل">
                                        <span class="dashicons dashicons-clock"></span>
                                        ⏳ تأجيل
                                    </button>

                                    <button type="button" class="hozi-quick-btn hozi-rejected-btn"
                                            onclick="showQuickModal(<?php echo $order_data->order_id; ?>, 'rejected')"
                                            title="رفض الطلب">
                                        <span class="dashicons dashicons-dismiss"></span>
                                        ❌ رفض
                                    </button>

                                    <button type="button" class="hozi-quick-btn hozi-exchange-btn"
                                            onclick="showQuickModal(<?php echo $order_data->order_id; ?>, 'exchange')"
                                            title="طلب استبدال">
                                        <span class="dashicons dashicons-update"></span>
                                        🔄 استبدال
                                    </button>

                                <?php else : ?>
                                    <!-- Quick Edit Actions for Completed Orders -->
                                    <div class="hozi-completed-status">
                                        <span class="hozi-current-status">
                                            الحالة الحالية:
                                            <strong>
                                                <?php
                                                switch($order_data->delivery_status) {
                                                    case 'delivered': echo '✅ تم التوصيل'; break;
                                                    case 'rejected': echo '❌ مرفوض'; break;
                                                    case 'postponed': echo '⏳ مؤجل'; break;
                                                    case 'exchange': echo '🔄 استبدال'; break;
                                                    default: echo $order_data->delivery_status; break;
                                                }
                                                ?>
                                            </strong>
                                        </span>
                                    </div>

                                    <!-- Small Quick Change Buttons -->
                                    <div class="hozi-quick-change-buttons">
                                        <?php if ($order_data->delivery_status !== 'delivered') : ?>
                                            <button type="button" class="hozi-mini-btn hozi-mini-delivered"
                                                    onclick="quickUpdateStatus(<?php echo $order_data->order_id; ?>, 'delivered', 'تم التوصيل بنجاح')"
                                                    title="تغيير إلى: تم التوصيل">
                                                ✅
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($order_data->delivery_status !== 'postponed') : ?>
                                            <button type="button" class="hozi-mini-btn hozi-mini-postponed"
                                                    onclick="showQuickModal(<?php echo $order_data->order_id; ?>, 'postponed')"
                                                    title="تغيير إلى: مؤجل">
                                                ⏳
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($order_data->delivery_status !== 'rejected') : ?>
                                            <button type="button" class="hozi-mini-btn hozi-mini-rejected"
                                                    onclick="showQuickModal(<?php echo $order_data->order_id; ?>, 'rejected')"
                                                    title="تغيير إلى: مرفوض">
                                                ❌
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($order_data->delivery_status !== 'exchange') : ?>
                                            <button type="button" class="hozi-mini-btn hozi-mini-exchange"
                                                    onclick="showQuickModal(<?php echo $order_data->order_id; ?>, 'exchange')"
                                                    title="تغيير إلى: استبدال">
                                                🔄
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                </div>
                            </div>

                            <!-- Additional Actions -->
                            <div class="hozi-actions-section">
                                <h4 class="hozi-actions-title">⚙️ إجراءات إضافية</h4>
                                <div class="hozi-additional-actions">
                                <a href="<?php echo admin_url('post.php?post=' . $order_data->order_id . '&action=edit'); ?>"
                                   class="hozi-action-btn hozi-view-btn" target="_blank" title="عرض تفاصيل الطلب">
                                    <span class="dashicons dashicons-external"></span>
                                    عرض الطلب
                                </a>

                                <button type="button" class="hozi-action-btn hozi-notes-btn"
                                        onclick="showNotesModal(<?php echo $order_data->order_id; ?>)"
                                        title="إضافة ملاحظات">
                                    <span class="dashicons dashicons-edit-page"></span>
                                    ملاحظات
                                </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1) : ?>
                <div class="hozi-pagination">
                    <div class="hozi-pagination-info">
                        <span>صفحة <?php echo $current_page; ?> من <?php echo $total_pages; ?></span>
                        <span class="hozi-pagination-total">(<?php echo $total_orders; ?> طلب إجمالي)</span>
                    </div>

                    <div class="hozi-pagination-controls">
                        <?php
                        // Build current URL with filters
                        $base_url = admin_url('admin.php?page=hozi-akadly-delivery-tracking');
                        $url_params = array();
                        if ($status_filter) $url_params['status_filter'] = $status_filter;
                        if ($date_filter) $url_params['date_filter'] = $date_filter;
                        if ($search_filter) $url_params['search_filter'] = $search_filter;

                        // Previous page
                        if ($current_page > 1) :
                            $prev_params = $url_params;
                            $prev_params['paged'] = $current_page - 1;
                            $prev_url = $base_url . '&' . http_build_query($prev_params);
                        ?>
                            <a href="<?php echo esc_url($prev_url); ?>" class="hozi-pagination-btn hozi-prev-btn">
                                <span class="dashicons dashicons-arrow-right-alt2"></span>
                                السابق
                            </a>
                        <?php else : ?>
                            <span class="hozi-pagination-btn hozi-prev-btn disabled">
                                <span class="dashicons dashicons-arrow-right-alt2"></span>
                                السابق
                            </span>
                        <?php endif; ?>

                        <!-- Page numbers -->
                        <div class="hozi-page-numbers">
                            <?php
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++) :
                                $page_params = $url_params;
                                $page_params['paged'] = $i;
                                $page_url = $base_url . '&' . http_build_query($page_params);
                            ?>
                                <?php if ($i == $current_page) : ?>
                                    <span class="hozi-page-number active"><?php echo $i; ?></span>
                                <?php else : ?>
                                    <a href="<?php echo esc_url($page_url); ?>" class="hozi-page-number"><?php echo $i; ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </div>

                        <!-- Next page -->
                        <?php if ($current_page < $total_pages) :
                            $next_params = $url_params;
                            $next_params['paged'] = $current_page + 1;
                            $next_url = $base_url . '&' . http_build_query($next_params);
                        ?>
                            <a href="<?php echo esc_url($next_url); ?>" class="hozi-pagination-btn hozi-next-btn">
                                التالي
                                <span class="dashicons dashicons-arrow-left-alt2"></span>
                            </a>
                        <?php else : ?>
                            <span class="hozi-pagination-btn hozi-next-btn disabled">
                                التالي
                                <span class="dashicons dashicons-arrow-left-alt2"></span>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

        <?php else : ?>
            <div class="hozi-no-orders">
                <div class="hozi-no-orders-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <h3>لا توجد طلبات مكتملة</h3>
                <?php if ($is_admin): ?>
                    <p>لا توجد طلبات مؤكدة متاحة للمتابعة حسب إعدادات الوصول الحالية.</p>
                    <p><strong>إعدادات الوصول الحالية:</strong> <?php echo $access_description; ?></p>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-settings'); ?>" class="button button-primary">
                        تعديل إعدادات متابعة التوصيل
                    </a>
                <?php else: ?>
                    <p>لم تقم بتأكيد أي طلبات مكتملة بعد أو لا يحق لك الوصول إلى طلبات أخرى.</p>
                    <p><strong>صلاحيات الوصول:</strong> <?php echo $access_description; ?></p>
                <?php endif; ?>
                <?php if ($filter_active) : ?>
                    <p><strong>تلميح:</strong> جرب إزالة الفلاتر لعرض جميع الطلبات المتاحة.</p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Enhanced Quick Action Modal -->
<div id="hozi-quick-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content hozi-quick-modal-content">
        <div class="hozi-modal-header">
            <h3 id="quick-modal-title">تحديث حالة التوصيل</h3>
            <button type="button" class="hozi-modal-close" onclick="closeQuickModal()">&times;</button>
        </div>

        <form method="post" id="hozi-quick-form">
            <?php wp_nonce_field('hozi_delivery_update'); ?>
            <input type="hidden" name="update_delivery_status" value="1">
            <input type="hidden" name="order_id" id="quick_modal_order_id">

            <div class="hozi-quick-status-selector">
                <div class="hozi-status-options">
                    <label class="hozi-status-option" data-status="delivered">
                        <input type="radio" name="status" value="delivered">
                        <div class="hozi-status-card hozi-delivered">
                            <span class="hozi-status-icon">✅</span>
                            <span class="hozi-status-text">تم التوصيل</span>
                            <span class="hozi-status-desc">تم تسليم الطلب بنجاح</span>
                        </div>
                    </label>

                    <label class="hozi-status-option" data-status="postponed">
                        <input type="radio" name="status" value="postponed">
                        <div class="hozi-status-card hozi-postponed">
                            <span class="hozi-status-icon">⏳</span>
                            <span class="hozi-status-text">تأجيل</span>
                            <span class="hozi-status-desc">تأجيل التوصيل لوقت آخر</span>
                        </div>
                    </label>

                    <label class="hozi-status-option" data-status="rejected">
                        <input type="radio" name="status" value="rejected">
                        <div class="hozi-status-card hozi-rejected">
                            <span class="hozi-status-icon">❌</span>
                            <span class="hozi-status-text">رفض</span>
                            <span class="hozi-status-desc">رفض استلام الطلب</span>
                        </div>
                    </label>

                    <label class="hozi-status-option" data-status="exchange">
                        <input type="radio" name="status" value="exchange">
                        <div class="hozi-status-card hozi-exchange">
                            <span class="hozi-status-icon">🔄</span>
                            <span class="hozi-status-text">استبدال</span>
                            <span class="hozi-status-desc">طلب استبدال المنتج</span>
                        </div>
                    </label>
                </div>
            </div>

            <div class="hozi-form-group">
                <label>ملاحظات إضافية:</label>
                <textarea name="notes" id="quick_modal_notes" rows="3"
                          placeholder="أضف تفاصيل حول حالة التوصيل (اختياري)..."></textarea>
            </div>

            <div class="hozi-modal-actions">
                <button type="submit" class="button button-primary hozi-save-btn">
                    <span class="dashicons dashicons-yes"></span>
                    حفظ التحديث
                </button>
                <button type="button" class="button hozi-cancel-btn" onclick="closeQuickModal()">
                    <span class="dashicons dashicons-no"></span>
                    إلغاء
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Notes Modal -->
<div id="hozi-notes-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content">
        <div class="hozi-modal-header">
            <h3>إضافة ملاحظات</h3>
            <button type="button" class="hozi-modal-close" onclick="closeNotesModal()">&times;</button>
        </div>

        <form method="post" id="hozi-notes-form">
            <?php wp_nonce_field('hozi_delivery_update'); ?>
            <input type="hidden" name="add_notes_only" value="1">
            <input type="hidden" name="order_id" id="notes_modal_order_id">

            <div class="hozi-form-group">
                <label>الملاحظات:</label>
                <textarea name="notes" id="notes_modal_notes" rows="4"
                          placeholder="أضف ملاحظات حول الطلب..." required></textarea>
            </div>

            <div class="hozi-modal-actions">
                <button type="submit" class="button button-primary">
                    <span class="dashicons dashicons-edit"></span>
                    حفظ الملاحظات
                </button>
                <button type="button" class="button" onclick="closeNotesModal()">إلغاء</button>
            </div>
        </form>
    </div>
</div>

<script>
// Quick delivery status update
function updateDeliveryStatus(orderId, status, notes) {
    if (confirm('هل أنت متأكد من تحديث حالة التوصيل؟')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="update_delivery_status" value="1">
            <input type="hidden" name="order_id" value="${orderId}">
            <input type="hidden" name="status" value="${status}">
            <input type="hidden" name="notes" value="${notes}">
            <input type="hidden" name="_wpnonce" value="<?php echo wp_create_nonce('hozi_delivery_update'); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Show update modal
function showUpdateModal(orderId, currentStatus) {
    document.getElementById('modal_order_id').value = orderId;
    document.getElementById('modal_status').value = currentStatus || 'delivered';
    document.getElementById('modal_notes').value = '';
    document.getElementById('hozi-update-modal').style.display = 'block';
}

// Close update modal
function closeUpdateModal() {
    document.getElementById('hozi-update-modal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('hozi-update-modal');
    if (event.target == modal) {
        closeUpdateModal();
    }
}

// Toggle filters visibility
function toggleFilters() {
    const container = document.getElementById('filtersContainer');
    const button = document.querySelector('.hozi-toggle-filters');

    if (container.classList.contains('active')) {
        container.classList.remove('active');
        button.innerHTML = '<span class="dashicons dashicons-filter"></span> إظهار الفلاتر';
    } else {
        container.classList.add('active');
        button.innerHTML = '<span class="dashicons dashicons-filter"></span> إخفاء الفلاتر';
    }
}

// Auto-show filters if any filter is active
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const hasFilters = urlParams.get('status_filter') || urlParams.get('date_filter') || urlParams.get('search_filter');

    if (hasFilters) {
        const container = document.getElementById('filtersContainer');
        const button = document.querySelector('.hozi-toggle-filters');
        container.classList.add('active');
        button.innerHTML = '<span class="dashicons dashicons-filter"></span> إخفاء الفلاتر';
    }
});

// Quick status update (one-click for delivered)
function quickUpdateStatus(orderId, status, notes) {
    if (confirm('هل أنت متأكد من تحديث حالة الطلب إلى "' + notes + '"؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="update_delivery_status" value="1">
            <input type="hidden" name="order_id" value="${orderId}">
            <input type="hidden" name="status" value="${status}">
            <input type="hidden" name="notes" value="${notes}">
            <?php echo wp_nonce_field('hozi_delivery_update', '_wpnonce', true, false); ?>
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Show enhanced quick modal
function showQuickModal(orderId, currentStatus = '') {
    document.getElementById('quick_modal_order_id').value = orderId;
    document.getElementById('quick_modal_notes').value = '';

    // Update modal title based on current status
    const title = currentStatus ? 'تعديل حالة التوصيل' : 'تحديث حالة التوصيل';
    document.getElementById('quick-modal-title').textContent = title;

    // Pre-select current status if editing
    if (currentStatus) {
        const radioButton = document.querySelector(`input[name="status"][value="${currentStatus}"]`);
        if (radioButton) {
            radioButton.checked = true;
        }
    } else {
        // Clear all selections for new status
        document.querySelectorAll('input[name="status"]').forEach(radio => {
            radio.checked = false;
        });
    }

    document.getElementById('hozi-quick-modal').style.display = 'flex';
}

// Close quick modal
function closeQuickModal() {
    document.getElementById('hozi-quick-modal').style.display = 'none';
}

// Show notes modal
function showNotesModal(orderId) {
    document.getElementById('notes_modal_order_id').value = orderId;
    document.getElementById('notes_modal_notes').value = '';
    document.getElementById('hozi-notes-modal').style.display = 'flex';
}

// Close notes modal
function closeNotesModal() {
    document.getElementById('hozi-notes-modal').style.display = 'none';
}

// WhatsApp integration
function openWhatsApp(phone, orderId) {
    if (!phone) {
        alert('رقم الهاتف غير متوفر');
        return;
    }

    // Clean phone number
    const cleanPhone = phone.replace(/[^\d+]/g, '');
    const message = `مرحباً، بخصوص طلبكم رقم ${orderId} من متجرنا. كيف يمكنني مساعدتكم؟`;
    const whatsappUrl = `https://wa.me/${cleanPhone}?text=${encodeURIComponent(message)}`;

    window.open(whatsappUrl, '_blank');
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('hozi-modal')) {
        e.target.style.display = 'none';
    }
});

// Form submission with loading state
document.getElementById('hozi-quick-form').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('.hozi-save-btn');
    submitBtn.innerHTML = '<span class="dashicons dashicons-update"></span> جاري الحفظ...';
    submitBtn.disabled = true;
});

document.getElementById('hozi-notes-form').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<span class="dashicons dashicons-update"></span> جاري الحفظ...';
    submitBtn.disabled = true;
});
</script>

<style>
.hozi-delivery-tracking-wrap {
    background: #f8f9fa;
    margin: 0 0 0 -20px;
    padding: 20px;
    min-height: 100vh;
}

/* Navigation Tabs */
.hozi-nav-tabs {
    display: flex;
    gap: 0;
    margin: 20px 0;
    border-bottom: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    text-decoration: none;
    color: #666;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    transition: all 0.3s ease;
    font-weight: 500;
    flex: 1;
    justify-content: center;
    flex-direction: column;
}

.hozi-step-number {
    background: #0073aa;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}

.hozi-nav-tab-active .hozi-step-number {
    background: white;
    color: #0073aa;
}

.hozi-nav-tab:last-child {
    border-right: none;
}

.hozi-nav-tab:hover {
    background: #e9ecef;
    color: #333;
    text-decoration: none;
}

.hozi-nav-tab-active {
    background: #0073aa !important;
    color: white !important;
    font-weight: 600;
}

.hozi-nav-tab-active:hover {
    background: #005a87 !important;
    color: white !important;
}

/* Advanced Filters Styles */
.hozi-filters-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.hozi-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.hozi-filters-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.hozi-toggle-filters {
    background: #0073aa;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    transition: background 0.3s ease;
}

.hozi-toggle-filters:hover {
    background: #005a87;
}

.hozi-filters-container {
    padding: 20px;
    display: none;
}

.hozi-filters-container.active {
    display: block;
}

.hozi-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: end;
}

.hozi-filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.hozi-filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.hozi-filter-group select,
.hozi-filter-group input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.hozi-filter-group select:focus,
.hozi-filter-group input[type="text"]:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px rgba(0, 115, 170, 0.2);
}

.hozi-filter-actions {
    display: flex;
    gap: 10px;
    flex-direction: row !important;
}

.hozi-filter-actions .button {
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
}

.hozi-header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.hozi-results-count {
    background: #e7f3ff;
    color: #0073aa;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

/* Filtered stats indicator */
.hozi-stat-card.filtered {
    border-left: 4px solid #ff9800;
    background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
}

.hozi-stat-card.filtered .hozi-stat-content h3 {
    color: #ff9800;
}

/* Enhanced Action Buttons */
.hozi-order-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.hozi-contact-actions,
.hozi-status-actions,
.hozi-additional-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.hozi-actions-section {
    margin-bottom: 15px;
}

.hozi-actions-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #0073aa;
    text-align: center;
}

.hozi-status-actions {
    padding: 10px 0;
}

.hozi-action-btn,
.hozi-quick-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

/* Contact Action Buttons */
.hozi-call-btn {
    background: #28a745;
    color: white;
}

.hozi-call-btn:hover {
    background: #218838;
    color: white;
}

.hozi-whatsapp-btn {
    background: #25d366;
    color: white;
}

.hozi-whatsapp-btn:hover {
    background: #128c7e;
    color: white;
}

/* Quick Status Buttons */
.hozi-delivered-btn {
    background: #28a745;
    color: white;
    font-weight: bold;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.hozi-delivered-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.hozi-postponed-btn {
    background: #ffc107;
    color: #333;
    font-weight: bold;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.hozi-postponed-btn:hover {
    background: #e0a800;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.4);
}

.hozi-rejected-btn {
    background: #dc3545;
    color: white;
    font-weight: bold;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.hozi-rejected-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

.hozi-exchange-btn {
    background: #17a2b8;
    color: white;
    font-weight: bold;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.hozi-exchange-btn:hover {
    background: #138496;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
}

/* Completed Status Display */
.hozi-completed-status {
    margin-bottom: 10px;
}

.hozi-current-status {
    font-size: 13px;
    color: #666;
    display: block;
    margin-bottom: 8px;
    text-align: center;
}

.hozi-current-status strong {
    color: #333;
}

/* Mini Quick Change Buttons */
.hozi-quick-change-buttons {
    display: flex;
    gap: 6px;
    justify-content: center;
    flex-wrap: wrap;
}

.hozi-mini-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.hozi-mini-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.hozi-mini-delivered {
    background: #28a745;
    color: white;
}

.hozi-mini-delivered:hover {
    background: #218838;
}

.hozi-mini-postponed {
    background: #ffc107;
    color: #333;
}

.hozi-mini-postponed:hover {
    background: #e0a800;
}

.hozi-mini-rejected {
    background: #dc3545;
    color: white;
}

.hozi-mini-rejected:hover {
    background: #c82333;
}

.hozi-mini-exchange {
    background: #17a2b8;
    color: white;
}

.hozi-mini-exchange:hover {
    background: #138496;
}

.hozi-postponed-btn {
    background: #ffc107;
    color: #212529;
}

.hozi-postponed-btn:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.hozi-rejected-btn {
    background: #dc3545;
    color: white;
}

.hozi-rejected-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.hozi-exchange-btn {
    background: #17a2b8;
    color: white;
}

.hozi-exchange-btn:hover {
    background: #138496;
    transform: translateY(-1px);
}

/* Status Badges */
.hozi-completed-status {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-status-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.hozi-status-delivered {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.hozi-status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hozi-status-postponed {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.hozi-status-exchange {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Additional Action Buttons */
.hozi-view-btn {
    background: #6c757d;
    color: white;
}

.hozi-view-btn:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

.hozi-notes-btn {
    background: #007cba;
    color: white;
}

.hozi-notes-btn:hover {
    background: #005a87;
}

.hozi-edit-btn {
    background: #0073aa;
    color: white;
    padding: 6px 10px;
    font-size: 11px;
}

.hozi-edit-btn:hover {
    background: #005a87;
}

/* Enhanced Modal Styles */
.hozi-quick-modal-content {
    max-width: 600px;
    width: 90%;
}

.hozi-quick-status-selector {
    margin: 20px 0;
}

.hozi-status-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.hozi-status-option {
    cursor: pointer;
    display: block;
}

.hozi-status-option input[type="radio"] {
    display: none;
}

.hozi-status-card {
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
    position: relative;
}

.hozi-status-card:hover {
    border-color: #0073aa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.hozi-status-option input[type="radio"]:checked + .hozi-status-card {
    border-color: #0073aa;
    background: #f0f8ff;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.hozi-status-icon {
    font-size: 24px;
    display: block;
    margin-bottom: 8px;
}

.hozi-status-text {
    font-weight: 600;
    font-size: 14px;
    display: block;
    margin-bottom: 4px;
    color: #333;
}

.hozi-status-desc {
    font-size: 11px;
    color: #666;
    display: block;
}

/* Status-specific colors */
.hozi-status-card.hozi-delivered:hover,
.hozi-status-option input[type="radio"]:checked + .hozi-status-card.hozi-delivered {
    border-color: #28a745;
    background: #f8fff9;
}

.hozi-status-card.hozi-postponed:hover,
.hozi-status-option input[type="radio"]:checked + .hozi-status-card.hozi-postponed {
    border-color: #ffc107;
    background: #fffef7;
}

.hozi-status-card.hozi-rejected:hover,
.hozi-status-option input[type="radio"]:checked + .hozi-status-card.hozi-rejected {
    border-color: #dc3545;
    background: #fff8f8;
}

.hozi-status-card.hozi-exchange:hover,
.hozi-status-option input[type="radio"]:checked + .hozi-status-card.hozi-exchange {
    border-color: #17a2b8;
    background: #f7fcfd;
}

/* Modal Action Buttons */
.hozi-save-btn {
    background: #28a745 !important;
    border-color: #28a745 !important;
}

.hozi-save-btn:hover {
    background: #218838 !important;
    border-color: #1e7e34 !important;
}

.hozi-cancel-btn {
    background: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

.hozi-cancel-btn:hover {
    background: #5a6268 !important;
    border-color: #545b62 !important;
}

/* Mobile Responsive for Filters */
@media (max-width: 768px) {
    .hozi-filters-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .hozi-filter-actions {
        flex-direction: column !important;
    }

    .hozi-filter-actions .button {
        width: 100%;
        justify-content: center;
    }

    .hozi-header-actions {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    /* Mobile responsive for action buttons */
    .hozi-order-actions {
        gap: 10px;
    }

    .hozi-contact-actions,
    .hozi-status-actions,
    .hozi-additional-actions {
        flex-direction: column;
        gap: 8px;
    }

    .hozi-action-btn,
    .hozi-quick-btn {
        justify-content: center;
        padding: 10px 15px;
        font-size: 13px;
    }

    .hozi-completed-status {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .hozi-status-badge {
        text-align: center;
    }

    /* Mobile modal adjustments */
    .hozi-status-options {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .hozi-status-card {
        padding: 12px 8px;
    }

    .hozi-status-icon {
        font-size: 20px;
        margin-bottom: 6px;
    }

    .hozi-status-text {
        font-size: 12px;
    }

    .hozi-status-desc {
        font-size: 10px;
    }
}

.hozi-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #0073aa;
}

.hozi-header h1 {
    margin: 0 0 10px 0;
    color: #0073aa;
}

.hozi-nav-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.hozi-nav-tab {
    background: white;
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    color: #666;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.hozi-nav-tab:hover {
    background: #f0f0f0;
    color: #333;
}

.hozi-nav-tab-active {
    background: #0073aa !important;
    color: white !important;
    border-color: #0073aa !important;
}

.hozi-stats-section {
    margin-bottom: 20px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.hozi-stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease;
}

.hozi-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.hozi-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.hozi-total-card .hozi-stat-icon { background: #2196f3; }
.hozi-pending-card .hozi-stat-icon { background: #ff9800; }
.hozi-delivered-card .hozi-stat-icon { background: #4caf50; }
.hozi-issues-card .hozi-stat-icon { background: #f44336; }

.hozi-stat-content h3 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
}

.hozi-stat-content p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 14px;
}

.hozi-orders-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.hozi-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.hozi-section-header h2 {
    margin: 0;
    color: #333;
}

.hozi-orders-list {
    display: grid;
    gap: 15px;
}

/* Enhanced Order Cards with Colors and Animations */
.hozi-order-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 4px solid #0073aa;
    position: relative;
    transform: translateY(0);
}

.hozi-order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* Status-based Card Colors */
.hozi-card-pending {
    border-left-color: #ff9800;
    background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
}

.hozi-card-delivered {
    border-left-color: #4caf50;
    background: linear-gradient(135deg, #f1f8e9 0%, #ffffff 100%);
}

.hozi-card-rejected {
    border-left-color: #f44336;
    background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
}

.hozi-card-postponed {
    border-left-color: #ff9800;
    background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
    animation: pulse-urgent 2s infinite;
}

.hozi-card-exchange {
    border-left-color: #2196f3;
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
}

/* Priority Urgent Animation */
@keyframes pulse-urgent {
    0% { box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
    50% { box-shadow: 0 4px 16px rgba(255, 152, 0, 0.3); }
    100% { box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
}

.hozi-priority-urgent {
    position: relative;
}

.hozi-priority-urgent::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff9800, #ff5722, #ff9800);
    animation: priority-glow 1.5s ease-in-out infinite;
}

@keyframes priority-glow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Progress Bar */
.hozi-progress-container {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    background: rgba(0,0,0,0.02);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.hozi-progress-bar {
    flex: 1;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
}

.hozi-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 3px;
    transition: width 0.8s ease;
    position: relative;
}

.hozi-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.hozi-progress-text {
    font-size: 11px;
    font-weight: 600;
    color: #666;
    min-width: 35px;
    text-align: right;
}

/* Enhanced Header */
.hozi-order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 15px 20px;
    gap: 15px;
}

.hozi-order-title {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    flex: 1;
}

.hozi-status-icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.hozi-status-icon {
    font-size: 24px;
    animation: icon-bounce 2s infinite;
}

@keyframes icon-bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-2px); }
}

.hozi-priority-badge {
    background: #ff5722;
    color: white;
    font-size: 9px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
    animation: badge-pulse 1s infinite;
}

@keyframes badge-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.hozi-order-info h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.hozi-order-date {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #666;
}

.hozi-order-status-badge {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.hozi-status-text {
    font-size: 12px;
    font-weight: 600;
    color: #333;
}

.hozi-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: indicator-pulse 2s infinite;
}

@keyframes indicator-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Pagination Styles */
.hozi-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.hozi-pagination-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    color: #666;
    font-size: 14px;
}

.hozi-pagination-total {
    font-size: 12px;
    color: #999;
}

.hozi-pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.hozi-pagination-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    background: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-pagination-btn:hover {
    background: #005a87;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.hozi-pagination-btn.disabled {
    background: #ccc;
    color: #999;
    cursor: not-allowed;
    transform: none;
}

.hozi-pagination-btn.disabled:hover {
    background: #ccc;
    color: #999;
    transform: none;
}

.hozi-page-numbers {
    display: flex;
    gap: 5px;
}

.hozi-page-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background: white;
    color: #666;
    text-decoration: none;
    border-radius: 6px;
    border: 1px solid #ddd;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-page-number:hover {
    background: #0073aa;
    color: white;
    text-decoration: none;
    border-color: #0073aa;
}

.hozi-page-number.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
    font-weight: 600;
}

/* Mobile Pagination */
@media (max-width: 768px) {
    .hozi-pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .hozi-pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .hozi-page-numbers {
        order: -1;
        margin-bottom: 10px;
    }

    .hozi-pagination-btn {
        flex: 1;
        min-width: 100px;
        justify-content: center;
    }
}

.hozi-customer-info p {
    margin: 8px 0;
    color: #555;
}

.hozi-phone-link {
    color: #0073aa;
    text-decoration: none;
    font-weight: bold;
}

.hozi-phone-link:hover {
    text-decoration: underline;
}

.hozi-delivery-notes {
    background: #f0f8ff;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    border-left: 3px solid #0073aa;
}

.hozi-order-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.hozi-quick-btn, .hozi-edit-btn, .hozi-view-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.hozi-delivered-btn {
    background: #4caf50;
    color: white;
}

.hozi-delivered-btn:hover {
    background: #45a049;
}

.hozi-rejected-btn {
    background: #f44336;
    color: white;
}

.hozi-rejected-btn:hover {
    background: #da190b;
}

.hozi-postponed-btn {
    background: #ff9800;
    color: white;
}

.hozi-postponed-btn:hover {
    background: #e68900;
}

.hozi-edit-btn {
    background: #2196f3;
    color: white;
}

.hozi-edit-btn:hover {
    background: #1976d2;
}

.hozi-view-btn {
    background: #666;
    color: white;
}

.hozi-view-btn:hover {
    background: #555;
    color: white;
    text-decoration: none;
}

.hozi-no-orders {
    text-align: center;
    padding: 40px;
    color: #666;
}

.hozi-no-orders-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* Modal Styles */
.hozi-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.hozi-modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.hozi-modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hozi-modal-header h3 {
    margin: 0;
    color: #333;
}

.hozi-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.hozi-modal-close:hover {
    color: #333;
}

.hozi-form-group {
    margin: 20px;
}

.hozi-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.hozi-form-group select,
.hozi-form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.hozi-modal-actions {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.hozi-order-card.delivered {
    border-left: 4px solid #4caf50;
}

.hozi-order-card.rejected {
    border-left: 4px solid #f44336;
}

.hozi-order-card.postponed {
    border-left: 4px solid #ff9800;
}

.hozi-order-card.exchange {
    border-left: 4px solid #2196f3;
}

@media (max-width: 768px) {
    .hozi-delivery-tracking-wrap {
        padding: 10px;
        margin: 0 0 0 -10px;
    }

    .hozi-nav-tabs {
        flex-direction: column;
    }

    .hozi-stats-grid {
        grid-template-columns: 1fr;
    }

    .hozi-order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .hozi-order-actions {
        flex-direction: column;
    }

    .hozi-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>

<script>
function updateDeliveryStatus(orderId, status, notes) {
    if (confirm('هل أنت متأكد من تحديث حالة التوصيل؟')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        // Add nonce
        const nonce = document.createElement('input');
        nonce.type = 'hidden';
        nonce.name = '_wpnonce';
        nonce.value = '<?php echo wp_create_nonce('hozi_delivery_update'); ?>';
        form.appendChild(nonce);

        // Add action
        const action = document.createElement('input');
        action.type = 'hidden';
        action.name = 'update_delivery_status';
        action.value = '1';
        form.appendChild(action);

        // Add order ID
        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;
        form.appendChild(orderIdInput);

        // Add status
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = status;
        form.appendChild(statusInput);

        // Add notes
        const notesInput = document.createElement('input');
        notesInput.type = 'hidden';
        notesInput.name = 'notes';
        notesInput.value = notes;
        form.appendChild(notesInput);

        document.body.appendChild(form);
        form.submit();
    }
}

function showUpdateModal(orderId, currentStatus) {
    document.getElementById('modal_order_id').value = orderId;
    document.getElementById('modal_status').value = currentStatus || 'delivered';
    document.getElementById('modal_notes').value = '';
    document.getElementById('hozi-update-modal').style.display = 'flex';
}

function closeUpdateModal() {
    document.getElementById('hozi-update-modal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('hozi-update-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeUpdateModal();
    }
});

/* Access Control Styles */
.hozi-access-info {
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.hozi-access-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    max-width: fit-content;
}

.hozi-access-badge.admin {
    background: #e8f5e8;
    color: #2d5a2d;
    border: 1px solid #c3e6cb;
}

.hozi-access-badge.agent {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.hozi-admin-note {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 10px;
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    font-size: 12px;
    max-width: fit-content;
}

.hozi-admin-note .dashicons {
    font-size: 14px;
}

.hozi-admin-nav {
    margin: 20px 0;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-admin-nav .button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

@media (max-width: 768px) {
    .hozi-access-info {
        margin-top: 10px;
    }

    .hozi-access-badge,
    .hozi-admin-note {
        font-size: 11px;
        padding: 6px 8px;
    }

    .hozi-access-badge .dashicons,
    .hozi-admin-note .dashicons {
        font-size: 12px;
    }
}
</script>
