# 🧪 دليل اختبار إضافة Hozi Akadly

## 📋 خطوات الاختبار السريع

### **المرحلة 1: التثبيت والإعداد**

#### **1. رفع الإضافة**
```bash
# ارفع مجلد hozi-akadly إلى:
/wp-content/plugins/hozi-akadly/
```

#### **2. تفعيل الإضافة**
1. ادخل إلى: `https://elina-shop.online/wp-admin`
2. انتقل إلى **الإضافات > الإضافات المثبتة**
3. ابحث عن "Hozi Akadly - أكدلي"
4. اضغط **تفعيل**

#### **3. التحقق من التثبيت**
- يجب أن تظهر قائمة "أكدلي" في القائمة الجانبية
- تحقق من وجود الجداول في قاعدة البيانات:
  - `wp_hozi_agents`
  - `wp_hozi_order_assignments`
  - `wp_hozi_confirmation_logs`

### **المرحلة 2: إنشاء وكيل تجريبي**

#### **1. إنشاء مستخدم جديد**
1. انتقل إلى **المستخدمون > إضافة جديد**
2. املأ البيانات:
   ```
   اسم المستخدم: test_agent
   البريد الإلكتروني: <EMAIL>
   كلمة المرور: TestAgent123!
   الدور: Subscriber
   ```
3. اضغط **إضافة مستخدم جديد**

#### **2. تحويل المستخدم إلى وكيل**
1. انتقل إلى **أكدلي > الوكلاء**
2. اضغط **إضافة وكيل جديد**
3. املأ النموذج:
   ```
   المستخدم: test_agent (<EMAIL>)
   اسم الوكيل: أحمد التجريبي
   رقم الهاتف: 0501234567
   الحد الأقصى للطلبات: 0
   ```
4. اضغط **إضافة وكيل**

### **المرحلة 3: ضبط الإعدادات**

#### **1. إعدادات التوزيع**
1. انتقل إلى **أكدلي > الإعدادات**
2. اختر الإعدادات التالية:
   ```
   طريقة التوزيع: ✅ توزيع دوري تلقائي
   التخصيص التلقائي: ✅ مفعل
   حالات الطلبات: ✅ pending ✅ processing
   ```
3. اضغط **حفظ الإعدادات**

### **المرحلة 4: اختبار النظام**

#### **1. إنشاء طلب تجريبي**
1. انتقل إلى موقعك الأمامي
2. أضف منتج إلى السلة
3. أكمل عملية الشراء بالبيانات التالية:
   ```
   الاسم: عميل تجريبي
   الهاتف: 0509876543
   العنوان: الرياض، السعودية
   ```
4. أكمل الطلب

#### **2. التحقق من التخصيص التلقائي**
1. ارجع إلى لوحة التحكم
2. انتقل إلى **أكدلي > لوحة التحكم**
3. تحقق من الإحصائيات
4. انتقل إلى **WooCommerce > الطلبات**
5. تحقق من ظهور عمود "وكيل التأكيد" و "حالة التأكيد"

### **المرحلة 5: اختبار واجهة الوكيل**

#### **1. تسجيل دخول الوكيل**
1. اخرج من حساب المشرف
2. ادخل بحساب الوكيل:
   ```
   الرابط: https://elina-shop.online/wp-admin
   اسم المستخدم: test_agent
   كلمة المرور: TestAgent123!
   ```

#### **2. اختبار واجهة الوكيل**
- يجب أن يتم توجيهك تلقائياً إلى صفحة "طلباتي"
- يجب أن ترى الطلب المخصص لك
- تحقق من ظهور:
  - ✅ معلومات العميل (الاسم، الهاتف، العنوان)
  - ✅ تفاصيل المنتجات والأسعار
  - ✅ أزرار التأكيد (تم التأكيد، لم يرد، إعادة الاتصال، رفض)
  - ✅ حقل الملاحظات

#### **3. اختبار تأكيد الطلب**
1. اضغط على **تم التأكيد**
2. أضف ملاحظة: "تم التأكيد مع العميل هاتفياً"
3. أكد العملية
4. تحقق من:
   - ✅ اختفاء الطلب من قائمة الطلبات المعلقة
   - ✅ تحديث الإحصائيات
   - ✅ تغيير حالة الطلب في WooCommerce

### **المرحلة 6: اختبار التوزيع اليدوي**

#### **1. إنشاء وكيل ثاني**
1. أنشئ مستخدم جديد: `test_agent2`
2. حوله إلى وكيل: "سارة التجريبية"

#### **2. اختبار التوزيع اليدوي**
1. أنشئ طلب جديد
2. انتقل إلى **أكدلي > توزيع الطلبات**
3. حدد الطلب الجديد
4. اختر وكيل محدد
5. اضغط **تخصيص المحدد**

### **المرحلة 7: اختبار الحالات المختلفة**

#### **1. اختبار "لم يرد"**
1. ادخل كوكيل
2. اختر طلب واضغط **لم يرد**
3. أضف ملاحظة: "لم يرد العميل على 3 مكالمات"

#### **2. اختبار "إعادة الاتصال"**
1. اختر طلب واضغط **إعادة الاتصال**
2. أضف ملاحظة: "طلب العميل الاتصال بعد الساعة 6 مساءً"

#### **3. اختبار "رفض"**
1. اختر طلب واضغط **رفض**
2. أضف ملاحظة: "العميل غير مهتم بالمنتج"

## ✅ **قائمة التحقق النهائية**

### **الوظائف الأساسية:**
- [ ] تثبيت وتفعيل الإضافة بنجاح
- [ ] إنشاء وكلاء جدد
- [ ] تخصيص الطلبات تلقائياً
- [ ] تخصيص الطلبات يدوياً
- [ ] واجهة الوكيل تعمل بشكل صحيح
- [ ] جميع أزرار التأكيد تعمل
- [ ] تحديث حالات الطلبات في WooCommerce
- [ ] الإحصائيات تتحدث بشكل صحيح

### **واجهة المستخدم:**
- [ ] التصميم متجاوب على الجوال
- [ ] النصوص باللغة العربية
- [ ] الأيقونات والألوان واضحة
- [ ] سهولة الاستخدام للوكلاء

### **الأمان:**
- [ ] الوكلاء يرون طلباتهم فقط
- [ ] صلاحيات محددة لكل دور
- [ ] حماية من CSRF
- [ ] تسجيل جميع الإجراءات

## 🐛 **المشاكل المحتملة وحلولها**

### **مشكلة: الإضافة لا تظهر**
```
الحل: تحقق من:
- رفع الملفات في المكان الصحيح
- صلاحيات الملفات (755 للمجلدات، 644 للملفات)
- عدم وجود أخطاء PHP
```

### **مشكلة: الوكيل لا يرى الطلبات**
```
الحل: تحقق من:
- تم إنشاء الوكيل بشكل صحيح
- الطلبات مخصصة للوكيل
- الوكيل نشط (is_active = 1)
```

### **مشكلة: التخصيص التلقائي لا يعمل**
```
الحل: تحقق من:
- الإعدادات مفعلة
- يوجد وكلاء نشطون
- حالة الطلب ضمن الحالات المحددة
```

## 📞 **الدعم**

إذا واجهت أي مشكلة:
1. فعّل وضع التصحيح في WordPress
2. راجع سجل الأخطاء
3. تحقق من متطلبات النظام
4. تأكد من تفعيل WooCommerce

---

**ملاحظة**: هذا دليل اختبار شامل للتأكد من عمل جميع وظائف الإضافة بشكل صحيح.
