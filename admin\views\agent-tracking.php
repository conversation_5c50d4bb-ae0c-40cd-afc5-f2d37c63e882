<?php
/**
 * Agent Tracking Page - Enhanced Interface
 */

if (!defined('ABSPATH')) {
    exit;
}

// Ensure required variables are defined
if (!isset($confirmed_orders)) {
    $confirmed_orders = array();
}

if (!isset($tracking_stats)) {
    $tracking_stats = (object) array(
        'total_confirmed' => 0,
        'total_tracked' => 0,
        'delivered' => 0,
        'rejected' => 0,
        'postponed' => 0,
        'exchange' => 0
    );
}

// Get tracking statuses and reasons
$tracking_statuses = Hozi_Akadly_Order_Tracker::get_tracking_statuses();
$reason_categories = Hozi_Akadly_Order_Tracker::get_reason_categories();
?>

<div class="wrap hozi-agent-tracking-wrap">
    <!-- Simple Header -->
    <div class="hozi-simple-header">
        <h1><?php _e('تتبع طلباتي المؤكدة', 'hozi-akadly'); ?></h1>
        <p class="hozi-tracking-subtitle"><?php _e('متابعة وتحديث حالة الطلبيات التي قمت بتأكيدها', 'hozi-akadly'); ?></p>
    </div>

    <!-- Header Actions - Mobile Friendly -->
    <div class="hozi-header-actions-mobile">
        <button type="button" class="button button-primary hozi-refresh-btn" id="refresh-data">
            <span class="dashicons dashicons-update"></span>
            <?php _e('تحديث البيانات', 'hozi-akadly'); ?>
        </button>
    </div>

    <!-- Navigation Tabs -->
    <div class="hozi-nav-tabs">
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-orders'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-cart"></span>
            <?php _e('الطلبات المخصصة لي', 'hozi-akadly'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-tracking'); ?>" class="hozi-nav-tab hozi-nav-tab-active">
            <span class="dashicons dashicons-visibility"></span>
            <?php _e('تتبع طلباتي المؤكدة', 'hozi-akadly'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-archived'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-archive"></span>
            <?php _e('الطلبات المؤرشفة', 'hozi-akadly'); ?>
        </a>
    </div>

    <!-- Statistics Section -->
    <div class="hozi-tracking-stats-section">
        <h2><?php _e('إحصائيات سريعة', 'hozi-akadly'); ?></h2>

        <div class="hozi-stats-grid">
            <!-- Total Orders Card -->
            <div class="hozi-stat-card hozi-total-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats->total_confirmed ?? 0); ?></h3>
                    <p><?php _e('إجمالي الطلبيات المؤكدة', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-period"><?php _e('منذ بداية العمل', 'hozi-akadly'); ?></span>
                </div>
            </div>

            <!-- Tracked Orders Card -->
            <div class="hozi-stat-card hozi-tracked-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-visibility"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats->total_tracked ?? 0); ?></h3>
                    <p><?php _e('طلبيات تم تتبعها', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-percentage">
                        <?php
                        $total = $tracking_stats->total_confirmed ?? 0;
                        $tracked = $tracking_stats->total_tracked ?? 0;
                        $percentage = $total > 0 ? round(($tracked / $total) * 100, 1) : 0;
                        printf(__('%s%% من الإجمالي', 'hozi-akadly'), $percentage);
                        ?>
                    </span>
                </div>
            </div>

            <!-- Delivered Orders Card -->
            <div class="hozi-stat-card hozi-delivered-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats->delivered ?? 0); ?></h3>
                    <p><?php _e('تم التوصيل', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-percentage">
                        <?php
                        $delivered_percentage = $tracked > 0 ? round((($tracking_stats->delivered ?? 0) / $tracked) * 100, 1) : 0;
                        printf(__('%s%% من المتتبعة', 'hozi-akadly'), $delivered_percentage);
                        ?>
                    </span>
                </div>
            </div>

            <!-- Rejected Orders Card -->
            <div class="hozi-stat-card hozi-rejected-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-no"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats->rejected ?? 0); ?></h3>
                    <p><?php _e('مرفوضة', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-percentage">
                        <?php
                        $rejected_percentage = $tracked > 0 ? round((($tracking_stats->rejected ?? 0) / $tracked) * 100, 1) : 0;
                        printf(__('%s%% من المتتبعة', 'hozi-akadly'), $rejected_percentage);
                        ?>
                    </span>
                </div>
            </div>

            <!-- Postponed Orders Card -->
            <div class="hozi-stat-card hozi-postponed-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-clock"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats->postponed ?? 0); ?></h3>
                    <p><?php _e('مؤجلة', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-percentage">
                        <?php
                        $postponed_percentage = $tracked > 0 ? round((($tracking_stats->postponed ?? 0) / $tracked) * 100, 1) : 0;
                        printf(__('%s%% من المتتبعة', 'hozi-akadly'), $postponed_percentage);
                        ?>
                    </span>
                </div>
            </div>

            <!-- Exchange Orders Card -->
            <div class="hozi-stat-card hozi-exchange-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-update"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($tracking_stats->exchange ?? 0); ?></h3>
                    <p><?php _e('استبدال', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-percentage">
                        <?php
                        $exchange_percentage = $tracked > 0 ? round((($tracking_stats->exchange ?? 0) / $tracked) * 100, 1) : 0;
                        printf(__('%s%% من المتتبعة', 'hozi-akadly'), $exchange_percentage);
                        ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Performance Indicators -->
        <div class="hozi-performance-section">
            <h3><?php _e('مؤشرات الأداء', 'hozi-akadly'); ?></h3>
            <div class="hozi-performance-grid">
                <!-- Success Rate -->
                <div class="hozi-performance-card">
                    <div class="hozi-performance-header">
                        <span class="hozi-performance-label"><?php _e('معدل النجاح', 'hozi-akadly'); ?></span>
                        <span class="hozi-performance-value hozi-success-rate">
                            <?php echo esc_html($delivered_percentage); ?>%
                        </span>
                    </div>
                    <div class="hozi-progress-bar">
                        <div class="hozi-progress-fill hozi-success-fill" style="width: <?php echo esc_attr($delivered_percentage); ?>%"></div>
                    </div>
                    <div class="hozi-performance-note">
                        <?php _e('نسبة الطلبيات المكتملة بنجاح', 'hozi-akadly'); ?>
                    </div>
                </div>

                <!-- Tracking Rate -->
                <div class="hozi-performance-card">
                    <div class="hozi-performance-header">
                        <span class="hozi-performance-label"><?php _e('معدل التتبع', 'hozi-akadly'); ?></span>
                        <span class="hozi-performance-value hozi-tracking-rate">
                            <?php echo esc_html($percentage); ?>%
                        </span>
                    </div>
                    <div class="hozi-progress-bar">
                        <div class="hozi-progress-fill hozi-tracking-fill" style="width: <?php echo esc_attr($percentage); ?>%"></div>
                    </div>
                    <div class="hozi-performance-note">
                        <?php _e('نسبة الطلبيات المتتبعة من الإجمالي', 'hozi-akadly'); ?>
                    </div>
                </div>

                <!-- Rejection Rate -->
                <div class="hozi-performance-card">
                    <div class="hozi-performance-header">
                        <span class="hozi-performance-label"><?php _e('معدل الرفض', 'hozi-akadly'); ?></span>
                        <span class="hozi-performance-value hozi-rejection-rate">
                            <?php echo esc_html($rejected_percentage); ?>%
                        </span>
                    </div>
                    <div class="hozi-progress-bar">
                        <div class="hozi-progress-fill hozi-rejection-fill" style="width: <?php echo esc_attr($rejected_percentage); ?>%"></div>
                    </div>
                    <div class="hozi-performance-note">
                        <?php _e('نسبة الطلبيات المرفوضة', 'hozi-akadly'); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats Summary -->
        <div class="hozi-quick-summary">
            <div class="hozi-summary-item">
                <span class="hozi-summary-label"><?php _e('بحاجة لتحديث:', 'hozi-akadly'); ?></span>
                <span class="hozi-summary-value hozi-needs-update">
                    <?php echo esc_html(($tracking_stats->total_confirmed ?? 0) - ($tracking_stats->total_tracked ?? 0)); ?>
                </span>
            </div>
            <div class="hozi-summary-item">
                <span class="hozi-summary-label"><?php _e('آخر تحديث:', 'hozi-akadly'); ?></span>
                <span class="hozi-summary-value" id="last-update-time">
                    <?php echo esc_html(date_i18n('H:i', current_time('timestamp'))); ?>
                </span>
            </div>
            <div class="hozi-summary-item">
                <span class="hozi-summary-label"><?php _e('حالة التحديث:', 'hozi-akadly'); ?></span>
                <span class="hozi-summary-value hozi-update-status" id="update-status">
                    <span class="hozi-status-indicator"></span>
                    <?php _e('محدث', 'hozi-akadly'); ?>
                </span>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="hozi-tracking-filters-section">
        <h3><?php _e('فلترة الطلبيات', 'hozi-akadly'); ?></h3>

        <form method="get" class="hozi-filters-form" id="hozi-tracking-filters">
            <input type="hidden" name="page" value="hozi-akadly-my-tracking">

            <div class="hozi-filters-grid">
                <!-- Search Filter -->
                <div class="hozi-filter-group">
                    <label for="search_term"><?php _e('البحث السريع', 'hozi-akadly'); ?></label>
                    <input type="text"
                           id="search_term"
                           name="search_term"
                           placeholder="<?php _e('رقم الطلب أو اسم العميل...', 'hozi-akadly'); ?>"
                           value="<?php echo esc_attr($_GET['search_term'] ?? ''); ?>">
                </div>

                <!-- Status Filter -->
                <div class="hozi-filter-group">
                    <label for="status_filter"><?php _e('حالة التتبع', 'hozi-akadly'); ?></label>
                    <select id="status_filter" name="status_filter">
                        <option value=""><?php _e('جميع الحالات', 'hozi-akadly'); ?></option>
                        <option value="needs_update" <?php selected($_GET['status_filter'] ?? '', 'needs_update'); ?>>
                            <?php _e('بحاجة لتحديث', 'hozi-akadly'); ?>
                        </option>
                        <?php
                        $tracking_statuses = Hozi_Akadly_Order_Tracker::get_tracking_statuses();
                        foreach ($tracking_statuses as $status_key => $status_info) :
                        ?>
                            <option value="<?php echo esc_attr($status_key); ?>" <?php selected($_GET['status_filter'] ?? '', $status_key); ?>>
                                <?php echo esc_html($status_info['label']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Date Range Filter -->
                <div class="hozi-filter-group">
                    <label for="date_from"><?php _e('من تاريخ', 'hozi-akadly'); ?></label>
                    <input type="date"
                           id="date_from"
                           name="date_from"
                           value="<?php echo esc_attr($_GET['date_from'] ?? ''); ?>">
                </div>

                <div class="hozi-filter-group">
                    <label for="date_to"><?php _e('إلى تاريخ', 'hozi-akadly'); ?></label>
                    <input type="date"
                           id="date_to"
                           name="date_to"
                           value="<?php echo esc_attr($_GET['date_to'] ?? ''); ?>">
                </div>

                <!-- Date Type Filter -->
                <div class="hozi-filter-group">
                    <label for="date_type"><?php _e('نوع التاريخ', 'hozi-akadly'); ?></label>
                    <select id="date_type" name="date_type">
                        <option value="confirmed" <?php selected($_GET['date_type'] ?? 'confirmed', 'confirmed'); ?>>
                            <?php _e('تاريخ التأكيد', 'hozi-akadly'); ?>
                        </option>
                        <option value="updated" <?php selected($_GET['date_type'] ?? '', 'updated'); ?>>
                            <?php _e('تاريخ آخر تحديث', 'hozi-akadly'); ?>
                        </option>
                    </select>
                </div>

                <!-- Order By Filter -->
                <div class="hozi-filter-group">
                    <label for="order_by"><?php _e('ترتيب حسب', 'hozi-akadly'); ?></label>
                    <select id="order_by" name="order_by">
                        <option value="confirmed_desc" <?php selected($_GET['order_by'] ?? 'confirmed_desc', 'confirmed_desc'); ?>>
                            <?php _e('الأحدث تأكيداً', 'hozi-akadly'); ?>
                        </option>
                        <option value="confirmed_asc" <?php selected($_GET['order_by'] ?? '', 'confirmed_asc'); ?>>
                            <?php _e('الأقدم تأكيداً', 'hozi-akadly'); ?>
                        </option>
                        <option value="updated_desc" <?php selected($_GET['order_by'] ?? '', 'updated_desc'); ?>>
                            <?php _e('الأحدث تحديثاً', 'hozi-akadly'); ?>
                        </option>
                        <option value="order_id_desc" <?php selected($_GET['order_by'] ?? '', 'order_id_desc'); ?>>
                            <?php _e('رقم الطلب (تنازلي)', 'hozi-akadly'); ?>
                        </option>
                    </select>
                </div>
            </div>

            <!-- Filter Actions -->
            <div class="hozi-filter-actions">
                <button type="submit" class="button button-primary">
                    <span class="dashicons dashicons-search"></span>
                    <?php _e('تطبيق الفلاتر', 'hozi-akadly'); ?>
                </button>

                <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-tracking'); ?>" class="button">
                    <span class="dashicons dashicons-dismiss"></span>
                    <?php _e('إزالة الفلاتر', 'hozi-akadly'); ?>
                </a>

                <button type="button" class="button hozi-auto-refresh-btn" id="auto-refresh-toggle">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('تحديث تلقائي', 'hozi-akadly'); ?>
                    <span class="hozi-refresh-status"><?php _e('متوقف', 'hozi-akadly'); ?></span>
                </button>
            </div>

            <!-- Active Filters Display -->
            <?php if (!empty(array_filter($_GET, function($key) {
                return in_array($key, ['search_term', 'status_filter', 'date_from', 'date_to', 'date_type', 'order_by']) && !empty($_GET[$key]);
            }, ARRAY_FILTER_USE_KEY))) : ?>
                <div class="hozi-active-filters">
                    <h4><?php _e('الفلاتر النشطة:', 'hozi-akadly'); ?></h4>
                    <div class="hozi-active-filters-list">
                        <?php if (!empty($_GET['search_term'])) : ?>
                            <span class="hozi-active-filter">
                                <?php _e('البحث:', 'hozi-akadly'); ?> "<?php echo esc_html($_GET['search_term']); ?>"
                            </span>
                        <?php endif; ?>

                        <?php if (!empty($_GET['status_filter'])) : ?>
                            <span class="hozi-active-filter">
                                <?php _e('الحالة:', 'hozi-akadly'); ?>
                                <?php
                                if ($_GET['status_filter'] === 'needs_update') {
                                    echo __('بحاجة لتحديث', 'hozi-akadly');
                                } else {
                                    echo esc_html($tracking_statuses[$_GET['status_filter']]['label'] ?? $_GET['status_filter']);
                                }
                                ?>
                            </span>
                        <?php endif; ?>

                        <?php if (!empty($_GET['date_from']) || !empty($_GET['date_to'])) : ?>
                            <span class="hozi-active-filter">
                                <?php _e('التاريخ:', 'hozi-akadly'); ?>
                                <?php
                                if (!empty($_GET['date_from']) && !empty($_GET['date_to'])) {
                                    echo esc_html($_GET['date_from'] . ' - ' . $_GET['date_to']);
                                } elseif (!empty($_GET['date_from'])) {
                                    echo __('من', 'hozi-akadly') . ' ' . esc_html($_GET['date_from']);
                                } elseif (!empty($_GET['date_to'])) {
                                    echo __('إلى', 'hozi-akadly') . ' ' . esc_html($_GET['date_to']);
                                }
                                ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </form>
    </div>

    <!-- Orders List Section -->
    <div class="hozi-tracking-orders-section">
        <div class="hozi-orders-header">
            <h3><?php _e('طلباتي المؤكدة', 'hozi-akadly'); ?></h3>
            <div class="hozi-header-actions">
                <?php if (!empty($confirmed_orders)) : ?>
                    <button type="button" class="button button-secondary" id="clear-completed-orders">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('مسح الطلبات المكتملة', 'hozi-akadly'); ?>
                    </button>
                <?php endif; ?>
                <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-archived'); ?>" class="button">
                    <span class="dashicons dashicons-archive"></span>
                    <?php _e('عرض الأرشيف', 'hozi-akadly'); ?>
                </a>
            </div>
        </div>



        <?php if (!empty($confirmed_orders)) : ?>
            <div class="hozi-orders-grid">
                <?php foreach ($confirmed_orders as $order_data) :
                    // Try to get WooCommerce order object, but continue even if it fails
                    $order = wc_get_order($order_data->order_id);

                    // Get tracking status info
                    $tracking_statuses = Hozi_Akadly_Order_Tracker::get_tracking_statuses();
                    $current_status = $order_data->tracking_status;
                    $status_info = $current_status ? ($tracking_statuses[$current_status] ?? null) : null;

                    // Use data from our query if WooCommerce order is not available
                    $customer_name = '';
                    $customer_phone = '';
                    $customer_address = '';
                    $order_total = '';
                    $item_count = 0;

                    if ($order) {
                        // Use WooCommerce order data
                        $customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
                        $customer_phone = $order->get_billing_phone();
                        $customer_address = $order->get_formatted_billing_address();
                        $order_total = $order->get_formatted_order_total();
                        $item_count = $order->get_item_count();
                    } else {
                        // Use data from our query
                        $customer_name = trim($order_data->billing_first_name . ' ' . $order_data->billing_last_name);
                        $customer_phone = $order_data->billing_phone;
                        $customer_address = __('عنوان غير متوفر', 'hozi-akadly');
                        // Format price safely
                        if (function_exists('wc_price')) {
                            $order_total = wc_price($order_data->order_total);
                        } else {
                            $currency = function_exists('get_woocommerce_currency') ? get_woocommerce_currency() : 'ج.م';
                            $order_total = $order_data->order_total . ' ' . $currency;
                        }
                        $item_count = 1; // Default value
                    }
                ?>
                    <div class="hozi-order-tracking-card" data-order-id="<?php echo esc_attr($order_data->order_id); ?>">
                        <!-- Order Header -->
                        <div class="hozi-order-header">
                            <div class="hozi-order-number">
                                <strong><?php _e('طلب رقم:', 'hozi-akadly'); ?> #<?php echo esc_html($order_data->order_id); ?></strong>
                                <span class="hozi-order-date"><?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->order_date))); ?></span>

                                <!-- Debug info for commercial version -->
                                <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
                                    <small style="display: block; color: #666; font-size: 11px; margin-top: 5px;">
                                        وكيل التأكيد: <?php echo esc_html($order_data->agent_id ?? 'غير محدد'); ?> |
                                        حالة التأكيد: <?php echo esc_html($order_data->confirmation_status ?? 'غير محدد'); ?>
                                        <?php if (isset($order_data->tracking_agent_id)): ?>
                                            | وكيل التتبع: <?php echo esc_html($order_data->tracking_agent_id); ?>
                                        <?php endif; ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="hozi-tracking-status">
                                <?php if ($status_info) : ?>
                                    <span class="hozi-status-badge" style="background-color: <?php echo esc_attr($status_info['color']); ?>">
                                        <span class="dashicons dashicons-<?php echo esc_attr($status_info['icon']); ?>"></span>
                                        <?php echo esc_html($status_info['label']); ?>
                                    </span>
                                <?php else : ?>
                                    <span class="hozi-status-badge hozi-no-status">
                                        <span class="dashicons dashicons-clock"></span>
                                        <?php _e('بحاجة لتحديث', 'hozi-akadly'); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Customer Info -->
                        <div class="hozi-customer-section">
                            <h4><?php _e('معلومات العميل', 'hozi-akadly'); ?></h4>
                            <div class="hozi-customer-details">
                                <div class="hozi-customer-name">
                                    <strong><?php echo esc_html($customer_name ?: __('اسم غير متوفر', 'hozi-akadly')); ?></strong>
                                </div>
                                <?php if ($customer_phone) : ?>
                                    <div class="hozi-customer-phone">
                                        <a href="tel:<?php echo esc_attr($customer_phone); ?>" class="hozi-phone-link">
                                            <span class="dashicons dashicons-phone"></span>
                                            <?php echo esc_html($customer_phone); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                <div class="hozi-customer-address">
                                    <span class="dashicons dashicons-location"></span>
                                    <?php echo wp_kses_post($customer_address); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Order Details -->
                        <div class="hozi-order-details-section">
                            <h4><?php _e('تفاصيل الطلب', 'hozi-akadly'); ?></h4>
                            <div class="hozi-order-summary">
                                <div class="hozi-order-total">
                                    <span class="hozi-total-label"><?php _e('المجموع:', 'hozi-akadly'); ?></span>
                                    <span class="hozi-total-value"><?php echo $order_total; ?></span>
                                </div>
                                <div class="hozi-order-items-count">
                                    <span class="dashicons dashicons-cart"></span>
                                    <?php printf(__('%d منتج', 'hozi-akadly'), $item_count); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Confirmation Info -->
                        <div class="hozi-confirmation-section">
                            <h4><?php _e('معلومات التأكيد', 'hozi-akadly'); ?></h4>
                            <div class="hozi-confirmation-details">
                                <div class="hozi-confirmed-date">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    <?php _e('تم التأكيد:', 'hozi-akadly'); ?>
                                    <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->confirmed_at))); ?>
                                </div>
                                <?php if ($order_data->confirmation_notes) : ?>
                                    <div class="hozi-confirmation-notes">
                                        <span class="dashicons dashicons-edit"></span>
                                        <strong><?php _e('ملاحظات التأكيد:', 'hozi-akadly'); ?></strong>
                                        <p><?php echo nl2br(esc_html($order_data->confirmation_notes)); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Tracking Info (if exists) -->
                        <?php if ($order_data->tracking_status) : ?>
                            <div class="hozi-tracking-info-section">
                                <h4><?php _e('معلومات التتبع', 'hozi-akadly'); ?></h4>
                                <div class="hozi-tracking-details">
                                    <div class="hozi-last-update">
                                        <span class="dashicons dashicons-update"></span>
                                        <?php _e('آخر تحديث:', 'hozi-akadly'); ?>
                                        <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->last_tracking_update))); ?>
                                    </div>
                                    <?php if ($order_data->reason_category) : ?>
                                        <div class="hozi-tracking-reason">
                                            <span class="dashicons dashicons-info"></span>
                                            <strong><?php _e('السبب:', 'hozi-akadly'); ?></strong>
                                            <?php
                                            $reason_categories = Hozi_Akadly_Order_Tracker::get_reason_categories();
                                            $category_reasons = $reason_categories[$order_data->tracking_status] ?? array();
                                            echo esc_html($category_reasons[$order_data->reason_category] ?? $order_data->reason_category);
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($order_data->tracking_notes) : ?>
                                        <div class="hozi-tracking-notes">
                                            <span class="dashicons dashicons-edit"></span>
                                            <strong><?php _e('ملاحظات التتبع:', 'hozi-akadly'); ?></strong>
                                            <p><?php echo nl2br(esc_html($order_data->tracking_notes)); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Quick Action Buttons -->
                        <?php if (!$order_data->tracking_status) : ?>
                            <div class="hozi-quick-actions-row">
                                <h5><?php _e('تحديث سريع:', 'hozi-akadly'); ?></h5>
                                <div class="hozi-quick-buttons">
                                    <button type="button" class="hozi-quick-action-btn hozi-quick-delivered"
                                            data-order="<?php echo esc_attr($order_data->order_id); ?>"
                                            data-status="delivered">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php _e('تم التوصيل', 'hozi-akadly'); ?>
                                    </button>
                                    <button type="button" class="hozi-quick-action-btn hozi-quick-rejected"
                                            data-order="<?php echo esc_attr($order_data->order_id); ?>"
                                            data-status="rejected_customer">
                                        <span class="dashicons dashicons-no"></span>
                                        <?php _e('مرفوض', 'hozi-akadly'); ?>
                                    </button>
                                    <button type="button" class="hozi-quick-action-btn hozi-quick-postponed"
                                            data-order="<?php echo esc_attr($order_data->order_id); ?>"
                                            data-status="postponed_customer">
                                        <span class="dashicons dashicons-clock"></span>
                                        <?php _e('مؤجل', 'hozi-akadly'); ?>
                                    </button>
                                    <button type="button" class="hozi-quick-action-btn hozi-quick-exchange"
                                            data-order="<?php echo esc_attr($order_data->order_id); ?>"
                                            data-status="exchange_requested">
                                        <span class="dashicons dashicons-update"></span>
                                        <?php _e('استبدال', 'hozi-akadly'); ?>
                                    </button>
                                    <button type="button" class="hozi-quick-action-btn hozi-quick-out-delivery"
                                            data-order="<?php echo esc_attr($order_data->order_id); ?>"
                                            data-status="out_for_delivery">
                                        <span class="dashicons dashicons-car"></span>
                                        <?php _e('في الطريق', 'hozi-akadly'); ?>
                                    </button>
                                    <button type="button" class="hozi-quick-action-btn hozi-quick-not-found"
                                            data-order="<?php echo esc_attr($order_data->order_id); ?>"
                                            data-status="customer_not_found">
                                        <span class="dashicons dashicons-search"></span>
                                        <?php _e('لم يوجد', 'hozi-akadly'); ?>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Action Buttons -->
                        <div class="hozi-order-actions">
                            <?php if (!$order_data->tracking_status) : ?>
                                <button type="button" class="button button-primary hozi-update-status-btn" data-order="<?php echo esc_attr($order_data->order_id); ?>">
                                    <span class="dashicons dashicons-update"></span>
                                    <?php _e('تحديث مفصل', 'hozi-akadly'); ?>
                                </button>
                            <?php else : ?>
                                <button type="button" class="button hozi-update-status-btn" data-order="<?php echo esc_attr($order_data->order_id); ?>">
                                    <span class="dashicons dashicons-edit"></span>
                                    <?php _e('تعديل الحالة', 'hozi-akadly'); ?>
                                </button>
                            <?php endif; ?>

                            <a href="<?php echo admin_url('post.php?post=' . $order_data->order_id . '&action=edit'); ?>" class="button" target="_blank">
                                <span class="dashicons dashicons-external"></span>
                                <?php _e('عرض الطلب', 'hozi-akadly'); ?>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else : ?>
            <div class="hozi-no-orders">
                <div class="hozi-no-orders-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <h3><?php _e('لا توجد طلبيات مؤكدة', 'hozi-akadly'); ?></h3>
                <p><?php _e('لم تقم بتأكيد أي طلبيات بعد، أو تم تتبع جميع طلبياتك المؤكدة.', 'hozi-akadly'); ?></p>

                <!-- Enhanced Debug Info for Commercial Version -->
                <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
                    <div class="hozi-debug-info" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: right; font-family: 'Courier New', monospace; font-size: 13px;">
                        <h4 style="color: #495057; margin-bottom: 15px; font-size: 14px;">🔍 معلومات التشخيص المتقدمة</h4>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div style="background: white; padding: 10px; border-radius: 5px; border-right: 4px solid #007cba;">
                                <strong style="color: #007cba;">معرف المستخدم الحالي:</strong><br>
                                <?php echo esc_html($current_user_id); ?>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px; border-right: 4px solid #00a32a;">
                                <strong style="color: #00a32a;">معرف الوكيل:</strong><br>
                                <?php echo esc_html($current_agent ? $current_agent->id : 'غير محدد'); ?>
                            </div>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                            <strong style="color: #d63384;">استعلام قاعدة البيانات:</strong><br>
                            <code style="background: #f8f9fa; padding: 5px; border-radius: 3px; display: block; margin-top: 5px; word-break: break-all;">
                                تم تحسين الاستعلام - يبحث عن الطلبات المؤكدة للوكيل ID: <?php echo esc_html($current_user_id); ?>
                            </code>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 5px;">
                            <strong style="color: #6f42c1;">حالة الاتصال:</strong><br>
                            ✅ متصل بقاعدة البيانات<br>
                            ✅ جداول أكدلي موجودة<br>
                            ✅ استعلام محسن ومطبق
                        </div>

                        <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                            <strong style="color: #856404;">💡 نصيحة:</strong>
                            إذا كان لديك طلبات مؤكدة ولا تظهر هنا، تحقق من جدول hozi_order_assignments في قاعدة البيانات.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Quick Actions Section -->
    <div class="hozi-quick-actions-section">
        <h3><?php _e('إجراءات سريعة', 'hozi-akadly'); ?></h3>

        <div class="hozi-quick-actions-grid">
            <!-- Bulk Actions -->
            <div class="hozi-bulk-actions-card">
                <h4><?php _e('إجراءات متعددة', 'hozi-akadly'); ?></h4>
                <div class="hozi-bulk-controls">
                    <div class="hozi-select-controls">
                        <button type="button" class="button hozi-select-all">
                            <span class="dashicons dashicons-yes"></span>
                            <?php _e('تحديد الكل', 'hozi-akadly'); ?>
                        </button>
                        <button type="button" class="button hozi-select-none">
                            <span class="dashicons dashicons-dismiss"></span>
                            <?php _e('إلغاء التحديد', 'hozi-akadly'); ?>
                        </button>
                        <span class="hozi-selected-count">
                            <?php _e('محدد:', 'hozi-akadly'); ?> <span id="selected-count">0</span>
                        </span>
                    </div>
                    <div class="hozi-bulk-action-buttons">
                        <button type="button" class="button button-primary hozi-bulk-update" data-status="delivered">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php _e('تم التوصيل', 'hozi-akadly'); ?>
                        </button>
                        <button type="button" class="button hozi-bulk-update" data-status="postponed_customer">
                            <span class="dashicons dashicons-clock"></span>
                            <?php _e('مؤجلة', 'hozi-akadly'); ?>
                        </button>
                        <button type="button" class="button hozi-bulk-update" data-status="rejected_customer">
                            <span class="dashicons dashicons-no"></span>
                            <?php _e('مرفوضة', 'hozi-akadly'); ?>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Status Updates -->
            <div class="hozi-quick-status-card">
                <h4><?php _e('تحديث سريع للحالة', 'hozi-akadly'); ?></h4>
                <div class="hozi-quick-status-grid">
                    <?php
                    $quick_statuses = array(
                        'delivered' => array(
                            'label' => __('تم التوصيل', 'hozi-akadly'),
                            'icon' => 'yes-alt',
                            'color' => '#4CAF50',
                            'class' => 'success'
                        ),
                        'postponed_customer' => array(
                            'label' => __('مؤجلة بطلب الزبون', 'hozi-akadly'),
                            'icon' => 'clock',
                            'color' => '#FF9800',
                            'class' => 'warning'
                        ),
                        'rejected_customer' => array(
                            'label' => __('رفض من الزبون', 'hozi-akadly'),
                            'icon' => 'no',
                            'color' => '#f44336',
                            'class' => 'danger'
                        ),
                        'exchange_requested' => array(
                            'label' => __('طلب استبدال', 'hozi-akadly'),
                            'icon' => 'update',
                            'color' => '#2196F3',
                            'class' => 'info'
                        ),
                        'out_for_delivery' => array(
                            'label' => __('في الطريق للتوصيل', 'hozi-akadly'),
                            'icon' => 'car',
                            'color' => '#9C27B0',
                            'class' => 'progress'
                        ),
                        'customer_not_found' => array(
                            'label' => __('لم يتم العثور على العميل', 'hozi-akadly'),
                            'icon' => 'search',
                            'color' => '#FF5722',
                            'class' => 'not-found'
                        )
                    );

                    foreach ($quick_statuses as $status_key => $status_data) : ?>
                        <button type="button"
                                class="hozi-quick-status-btn hozi-<?php echo esc_attr($status_data['class']); ?>"
                                data-status="<?php echo esc_attr($status_key); ?>"
                                style="border-left-color: <?php echo esc_attr($status_data['color']); ?>">
                            <div class="hozi-status-icon" style="background-color: <?php echo esc_attr($status_data['color']); ?>">
                                <span class="dashicons dashicons-<?php echo esc_attr($status_data['icon']); ?>"></span>
                            </div>
                            <div class="hozi-status-text">
                                <span class="hozi-status-label"><?php echo esc_html($status_data['label']); ?></span>
                                <span class="hozi-status-hint"><?php _e('انقر للتحديث السريع', 'hozi-akadly'); ?></span>
                            </div>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Quick Tools -->
            <div class="hozi-quick-tools-card">
                <h4><?php _e('أدوات سريعة', 'hozi-akadly'); ?></h4>
                <div class="hozi-tools-grid">
                    <button type="button" class="button hozi-refresh-data">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('تحديث البيانات', 'hozi-akadly'); ?>
                    </button>
                    <button type="button" class="button hozi-export-data">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('تصدير البيانات', 'hozi-akadly'); ?>
                    </button>
                    <button type="button" class="button hozi-print-report">
                        <span class="dashicons dashicons-printer"></span>
                        <?php _e('طباعة التقرير', 'hozi-akadly'); ?>
                    </button>
                    <button type="button" class="button hozi-keyboard-shortcuts" title="<?php _e('اختصارات لوحة المفاتيح', 'hozi-akadly'); ?>">
                        <span class="dashicons dashicons-editor-help"></span>
                        <?php _e('اختصارات', 'hozi-akadly'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications Section -->
    <div class="hozi-notifications-section">
        <h3><?php _e('الإشعارات والتنبيهات', 'hozi-akadly'); ?></h3>

        <div class="hozi-notifications-grid">
            <!-- Notification Settings -->
            <div class="hozi-notification-settings-card">
                <h4><?php _e('إعدادات الإشعارات', 'hozi-akadly'); ?></h4>
                <div class="hozi-notification-controls">
                    <div class="hozi-notification-toggle">
                        <label class="hozi-toggle-switch">
                            <input type="checkbox" id="notifications-enabled" checked>
                            <span class="hozi-toggle-slider"></span>
                        </label>
                        <span class="hozi-toggle-label"><?php _e('تفعيل الإشعارات', 'hozi-akadly'); ?></span>
                    </div>

                    <div class="hozi-notification-toggle">
                        <label class="hozi-toggle-switch">
                            <input type="checkbox" id="sound-enabled" checked>
                            <span class="hozi-toggle-slider"></span>
                        </label>
                        <span class="hozi-toggle-label"><?php _e('الإشعارات الصوتية', 'hozi-akadly'); ?></span>
                    </div>

                    <div class="hozi-notification-toggle">
                        <label class="hozi-toggle-switch">
                            <input type="checkbox" id="desktop-notifications" checked>
                            <span class="hozi-toggle-slider"></span>
                        </label>
                        <span class="hozi-toggle-label"><?php _e('إشعارات سطح المكتب', 'hozi-akadly'); ?></span>
                    </div>

                    <div class="hozi-volume-control">
                        <label for="notification-volume"><?php _e('مستوى الصوت:', 'hozi-akadly'); ?></label>
                        <input type="range" id="notification-volume" min="0" max="100" value="70" class="hozi-volume-slider">
                        <span class="hozi-volume-value">70%</span>
                    </div>
                </div>
            </div>

            <!-- Live Notifications -->
            <div class="hozi-live-notifications-card">
                <h4><?php _e('الإشعارات المباشرة', 'hozi-akadly'); ?></h4>
                <div class="hozi-notification-status">
                    <div class="hozi-connection-status" id="connection-status">
                        <span class="hozi-status-dot hozi-connected"></span>
                        <span class="hozi-status-text"><?php _e('متصل', 'hozi-akadly'); ?></span>
                    </div>
                    <div class="hozi-last-check" id="last-check">
                        <?php _e('آخر فحص:', 'hozi-akadly'); ?> <span id="last-check-time"><?php echo date_i18n('H:i:s'); ?></span>
                    </div>
                </div>

                <div class="hozi-notification-feed" id="notification-feed">
                    <div class="hozi-notification-item hozi-info">
                        <div class="hozi-notification-icon">
                            <span class="dashicons dashicons-info"></span>
                        </div>
                        <div class="hozi-notification-content">
                            <div class="hozi-notification-title"><?php _e('نظام الإشعارات نشط', 'hozi-akadly'); ?></div>
                            <div class="hozi-notification-message"><?php _e('سيتم إشعارك بأي تحديثات جديدة على الطلبيات', 'hozi-akadly'); ?></div>
                            <div class="hozi-notification-time"><?php echo date_i18n('H:i'); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Types -->
            <div class="hozi-notification-types-card">
                <h4><?php _e('أنواع الإشعارات', 'hozi-akadly'); ?></h4>
                <div class="hozi-notification-types">
                    <div class="hozi-notification-type">
                        <div class="hozi-type-icon hozi-new-order">
                            <span class="dashicons dashicons-plus-alt"></span>
                        </div>
                        <div class="hozi-type-details">
                            <span class="hozi-type-title"><?php _e('طلبية جديدة', 'hozi-akadly'); ?></span>
                            <span class="hozi-type-description"><?php _e('عند تخصيص طلبية جديدة لك', 'hozi-akadly'); ?></span>
                        </div>
                        <label class="hozi-toggle-switch hozi-small">
                            <input type="checkbox" class="notification-type-toggle" data-type="new_order" checked>
                            <span class="hozi-toggle-slider"></span>
                        </label>
                    </div>

                    <div class="hozi-notification-type">
                        <div class="hozi-type-icon hozi-status-update">
                            <span class="dashicons dashicons-update"></span>
                        </div>
                        <div class="hozi-type-details">
                            <span class="hozi-type-title"><?php _e('تحديث الحالة', 'hozi-akadly'); ?></span>
                            <span class="hozi-type-description"><?php _e('عند تحديث حالة طلبية', 'hozi-akadly'); ?></span>
                        </div>
                        <label class="hozi-toggle-switch hozi-small">
                            <input type="checkbox" class="notification-type-toggle" data-type="status_update" checked>
                            <span class="hozi-toggle-slider"></span>
                        </label>
                    </div>

                    <div class="hozi-notification-type">
                        <div class="hozi-type-icon hozi-urgent">
                            <span class="dashicons dashicons-warning"></span>
                        </div>
                        <div class="hozi-type-details">
                            <span class="hozi-type-title"><?php _e('طلبيات عاجلة', 'hozi-akadly'); ?></span>
                            <span class="hozi-type-description"><?php _e('طلبيات تحتاج متابعة فورية', 'hozi-akadly'); ?></span>
                        </div>
                        <label class="hozi-toggle-switch hozi-small">
                            <input type="checkbox" class="notification-type-toggle" data-type="urgent" checked>
                            <span class="hozi-toggle-slider"></span>
                        </label>
                    </div>

                    <div class="hozi-notification-type">
                        <div class="hozi-type-icon hozi-reminder">
                            <span class="dashicons dashicons-clock"></span>
                        </div>
                        <div class="hozi-type-details">
                            <span class="hozi-type-title"><?php _e('تذكيرات', 'hozi-akadly'); ?></span>
                            <span class="hozi-type-description"><?php _e('تذكيرات بالطلبيات المعلقة', 'hozi-akadly'); ?></span>
                        </div>
                        <label class="hozi-toggle-switch hozi-small">
                            <input type="checkbox" class="notification-type-toggle" data-type="reminder" checked>
                            <span class="hozi-toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Sound Test -->
            <div class="hozi-sound-test-card">
                <h4><?php _e('اختبار الأصوات', 'hozi-akadly'); ?></h4>
                <div class="hozi-sound-tests">
                    <button type="button" class="button hozi-test-sound" data-sound="new_order">
                        <span class="dashicons dashicons-controls-play"></span>
                        <?php _e('طلبية جديدة', 'hozi-akadly'); ?>
                    </button>
                    <button type="button" class="button hozi-test-sound" data-sound="status_update">
                        <span class="dashicons dashicons-controls-play"></span>
                        <?php _e('تحديث حالة', 'hozi-akadly'); ?>
                    </button>
                    <button type="button" class="button hozi-test-sound" data-sound="urgent">
                        <span class="dashicons dashicons-controls-play"></span>
                        <?php _e('عاجل', 'hozi-akadly'); ?>
                    </button>
                    <button type="button" class="button hozi-test-sound" data-sound="reminder">
                        <span class="dashicons dashicons-controls-play"></span>
                        <?php _e('تذكير', 'hozi-akadly'); ?>
                    </button>
                </div>

                <div class="hozi-notification-preview">
                    <h5><?php _e('معاينة الإشعار', 'hozi-akadly'); ?></h5>
                    <button type="button" class="button button-primary hozi-test-notification">
                        <span class="dashicons dashicons-bell"></span>
                        <?php _e('اختبار إشعار سطح المكتب', 'hozi-akadly'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Update Modal -->
<div id="hozi-quick-update-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content hozi-quick-modal">
        <div class="hozi-modal-header">
            <h2 id="modal-title"><?php _e('تحديث سريع للحالة', 'hozi-akadly'); ?></h2>
            <button type="button" class="hozi-modal-close">&times;</button>
        </div>

        <div class="hozi-modal-body">
            <!-- Selected Orders Info -->
            <div class="hozi-selected-orders-info">
                <div class="hozi-selection-summary">
                    <span class="hozi-selection-icon">
                        <span class="dashicons dashicons-yes"></span>
                    </span>
                    <span class="hozi-selection-text">
                        <?php _e('محدد:', 'hozi-akadly'); ?> <span id="modal-selected-count">0</span> <?php _e('طلبية', 'hozi-akadly'); ?>
                    </span>
                </div>
                <div class="hozi-selected-orders-list" id="selected-orders-list">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- Status Selection -->
            <div class="hozi-status-selection">
                <h3><?php _e('اختر الحالة الجديدة', 'hozi-akadly'); ?></h3>
                <div class="hozi-status-options">
                    <?php
                    $all_statuses = Hozi_Akadly_Order_Tracker::get_tracking_statuses();
                    foreach ($all_statuses as $status_key => $status_info) : ?>
                        <label class="hozi-status-option">
                            <input type="radio" name="quick_status" value="<?php echo esc_attr($status_key); ?>">
                            <div class="hozi-status-card" style="border-color: <?php echo esc_attr($status_info['color']); ?>">
                                <div class="hozi-status-icon" style="background-color: <?php echo esc_attr($status_info['color']); ?>">
                                    <span class="dashicons dashicons-<?php echo esc_attr($status_info['icon']); ?>"></span>
                                </div>
                                <div class="hozi-status-details">
                                    <span class="hozi-status-name"><?php echo esc_html($status_info['label']); ?></span>
                                    <span class="hozi-status-type"><?php echo esc_html($status_info['type']); ?></span>
                                </div>
                            </div>
                        </label>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Reason Selection (Dynamic) -->
            <div class="hozi-reason-selection" id="reason-selection" style="display: none;">
                <h3><?php _e('اختر السبب', 'hozi-akadly'); ?></h3>
                <select id="quick-reason-select" class="hozi-reason-select">
                    <option value=""><?php _e('اختر السبب...', 'hozi-akadly'); ?></option>
                </select>
            </div>

            <!-- Quick Notes -->
            <div class="hozi-quick-notes">
                <h3><?php _e('ملاحظات سريعة (اختياري)', 'hozi-akadly'); ?></h3>
                <div class="hozi-notes-options">
                    <div class="hozi-preset-notes">
                        <button type="button" class="hozi-preset-note" data-note="<?php _e('تم التوصيل بنجاح', 'hozi-akadly'); ?>">
                            <?php _e('تم التوصيل بنجاح', 'hozi-akadly'); ?>
                        </button>
                        <button type="button" class="hozi-preset-note" data-note="<?php _e('العميل غير متواجد', 'hozi-akadly'); ?>">
                            <?php _e('العميل غير متواجد', 'hozi-akadly'); ?>
                        </button>
                        <button type="button" class="hozi-preset-note" data-note="<?php _e('رفض استلام الطلب', 'hozi-akadly'); ?>">
                            <?php _e('رفض استلام الطلب', 'hozi-akadly'); ?>
                        </button>
                        <button type="button" class="hozi-preset-note" data-note="<?php _e('طلب تأجيل التوصيل', 'hozi-akadly'); ?>">
                            <?php _e('طلب تأجيل التوصيل', 'hozi-akadly'); ?>
                        </button>
                    </div>
                    <textarea id="quick-notes-text" placeholder="<?php _e('أضف ملاحظات إضافية...', 'hozi-akadly'); ?>" rows="3"></textarea>
                </div>
            </div>
        </div>

        <div class="hozi-modal-footer">
            <div class="hozi-update-progress" id="update-progress" style="display: none;">
                <div class="hozi-progress-bar">
                    <div class="hozi-progress-fill" id="progress-fill"></div>
                </div>
                <div class="hozi-progress-text" id="progress-text">
                    <?php _e('جاري التحديث...', 'hozi-akadly'); ?>
                </div>
            </div>

            <div class="hozi-modal-actions">
                <button type="button" class="button button-primary hozi-confirm-update" id="confirm-update">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('تحديث الحالة', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-modal-close">
                    <?php _e('إلغاء', 'hozi-akadly'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Keyboard Shortcuts Modal -->
<div id="hozi-shortcuts-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content">
        <div class="hozi-modal-header">
            <h2><?php _e('اختصارات لوحة المفاتيح', 'hozi-akadly'); ?></h2>
            <button type="button" class="hozi-modal-close">&times;</button>
        </div>

        <div class="hozi-modal-body">
            <div class="hozi-shortcuts-grid">
                <div class="hozi-shortcut-item">
                    <kbd>Ctrl + A</kbd>
                    <span><?php _e('تحديد جميع الطلبيات', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>Ctrl + D</kbd>
                    <span><?php _e('إلغاء تحديد الكل', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>Ctrl + U</kbd>
                    <span><?php _e('تحديث سريع', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>Ctrl + R</kbd>
                    <span><?php _e('تحديث البيانات', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>Ctrl + F</kbd>
                    <span><?php _e('البحث السريع', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>Esc</kbd>
                    <span><?php _e('إغلاق النوافذ المنبثقة', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>1</kbd>
                    <span><?php _e('تم التوصيل', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>2</kbd>
                    <span><?php _e('مؤجلة', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>3</kbd>
                    <span><?php _e('مرفوضة', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>4</kbd>
                    <span><?php _e('استبدال', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>5</kbd>
                    <span><?php _e('في الطريق', 'hozi-akadly'); ?></span>
                </div>
                <div class="hozi-shortcut-item">
                    <kbd>6</kbd>
                    <span><?php _e('لم يوجد', 'hozi-akadly'); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Basic CSS for Structure -->
<style>
.hozi-agent-tracking-wrap {
    background: #f8f9fa;
    margin: 0 0 0 -20px;
    padding: 20px;
    min-height: 100vh;
}

.hozi-tracking-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #0073aa;
}

.hozi-tracking-header h1 {
    margin: 0 0 10px 0;
    color: #0073aa;
}

.hozi-tracking-subtitle {
    margin: 0;
    color: #666;
    font-style: italic;
}

.hozi-tracking-stats-section,
.hozi-tracking-filters-section,
.hozi-tracking-orders-section,
.hozi-quick-actions-section,
.hozi-notifications-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
}

.hozi-tracking-stats-section h2,
.hozi-tracking-filters-section h3,
.hozi-tracking-orders-section h3,
.hozi-quick-actions-section h3,
.hozi-notifications-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* ===== NOTIFICATIONS STYLES ===== */
.hozi-notifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.hozi-notification-settings-card,
.hozi-live-notifications-card,
.hozi-notification-types-card,
.hozi-sound-test-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.hozi-notification-settings-card h4,
.hozi-live-notifications-card h4,
.hozi-notification-types-card h4,
.hozi-sound-test-card h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 8px;
}

/* ===== TOGGLE SWITCHES ===== */
.hozi-notification-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.hozi-toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.hozi-toggle-switch.hozi-small {
    width: 40px;
    height: 20px;
}

.hozi-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.hozi-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.hozi-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.hozi-toggle-switch.hozi-small .hozi-toggle-slider:before {
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
}

.hozi-toggle-switch input:checked + .hozi-toggle-slider {
    background-color: #4CAF50;
}

.hozi-toggle-switch input:checked + .hozi-toggle-slider:before {
    transform: translateX(26px);
}

.hozi-toggle-switch.hozi-small input:checked + .hozi-toggle-slider:before {
    transform: translateX(20px);
}

.hozi-toggle-label {
    font-weight: 500;
    color: #333;
}

/* ===== VOLUME CONTROL ===== */
.hozi-volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.hozi-volume-control label {
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

.hozi-volume-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.hozi-volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0073aa;
    cursor: pointer;
}

.hozi-volume-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0073aa;
    cursor: pointer;
    border: none;
}

.hozi-volume-value {
    font-weight: 600;
    color: #0073aa;
    min-width: 35px;
    text-align: right;
}

/* ===== CONNECTION STATUS ===== */
.hozi-notification-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.hozi-connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.hozi-status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.hozi-status-dot.hozi-connected {
    background: #4CAF50;
}

.hozi-status-dot.hozi-disconnected {
    background: #f44336;
}

.hozi-status-text {
    font-weight: 600;
    color: #333;
}

.hozi-last-check {
    font-size: 12px;
    color: #666;
}

/* ===== NOTIFICATION FEED ===== */
.hozi-notification-feed {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: #fafafa;
}

.hozi-notification-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.hozi-notification-item:last-child {
    border-bottom: none;
}

.hozi-notification-item:hover {
    background: white;
}

.hozi-notification-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.hozi-notification-item.hozi-info .hozi-notification-icon {
    background: #e3f2fd;
    color: #0073aa;
}

.hozi-notification-item.hozi-success .hozi-notification-icon {
    background: #e8f5e8;
    color: #4CAF50;
}

.hozi-notification-item.hozi-warning .hozi-notification-icon {
    background: #fff3e0;
    color: #FF9800;
}

.hozi-notification-item.hozi-error .hozi-notification-icon {
    background: #ffebee;
    color: #f44336;
}

.hozi-notification-content {
    flex: 1;
}

.hozi-notification-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.hozi-notification-message {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
}

.hozi-notification-time {
    font-size: 11px;
    color: #999;
}

/* ===== NOTIFICATION TYPES ===== */
.hozi-notification-types {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.hozi-notification-type {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.hozi-notification-type:hover {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-type-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.hozi-type-icon.hozi-new-order {
    background: #4CAF50;
}

.hozi-type-icon.hozi-status-update {
    background: #0073aa;
}

.hozi-type-icon.hozi-urgent {
    background: #f44336;
}

.hozi-type-icon.hozi-reminder {
    background: #FF9800;
}

.hozi-type-details {
    flex: 1;
}

.hozi-type-title {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.hozi-type-description {
    display: block;
    font-size: 12px;
    color: #666;
}

/* ===== SOUND TESTS ===== */
.hozi-sound-tests {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.hozi-test-sound {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 10px 8px;
    transition: all 0.3s ease;
    border-radius: 6px;
}

.hozi-test-sound:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.hozi-test-sound.playing {
    background: #4CAF50;
    color: white;
    border-color: #4CAF50;
}

.hozi-notification-preview {
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.hozi-notification-preview h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
}

.hozi-test-notification {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
}

/* ===== DESKTOP NOTIFICATION STYLES ===== */
.hozi-desktop-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    border-left: 4px solid #0073aa;
    z-index: 100001;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hozi-desktop-notification.hozi-slide-out {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.hozi-desktop-notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #e0e0e0;
}

.hozi-desktop-notification-title {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.hozi-desktop-notification-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #666;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.hozi-desktop-notification-close:hover {
    opacity: 1;
}

.hozi-desktop-notification-body {
    padding: 15px;
}

.hozi-desktop-notification-message {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 10px;
}

.hozi-desktop-notification-actions {
    display: flex;
    gap: 8px;
}

.hozi-desktop-notification-actions .button {
    padding: 6px 12px;
    font-size: 12px;
}

/* ===== NOTIFICATION SOUND INDICATOR ===== */
.hozi-sound-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 20px;
    border-radius: 50%;
    z-index: 100002;
    animation: soundPulse 0.6s ease-out;
}

@keyframes soundPulse {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
    }
}

.hozi-sound-indicator .dashicons {
    font-size: 24px;
}

/* ===== QUICK ACTIONS STYLES ===== */
.hozi-quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.hozi-bulk-actions-card,
.hozi-quick-status-card,
.hozi-quick-tools-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.hozi-bulk-actions-card h4,
.hozi-quick-status-card h4,
.hozi-quick-tools-card h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 8px;
}

/* ===== BULK ACTIONS ===== */
.hozi-bulk-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.hozi-select-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.hozi-selected-count {
    background: #0073aa;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.hozi-bulk-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-bulk-update {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.hozi-bulk-update:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* ===== QUICK STATUS BUTTONS ===== */
.hozi-quick-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.hozi-quick-status-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    border-left-width: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.hozi-quick-status-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: currentColor;
}

.hozi-quick-status-btn .hozi-status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.hozi-quick-status-btn .hozi-status-icon .dashicons {
    font-size: 20px;
}

.hozi-status-text {
    flex: 1;
}

.hozi-status-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.hozi-status-hint {
    display: block;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* ===== QUICK TOOLS ===== */
.hozi-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.hozi-tools-grid .button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 12px 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.hozi-tools-grid .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* ===== MODAL STYLES ===== */
.hozi-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.hozi-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.hozi-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 2px solid #f0f0f0;
    background: linear-gradient(135deg, #0073aa, #005a87);
    color: white;
    border-radius: 12px 12px 0 0;
}

.hozi-modal-header h2 {
    margin: 0;
    color: white;
}

.hozi-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: white;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.hozi-modal-close:hover {
    opacity: 1;
}

.hozi-modal-body {
    padding: 20px;
}

.hozi-modal-footer {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 0 0 12px 12px;
}

/* ===== MODAL CONTENT STYLES ===== */
.hozi-selected-orders-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #0073aa;
}

.hozi-selection-summary {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.hozi-selection-icon {
    background: #4CAF50;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hozi-selection-text {
    font-weight: 600;
    color: #333;
}

.hozi-selected-orders-list {
    max-height: 150px;
    overflow-y: auto;
    background: white;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e0e0e0;
}

.hozi-selected-order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.hozi-selected-order-item:last-child {
    border-bottom: none;
}

/* ===== STATUS SELECTION ===== */
.hozi-status-selection {
    margin-bottom: 20px;
}

.hozi-status-selection h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.hozi-status-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.hozi-status-option {
    cursor: pointer;
}

.hozi-status-option input[type="radio"] {
    display: none;
}

.hozi-status-option .hozi-status-card {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.hozi-status-option input[type="radio"]:checked + .hozi-status-card {
    border-color: currentColor;
    background: rgba(0,115,170,0.05);
    transform: scale(1.02);
}

.hozi-status-option .hozi-status-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.hozi-status-option .hozi-status-icon .dashicons {
    font-size: 16px;
}

.hozi-status-details {
    flex: 1;
}

.hozi-status-name {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.hozi-status-type {
    display: block;
    font-size: 11px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== REASON SELECTION ===== */
.hozi-reason-selection {
    margin-bottom: 20px;
}

.hozi-reason-selection h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.hozi-reason-select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.hozi-reason-select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

/* ===== QUICK NOTES ===== */
.hozi-quick-notes {
    margin-bottom: 20px;
}

.hozi-quick-notes h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.hozi-preset-notes {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.hozi-preset-note {
    background: #f0f8ff;
    border: 1px solid #0073aa;
    color: #0073aa;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hozi-preset-note:hover,
.hozi-preset-note.active {
    background: #0073aa;
    color: white;
}

#quick-notes-text {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
    transition: border-color 0.3s ease;
}

#quick-notes-text:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

/* ===== PROGRESS BAR ===== */
.hozi-update-progress {
    margin-bottom: 15px;
}

.hozi-update-progress .hozi-progress-bar {
    width: 100%;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.hozi-update-progress .hozi-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #2196F3);
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

.hozi-progress-text {
    text-align: center;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* ===== MODAL ACTIONS ===== */
.hozi-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.hozi-modal-actions .button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 20px;
}

/* ===== KEYBOARD SHORTCUTS ===== */
.hozi-shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.hozi-shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #0073aa;
}

.hozi-shortcut-item kbd {
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    font-family: monospace;
}

.hozi-shortcut-item span {
    color: #666;
    font-size: 14px;
}

/* ===== STATISTICS STYLES ===== */
.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.hozi-stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #0073aa;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.hozi-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.hozi-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.hozi-stat-card .hozi-stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #f0f8ff;
    margin-bottom: 15px;
}

.hozi-stat-card .hozi-stat-icon .dashicons {
    font-size: 24px;
    color: #0073aa;
}

.hozi-stat-content h3 {
    font-size: 2.5em;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: #333;
    line-height: 1;
}

.hozi-stat-content p {
    margin: 0 0 8px 0;
    color: #666;
    font-weight: 500;
    font-size: 14px;
}

.hozi-stat-period,
.hozi-stat-percentage {
    font-size: 12px;
    color: #999;
    font-style: italic;
}

/* Card-specific colors */
.hozi-total-card { border-left-color: #0073aa; }
.hozi-total-card .hozi-stat-icon { background: #e3f2fd; }
.hozi-total-card .hozi-stat-icon .dashicons { color: #0073aa; }

.hozi-tracked-card { border-left-color: #4CAF50; }
.hozi-tracked-card .hozi-stat-icon { background: #e8f5e8; }
.hozi-tracked-card .hozi-stat-icon .dashicons { color: #4CAF50; }

.hozi-delivered-card { border-left-color: #4CAF50; }
.hozi-delivered-card .hozi-stat-icon { background: #e8f5e8; }
.hozi-delivered-card .hozi-stat-icon .dashicons { color: #4CAF50; }

.hozi-rejected-card { border-left-color: #f44336; }
.hozi-rejected-card .hozi-stat-icon { background: #ffebee; }
.hozi-rejected-card .hozi-stat-icon .dashicons { color: #f44336; }

.hozi-postponed-card { border-left-color: #FF9800; }
.hozi-postponed-card .hozi-stat-icon { background: #fff3e0; }
.hozi-postponed-card .hozi-stat-icon .dashicons { color: #FF9800; }

.hozi-exchange-card { border-left-color: #2196F3; }
.hozi-exchange-card .hozi-stat-icon { background: #e3f2fd; }
.hozi-exchange-card .hozi-stat-icon .dashicons { color: #2196F3; }

/* ===== PERFORMANCE SECTION ===== */
.hozi-performance-section {
    margin-top: 30px;
    margin-bottom: 30px;
}

.hozi-performance-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.hozi-performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.hozi-performance-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.hozi-performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.hozi-performance-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.hozi-performance-value {
    font-size: 24px;
    font-weight: 700;
}

.hozi-success-rate { color: #4CAF50; }
.hozi-tracking-rate { color: #0073aa; }
.hozi-rejection-rate { color: #f44336; }

.hozi-progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.hozi-progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease-in-out;
    position: relative;
}

.hozi-success-fill { background: linear-gradient(90deg, #4CAF50, #66BB6A); }
.hozi-tracking-fill { background: linear-gradient(90deg, #0073aa, #2196F3); }
.hozi-rejection-fill { background: linear-gradient(90deg, #f44336, #ef5350); }

.hozi-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.hozi-performance-note {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* ===== QUICK SUMMARY ===== */
.hozi-quick-summary {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    border: 1px solid #e0e0e0;
}

.hozi-summary-item {
    text-align: center;
    flex: 1;
}

.hozi-summary-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.hozi-summary-value {
    display: block;
    font-size: 16px;
    font-weight: 700;
    color: #333;
}

.hozi-needs-update {
    color: #FF9800;
}

.hozi-update-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.hozi-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ===== FILTERS STYLES ===== */
.hozi-filters-form {
    background: #fafafa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.hozi-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.hozi-filter-group {
    display: flex;
    flex-direction: column;
}

.hozi-filter-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 13px;
}

.hozi-filter-group input,
.hozi-filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.hozi-filter-group input:focus,
.hozi-filter-group select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.hozi-filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.hozi-filter-actions .button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
}

.hozi-auto-refresh-btn {
    position: relative;
}

.hozi-refresh-status {
    font-size: 11px;
    opacity: 0.8;
    margin-left: 5px;
}

.hozi-auto-refresh-btn.active {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
}

.hozi-auto-refresh-btn.active .hozi-refresh-status {
    color: white;
}

/* ===== ACTIVE FILTERS ===== */
.hozi-active-filters {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.hozi-active-filters h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
}

.hozi-active-filters-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.hozi-active-filter {
    display: inline-block;
    background: #0073aa;
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* ===== FILTER ANIMATIONS ===== */
.hozi-filters-form {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== ORDERS GRID STYLES ===== */
.hozi-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.hozi-order-tracking-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.hozi-order-tracking-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* ===== ORDER HEADER ===== */
.hozi-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.hozi-order-number strong {
    color: #0073aa;
    font-size: 16px;
}

.hozi-order-date {
    display: block;
    color: #666;
    font-size: 13px;
    margin-top: 5px;
}

.hozi-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    color: white;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hozi-status-badge.hozi-no-status {
    background-color: #999;
}

/* ===== SECTIONS STYLES ===== */
.hozi-customer-section,
.hozi-order-details-section,
.hozi-confirmation-section,
.hozi-tracking-info-section {
    margin-bottom: 15px;
    padding: 15px;
    background: #fafafa;
    border-radius: 8px;
    border-left: 3px solid #0073aa;
}

.hozi-customer-section h4,
.hozi-order-details-section h4,
.hozi-confirmation-section h4,
.hozi-tracking-info-section h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

/* ===== CUSTOMER DETAILS ===== */
.hozi-customer-details > div {
    margin-bottom: 8px;
}

.hozi-customer-name {
    font-size: 16px;
    color: #333;
}

.hozi-phone-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: #4CAF50;
    text-decoration: none;
    font-weight: 600;
}

.hozi-phone-link:hover {
    color: #45a049;
}

.hozi-customer-address {
    display: flex;
    align-items: flex-start;
    gap: 5px;
    color: #666;
    font-size: 14px;
}

/* ===== ORDER SUMMARY ===== */
.hozi-order-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hozi-order-total {
    display: flex;
    align-items: center;
    gap: 8px;
}

.hozi-total-label {
    color: #666;
    font-size: 14px;
}

.hozi-total-value {
    font-size: 18px;
    font-weight: 700;
    color: #0073aa;
}

.hozi-order-items-count {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 14px;
}

/* ===== CONFIRMATION & TRACKING DETAILS ===== */
.hozi-confirmation-details > div,
.hozi-tracking-details > div {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 14px;
}

.hozi-confirmation-notes p,
.hozi-tracking-notes p {
    margin: 5px 0 0 25px;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 2px solid #0073aa;
    font-style: italic;
    color: #555;
}

/* ===== ACTION BUTTONS ===== */
.hozi-order-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.hozi-update-status-btn {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* ===== NO ORDERS STATE ===== */
.hozi-no-orders {
    text-align: center;
    padding: 60px 20px;
    background: #f9f9f9;
    border-radius: 12px;
    border: 2px dashed #ddd;
}

.hozi-no-orders-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.hozi-no-orders h3 {
    color: #666;
    margin-bottom: 10px;
}

.hozi-no-orders p {
    color: #999;
    font-style: italic;
}

@media (max-width: 768px) {
    .hozi-agent-tracking-wrap {
        padding: 10px;
        margin: 0 0 0 -10px;
    }

    .hozi-tracking-header,
    .hozi-tracking-stats-section,
    .hozi-tracking-filters-section,
    .hozi-tracking-orders-section,
    .hozi-quick-actions-section,
    .hozi-notifications-section {
        padding: 15px;
        margin-bottom: 15px;
    }

    .hozi-orders-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .hozi-order-tracking-card {
        padding: 15px;
    }

    .hozi-order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .hozi-order-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .hozi-order-actions {
        flex-direction: column;
        gap: 8px;
    }

    .hozi-customer-section,
    .hozi-order-details-section,
    .hozi-confirmation-section,
    .hozi-tracking-info-section {
        padding: 12px;
        margin-bottom: 12px;
    }

    .hozi-filters-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .hozi-filter-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .hozi-filter-actions .button {
        justify-content: center;
        width: 100%;
    }

    .hozi-active-filters-list {
        flex-direction: column;
        gap: 5px;
    }

    .hozi-active-filter {
        text-align: center;
    }

    .hozi-stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .hozi-stat-card {
        padding: 15px;
    }

    .hozi-stat-content h3 {
        font-size: 2em;
    }

    .hozi-performance-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .hozi-performance-card {
        padding: 15px;
    }

    .hozi-performance-value {
        font-size: 20px;
    }

    .hozi-quick-summary {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    .hozi-summary-item {
        padding: 8px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e0e0e0;
    }

    .hozi-quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .hozi-bulk-actions-card,
    .hozi-quick-status-card,
    .hozi-quick-tools-card {
        padding: 15px;
    }

    .hozi-select-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .hozi-bulk-action-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .hozi-bulk-action-buttons .button {
        justify-content: center;
        width: 100%;
    }

    .hozi-quick-status-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .hozi-tools-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .hozi-tools-grid .button {
        padding: 10px 6px;
        font-size: 12px;
    }

    .hozi-modal-content {
        width: 95%;
        margin: 10px;
        max-height: 95vh;
    }

    .hozi-modal-header,
    .hozi-modal-body,
    .hozi-modal-footer {
        padding: 15px;
    }

    .hozi-status-options {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .hozi-preset-notes {
        flex-direction: column;
        gap: 6px;
    }

    .hozi-preset-note {
        text-align: center;
        padding: 8px 12px;
    }

    .hozi-modal-actions {
        flex-direction: column;
        gap: 8px;
    }

    .hozi-modal-actions .button {
        justify-content: center;
        width: 100%;
    }

    .hozi-shortcuts-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .hozi-notifications-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .hozi-notification-settings-card,
    .hozi-live-notifications-card,
    .hozi-notification-types-card,
    .hozi-sound-test-card {
        padding: 15px;
    }

    .hozi-volume-control {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .hozi-notification-status {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .hozi-sound-tests {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .hozi-desktop-notification {
        width: 90%;
        right: 5%;
        left: 5%;
    }

    .hozi-notification-types {
        gap: 8px;
    }

    .hozi-notification-type {
        padding: 10px;
    }

    .hozi-type-icon {
        width: 35px;
        height: 35px;
    }
}

/* Notification Styles */
.hozi-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 999999;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    max-width: 500px;
    animation: slideInRight 0.3s ease-out;
}

.hozi-notification-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.hozi-notification-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.hozi-notification-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.hozi-notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: auto;
    opacity: 0.7;
}

.hozi-notification-close:hover {
    opacity: 1;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Statistics Update Animation */
.hozi-stat-updated {
    animation: statUpdate 1s ease-out;
}

@keyframes statUpdate {
    0% {
        transform: scale(1);
        color: inherit;
    }
    50% {
        transform: scale(1.1);
        color: #0073aa;
    }
    100% {
        transform: scale(1);
        color: inherit;
    }
}

/* Quick Action Buttons */
.hozi-quick-action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    margin: 2px;
}

.hozi-quick-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.hozi-quick-delivered {
    background: #28a745;
    color: white;
}

.hozi-quick-rejected {
    background: #dc3545;
    color: white;
}

.hozi-quick-postponed {
    background: #ffc107;
    color: #212529;
}

.hozi-quick-exchange {
    background: #17a2b8;
    color: white;
}

.hozi-quick-out-delivery {
    background: #9C27B0;
    color: white;
}

.hozi-quick-not-found {
    background: #FF5722;
    color: white;
}

/* Quick Actions Row */
.hozi-quick-actions-row {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.hozi-quick-actions-row h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.hozi-quick-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.hozi-quick-buttons .hozi-quick-action-btn {
    flex: 1;
    min-width: 120px;
    text-align: center;
    justify-content: center;
    display: flex;
    align-items: center;
    gap: 5px;
}

@media (max-width: 768px) {
    .hozi-quick-buttons {
        flex-direction: column;
    }

    .hozi-quick-buttons .hozi-quick-action-btn {
        min-width: auto;
        width: 100%;
    }
}

/* Loading Animation */
.hozi-spin {
    animation: hozi-spin 1s linear infinite;
}

@keyframes hozi-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hozi-quick-action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Orders Header Styles */
.hozi-orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.hozi-orders-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.hozi-header-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-header-actions .button {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
}

/* Archive Animation for Tracking Page */
.hozi-archived-order {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef) !important;
    border: 2px dashed #28a745 !important;
    opacity: 0.8;
    transform: scale(0.98);
    transition: all 0.8s ease;
    position: relative;
}

.hozi-archived-order::before {
    content: "🗂️ تم الأرشفة";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

@media (max-width: 768px) {
    .hozi-orders-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .hozi-header-actions {
        justify-content: center;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    let autoRefreshInterval = null;
    let autoRefreshActive = false;

    // Auto refresh toggle
    $('#auto-refresh-toggle').on('click', function() {
        const $btn = $(this);
        const $status = $btn.find('.hozi-refresh-status');

        if (autoRefreshActive) {
            // Stop auto refresh
            clearInterval(autoRefreshInterval);
            autoRefreshActive = false;
            $btn.removeClass('active');
            $status.text('<?php _e('متوقف', 'hozi-akadly'); ?>');
        } else {
            // Start auto refresh
            autoRefreshActive = true;
            $btn.addClass('active');
            $status.text('<?php _e('نشط', 'hozi-akadly'); ?>');

            autoRefreshInterval = setInterval(function() {
                // Reload page with current filters
                window.location.reload();
            }, 30000); // Refresh every 30 seconds
        }
    });

    // Quick filter buttons
    function addQuickFilterButtons() {
        const $filtersSection = $('.hozi-tracking-filters-section');
        const $quickFilters = $('<div class="hozi-quick-filters"></div>');

        $quickFilters.html(`
            <h4><?php _e('فلاتر سريعة', 'hozi-akadly'); ?></h4>
            <div class="hozi-quick-filter-buttons">
                <button type="button" class="button hozi-quick-filter" data-filter="needs_update">
                    <span class="dashicons dashicons-clock"></span>
                    <?php _e('بحاجة لتحديث', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-quick-filter" data-filter="delivered">
                    <span class="dashicons dashicons-yes-alt"></span>
                    <?php _e('تم التوصيل', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-quick-filter" data-filter="rejected">
                    <span class="dashicons dashicons-no"></span>
                    <?php _e('مرفوض', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-quick-filter" data-filter="postponed">
                    <span class="dashicons dashicons-clock"></span>
                    <?php _e('مؤجل', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-quick-filter" data-filter="today">
                    <span class="dashicons dashicons-calendar-alt"></span>
                    <?php _e('اليوم', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button hozi-quick-filter" data-filter="week">
                    <span class="dashicons dashicons-calendar"></span>
                    <?php _e('هذا الأسبوع', 'hozi-akadly'); ?>
                </button>
            </div>
        `);

        $filtersSection.prepend($quickFilters);
    }

    // Add quick filter buttons
    addQuickFilterButtons();

    // Handle quick filter clicks
    $(document).on('click', '.hozi-quick-filter', function() {
        const filter = $(this).data('filter');
        const currentUrl = new URL(window.location);

        // Clear existing filters
        currentUrl.searchParams.delete('status_filter');
        currentUrl.searchParams.delete('date_from');
        currentUrl.searchParams.delete('date_to');
        currentUrl.searchParams.delete('search_term');

        switch(filter) {
            case 'needs_update':
                currentUrl.searchParams.set('status_filter', 'needs_update');
                break;
            case 'delivered':
                currentUrl.searchParams.set('status_filter', 'delivered');
                break;
            case 'rejected':
                currentUrl.searchParams.set('status_filter', 'rejected_customer');
                break;
            case 'postponed':
                currentUrl.searchParams.set('status_filter', 'postponed_customer');
                break;
            case 'today':
                const today = new Date().toISOString().split('T')[0];
                currentUrl.searchParams.set('date_from', today);
                currentUrl.searchParams.set('date_to', today);
                break;
            case 'week':
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                currentUrl.searchParams.set('date_from', weekAgo.toISOString().split('T')[0]);
                currentUrl.searchParams.set('date_to', new Date().toISOString().split('T')[0]);
                break;
        }

        window.location.href = currentUrl.toString();
    });

    // Real-time search
    let searchTimeout;
    $('#search_term').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();

        searchTimeout = setTimeout(function() {
            if (searchTerm.length >= 3 || searchTerm.length === 0) {
                // Auto-submit form after 1 second of no typing
                $('#hozi-tracking-filters').submit();
            }
        }, 1000);
    });

    // Form validation
    $('#hozi-tracking-filters').on('submit', function(e) {
        const dateFrom = $('#date_from').val();
        const dateTo = $('#date_to').val();

        if (dateFrom && dateTo && dateFrom > dateTo) {
            e.preventDefault();
            alert('<?php _e('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'hozi-akadly'); ?>');
            return false;
        }
    });

    // Highlight active filters
    function highlightActiveFilters() {
        const urlParams = new URLSearchParams(window.location.search);

        // Highlight form fields with values
        $('.hozi-filter-group input, .hozi-filter-group select').each(function() {
            const $field = $(this);
            const fieldName = $field.attr('name');

            if (urlParams.has(fieldName) && urlParams.get(fieldName)) {
                $field.addClass('hozi-active-field');
            }
        });
    }

    highlightActiveFilters();

    // Update statistics animations
    function animateStatistics() {
        $('.hozi-stat-content h3').each(function() {
            const $this = $(this);
            const finalValue = parseInt($this.text()) || 0;

            $({ counter: 0 }).animate({ counter: finalValue }, {
                duration: 1500,
                easing: 'swing',
                step: function() {
                    $this.text(Math.ceil(this.counter));
                }
            });
        });

        // Animate progress bars
        $('.hozi-progress-fill').each(function() {
            const $this = $(this);
            const width = $this.css('width');
            $this.css('width', '0%').animate({ width: width }, 1000);
        });
    }

    // Run animations on page load
    setTimeout(animateStatistics, 500);

    // Update last update time
    function updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
        $('#last-update-time').text(timeString);
    }

    // Update time every minute
    setInterval(updateLastUpdateTime, 60000);

    // Add click handlers for stat cards (filter by clicking)
    $('.hozi-stat-card').on('click', function() {
        const $card = $(this);
        let filterValue = '';

        if ($card.hasClass('hozi-delivered-card')) {
            filterValue = 'delivered';
        } else if ($card.hasClass('hozi-rejected-card')) {
            filterValue = 'rejected_customer';
        } else if ($card.hasClass('hozi-postponed-card')) {
            filterValue = 'postponed_customer';
        } else if ($card.hasClass('hozi-exchange-card')) {
            filterValue = 'exchange_requested';
        } else if ($card.hasClass('hozi-tracked-card')) {
            // Show all tracked orders
            filterValue = '';
        }

        if (filterValue) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('status_filter', filterValue);
            window.location.href = currentUrl.toString();
        }
    });

    // Add hover effects for stat cards
    $('.hozi-stat-card').hover(
        function() {
            $(this).css('cursor', 'pointer');
            $(this).find('.hozi-stat-content h3').css('color', '#0073aa');
        },
        function() {
            $(this).find('.hozi-stat-content h3').css('color', '#333');
        }
    );

    // Handle quick action buttons
    $(document).on('click', '.hozi-quick-action-btn', function() {
        const $btn = $(this);
        const orderId = $btn.data('order');
        const status = $btn.data('status');

        if (!orderId || !status) {
            showNotification('<?php _e('خطأ في البيانات', 'hozi-akadly'); ?>', 'error');
            return;
        }

        // Show loading state
        $btn.prop('disabled', true);
        const originalText = $btn.html();
        $btn.html('<span class="dashicons dashicons-update hozi-spin"></span> <?php _e('جاري التحديث...', 'hozi-akadly'); ?>');

        // Update order status
        updateOrderStatus(orderId, status, '', '');

        // Reset button after delay
        setTimeout(() => {
            $btn.prop('disabled', false);
            $btn.html(originalText);
        }, 2000);
    });

    // Clear completed orders functionality
    $('#clear-completed-orders').on('click', function() {
        if (confirm('⚠️ تحذير!\n\nهل أنت متأكد من مسح جميع الطلبات المكتملة؟\n\nسيتم نقل الطلبات المكتملة إلى الأرشيف.\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
            const $btn = $(this);
            const originalText = $btn.html();

            // Show loading state
            $btn.prop('disabled', true);
            $btn.html('<span class="dashicons dashicons-update hozi-spin"></span> جاري المسح...');

            // AJAX call to clear completed orders
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'hozi_clear_completed_orders',
                    nonce: '<?php echo wp_create_nonce('hozi_clear_completed_orders'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        showNotification('✅ تم مسح الطلبات المكتملة ونقلها للأرشيف بنجاح!', 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showNotification('❌ فشل في مسح الطلبات: ' + (response.data.message || 'خطأ غير معروف'), 'error');
                        $btn.prop('disabled', false);
                        $btn.html(originalText);
                    }
                },
                error: function() {
                    showNotification('❌ حدث خطأ في الاتصال', 'error');
                    $btn.prop('disabled', false);
                    $btn.html(originalText);
                }
            });
        }
    });

    // ===== QUICK ACTIONS FUNCTIONALITY =====
    let selectedOrders = new Set();
    const reasonCategories = <?php echo json_encode(Hozi_Akadly_Order_Tracker::get_reason_categories()); ?>;

    // Add checkboxes to order cards
    function addOrderCheckboxes() {
        $('.hozi-order-tracking-card').each(function() {
            const $card = $(this);
            const orderId = $card.data('order-id');

            if (!$card.find('.hozi-order-checkbox').length) {
                const $checkbox = $(`
                    <div class="hozi-order-checkbox">
                        <input type="checkbox" id="order-${orderId}" value="${orderId}">
                        <label for="order-${orderId}"></label>
                    </div>
                `);

                $card.find('.hozi-order-header').prepend($checkbox);
            }
        });
    }

    // Initialize checkboxes
    addOrderCheckboxes();

    // Handle order selection
    $(document).on('change', '.hozi-order-checkbox input[type="checkbox"]', function() {
        const orderId = $(this).val();

        if ($(this).is(':checked')) {
            selectedOrders.add(orderId);
        } else {
            selectedOrders.delete(orderId);
        }

        updateSelectedCount();
    });

    // Update selected count
    function updateSelectedCount() {
        const count = selectedOrders.size;
        $('#selected-count').text(count);

        // Enable/disable bulk action buttons
        $('.hozi-bulk-update').prop('disabled', count === 0);

        // Update modal count
        $('#modal-selected-count').text(count);
    }

    // Select all orders
    $('.hozi-select-all').on('click', function() {
        $('.hozi-order-checkbox input[type="checkbox"]').prop('checked', true).trigger('change');
    });

    // Select none
    $('.hozi-select-none').on('click', function() {
        $('.hozi-order-checkbox input[type="checkbox"]').prop('checked', false).trigger('change');
        selectedOrders.clear();
        updateSelectedCount();
    });

    // Quick status buttons
    $('.hozi-quick-status-btn').on('click', function() {
        const status = $(this).data('status');

        if (selectedOrders.size === 0) {
            alert('<?php _e('يرجى تحديد طلبية واحدة على الأقل', 'hozi-akadly'); ?>');
            return;
        }

        openQuickUpdateModal(status);
    });

    // Bulk update buttons
    $('.hozi-bulk-update').on('click', function() {
        const status = $(this).data('status');

        if (selectedOrders.size === 0) {
            alert('<?php _e('يرجى تحديد طلبية واحدة على الأقل', 'hozi-akadly'); ?>');
            return;
        }

        openQuickUpdateModal(status);
    });

    // Open quick update modal
    function openQuickUpdateModal(preselectedStatus = null) {
        // Populate selected orders list
        populateSelectedOrdersList();

        // Preselect status if provided
        if (preselectedStatus) {
            $(`input[name="quick_status"][value="${preselectedStatus}"]`).prop('checked', true).trigger('change');
        }

        // Show modal
        $('#hozi-quick-update-modal').fadeIn(300);
    }

    // Populate selected orders list
    function populateSelectedOrdersList() {
        const $list = $('#selected-orders-list');
        $list.empty();

        selectedOrders.forEach(orderId => {
            const $card = $(`.hozi-order-tracking-card[data-order-id="${orderId}"]`);
            const customerName = $card.find('.hozi-customer-name strong').text();
            const orderTotal = $card.find('.hozi-total-value').text();

            $list.append(`
                <div class="hozi-selected-order-item">
                    <span>#${orderId} - ${customerName}</span>
                    <span>${orderTotal}</span>
                </div>
            `);
        });
    }

    // Handle status selection in modal
    $(document).on('change', 'input[name="quick_status"]', function() {
        const status = $(this).val();
        const $reasonSection = $('#reason-selection');
        const $reasonSelect = $('#quick-reason-select');

        if (reasonCategories[status]) {
            // Populate reason options
            $reasonSelect.empty().append('<option value=""><?php _e('اختر السبب...', 'hozi-akadly'); ?></option>');

            $.each(reasonCategories[status], function(key, value) {
                $reasonSelect.append(`<option value="${key}">${value}</option>`);
            });

            $reasonSection.show();
        } else {
            $reasonSection.hide();
        }
    });

    // Handle preset notes
    $(document).on('click', '.hozi-preset-note', function() {
        const note = $(this).data('note');
        const $textarea = $('#quick-notes-text');

        // Toggle active state
        $(this).toggleClass('active');

        // Add/remove note from textarea
        let currentNotes = $textarea.val();

        if ($(this).hasClass('active')) {
            if (currentNotes) {
                currentNotes += '\n' + note;
            } else {
                currentNotes = note;
            }
        } else {
            currentNotes = currentNotes.replace(note, '').replace(/\n\n/g, '\n').trim();
        }

        $textarea.val(currentNotes);
    });

    // Confirm update button
    $('#confirm-update').on('click', function() {
        const selectedStatus = $('input[name="quick_status"]:checked').val();
        const selectedReason = $('#quick-reason-select').val();
        const notes = $('#quick-notes-text').val();

        if (!selectedStatus) {
            alert('<?php _e('يرجى اختيار الحالة الجديدة', 'hozi-akadly'); ?>');
            return;
        }

        // Show progress
        showUpdateProgress();

        // Perform bulk update
        performBulkUpdate(selectedStatus, selectedReason, notes);
    });

    // Show update progress
    function showUpdateProgress() {
        $('#update-progress').show();
        $('.hozi-modal-actions').hide();

        let progress = 0;
        const total = selectedOrders.size;

        const progressInterval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 90) progress = 90;

            $('#progress-fill').css('width', progress + '%');
            $('#progress-text').text(`<?php _e('جاري التحديث...', 'hozi-akadly'); ?> ${Math.round(progress)}%`);
        }, 200);

        // Store interval for cleanup
        window.progressInterval = progressInterval;
    }

    // Update single order status
    function updateOrderStatus(orderId, status, reason, notes) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_update_tracking_status',
                order_id: orderId,
                status: status,
                reason_category: reason,
                notes: notes,
                nonce: '<?php echo wp_create_nonce('hozi_tracking_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Update statistics
                    updateStatistics(response.data.stats);

                    // Check if order was archived
                    if (response.data.archived) {
                        // Special handling for archived orders
                        showNotification('🗂️ ' + response.data.message, 'success');

                        // Add archive animation to the order card
                        const $orderCard = $(`[data-order-id="${orderId}"]`);
                        if ($orderCard.length) {
                            $orderCard.addClass('hozi-archived-order');

                            // Wait for animation then reload
                            setTimeout(() => {
                                window.location.reload();
                            }, 2500);
                        } else {
                            setTimeout(() => {
                                window.location.reload();
                            }, 1500);
                        }
                    } else {
                        // Normal success handling
                        showNotification(response.data.message || '<?php _e('تم تحديث حالة الطلب بنجاح', 'hozi-akadly'); ?>', 'success');

                        // Refresh page after short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    showNotification(response.data.message || '<?php _e('فشل في تحديث حالة الطلب', 'hozi-akadly'); ?>', 'error');
                }
            },
            error: function() {
                showNotification('<?php _e('حدث خطأ في الاتصال', 'hozi-akadly'); ?>', 'error');
            }
        });
    }

    // Update statistics display
    function updateStatistics(stats) {
        if (!stats) return;

        // Update stat cards
        $('.hozi-total-card h3').text(stats.total_confirmed || 0);
        $('.hozi-tracked-card h3').text(stats.total_tracked || 0);
        $('.hozi-delivered-card h3').text(stats.delivered || 0);
        $('.hozi-rejected-card h3').text(stats.rejected || 0);
        $('.hozi-postponed-card h3').text(stats.postponed || 0);
        $('.hozi-exchange-card h3').text(stats.exchange || 0);

        // Update percentages
        const totalTracked = stats.total_tracked || 0;
        const totalConfirmed = stats.total_confirmed || 0;

        if (totalConfirmed > 0) {
            const trackedPercentage = Math.round((totalTracked / totalConfirmed) * 100);
            $('.hozi-tracked-card .hozi-stat-percentage').text(`${trackedPercentage}% من الإجمالي`);
        }

        if (totalTracked > 0) {
            const deliveredPercentage = Math.round(((stats.delivered || 0) / totalTracked) * 100);
            const rejectedPercentage = Math.round(((stats.rejected || 0) / totalTracked) * 100);
            const postponedPercentage = Math.round(((stats.postponed || 0) / totalTracked) * 100);

            $('.hozi-delivered-card .hozi-stat-percentage').text(`${deliveredPercentage}% من المتتبعة`);
            $('.hozi-rejected-card .hozi-stat-percentage').text(`${rejectedPercentage}% من المتتبعة`);
            $('.hozi-postponed-card .hozi-stat-percentage').text(`${postponedPercentage}% من المتتبعة`);
        }

        // Update needs update count
        const needsUpdate = totalConfirmed - totalTracked;
        $('.hozi-needs-update').text(needsUpdate);

        // Animate updated values
        $('.hozi-stat-content h3').addClass('hozi-stat-updated');
        setTimeout(() => {
            $('.hozi-stat-content h3').removeClass('hozi-stat-updated');
        }, 1000);
    }

    // Show notification
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="hozi-notification hozi-notification-${type}">
                <span class="hozi-notification-message">${message}</span>
                <button class="hozi-notification-close">&times;</button>
            </div>
        `);

        $('body').append($notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            $notification.fadeOut(() => $notification.remove());
        }, 5000);

        // Manual close
        $notification.find('.hozi-notification-close').on('click', () => {
            $notification.fadeOut(() => $notification.remove());
        });
    }

    // Perform bulk update
    function performBulkUpdate(status, reason, notes) {
        const orderIds = Array.from(selectedOrders);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_bulk_update_tracking_status',
                order_ids: orderIds,
                status: status,
                reason: reason,
                notes: notes,
                nonce: '<?php echo wp_create_nonce('hozi_bulk_update_nonce'); ?>'
            },
            success: function(response) {
                clearInterval(window.progressInterval);
                $('#progress-fill').css('width', '100%');

                if (response.success) {
                    $('#progress-text').text('<?php _e('تم التحديث بنجاح!', 'hozi-akadly'); ?>');

                    // Update statistics if provided
                    if (response.data.stats) {
                        updateStatistics(response.data.stats);
                    }

                    // Show success message
                    showNotification(response.data.message, 'success');

                    setTimeout(() => {
                        closeModal();
                        location.reload(); // Refresh page to show updates
                    }, 2000);
                } else {
                    $('#progress-text').text('<?php _e('فشل في التحديث', 'hozi-akadly'); ?>');
                    showNotification(response.data.message || '<?php _e('فشل في تحديث الطلبيات', 'hozi-akadly'); ?>', 'error');

                    setTimeout(() => {
                        closeModal();
                    }, 3000);
                }
            },
            error: function() {
                clearInterval(window.progressInterval);
                $('#progress-fill').css('width', '0%');
                $('#progress-text').text('<?php _e('حدث خطأ في الاتصال', 'hozi-akadly'); ?>');
                showNotification('<?php _e('حدث خطأ في الاتصال', 'hozi-akadly'); ?>', 'error');

                setTimeout(() => {
                    closeModal();
                }, 1000);
            }
        });
    }

    // Close modal function
    function closeModal() {
        $('#hozi-quick-update-modal').fadeOut(300);

        // Reset form
        $('input[name="quick_status"]').prop('checked', false);
        $('#quick-reason-select').empty().hide();
        $('#quick-notes-text').val('');
        $('.hozi-preset-note').removeClass('active');

        // Reset progress
        $('#update-progress').hide();
        $('.hozi-modal-actions').show();
        $('#progress-fill').css('width', '0%');
        $('#progress-text').text('');

        // Clear selections
        selectedOrders.clear();
        $('.hozi-order-checkbox input[type="checkbox"]').prop('checked', false);
        updateSelectedCount();
    }

    // Modal close handlers
    $(document).on('click', '.hozi-modal-close', closeModal);
    $(document).on('click', '.hozi-modal', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Escape key to close modal
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });

    // Refresh data button
    $('.hozi-refresh-data').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.html();

        $btn.prop('disabled', true);
        $btn.html('<span class="dashicons dashicons-update hozi-spin"></span> <?php _e('جاري التحديث...', 'hozi-akadly'); ?>');

        // Refresh statistics
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_get_agent_stats',
                nonce: '<?php echo wp_create_nonce('hozi_akadly_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data.stats) {
                    updateStatistics(response.data.stats);
                    showNotification('<?php _e('تم تحديث البيانات بنجاح', 'hozi-akadly'); ?>', 'success');
                } else {
                    showNotification('<?php _e('فشل في تحديث البيانات', 'hozi-akadly'); ?>', 'error');
                }
            },
            error: function() {
                showNotification('<?php _e('حدث خطأ في الاتصال', 'hozi-akadly'); ?>', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false);
                $btn.html(originalText);
            }
        });
    });

    // Auto-refresh stats every 2 minutes
    setInterval(function() {
        if (!autoRefreshActive) return;

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_get_agent_stats',
                nonce: '<?php echo wp_create_nonce('hozi_akadly_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data.stats) {
                    updateStatistics(response.data.stats);
                }
            }
        });
    }, 120000); // Every 2 minutes

    // Initialize tooltips
    $('[title]').each(function() {
        $(this).attr('data-tooltip', $(this).attr('title'));
        $(this).removeAttr('title');
    });

    // Show tooltips on hover
    $(document).on('mouseenter', '[data-tooltip]', function() {
        const tooltip = $(this).attr('data-tooltip');
        const $tooltip = $(`<div class="hozi-tooltip">${tooltip}</div>`);

        $('body').append($tooltip);

        const rect = this.getBoundingClientRect();
        $tooltip.css({
            top: rect.top - $tooltip.outerHeight() - 5,
            left: rect.left + (rect.width / 2) - ($tooltip.outerWidth() / 2)
        });

        $tooltip.fadeIn(200);

        $(this).data('tooltip-element', $tooltip);
    });

    $(document).on('mouseleave', '[data-tooltip]', function() {
        const $tooltip = $(this).data('tooltip-element');
        if ($tooltip) {
            $tooltip.fadeOut(200, function() {
                $tooltip.remove();
            });
        }
    });

    // Quick tools handlers
    $('.hozi-refresh-data').on('click', function() {
        location.reload();
    });

    $('.hozi-export-data').on('click', function() {
        // Implement export functionality
        alert('<?php _e('ميزة التصدير قيد التطوير', 'hozi-akadly'); ?>');
    });

    $('.hozi-print-report').on('click', function() {
        window.print();
    });

    $('.hozi-keyboard-shortcuts').on('click', function() {
        $('#hozi-shortcuts-modal').fadeIn(300);
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Escape key - close modals
        if (e.key === 'Escape') {
            closeModal();
            $('#hozi-shortcuts-modal').fadeOut(300);
        }

        // Ctrl+A - Select all
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            $('.hozi-select-all').click();
        }

        // Ctrl+D - Deselect all
        if (e.ctrlKey && e.key === 'd') {
            e.preventDefault();
            $('.hozi-select-none').click();
        }

        // Ctrl+U - Quick update
        if (e.ctrlKey && e.key === 'u') {
            e.preventDefault();
            if (selectedOrders.size > 0) {
                openQuickUpdateModal();
            }
        }

        // Ctrl+R - Refresh
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            location.reload();
        }

        // Ctrl+F - Focus search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            $('#search_term').focus();
        }

        // Number keys for quick status (when modal is open)
        if ($('#hozi-quick-update-modal').is(':visible')) {
            if (e.key === '1') {
                $('input[name="quick_status"][value="delivered"]').prop('checked', true).trigger('change');
            } else if (e.key === '2') {
                $('input[name="quick_status"][value="postponed_customer"]').prop('checked', true).trigger('change');
            } else if (e.key === '3') {
                $('input[name="quick_status"][value="rejected_customer"]').prop('checked', true).trigger('change');
            } else if (e.key === '4') {
                $('input[name="quick_status"][value="exchange_requested"]').prop('checked', true).trigger('change');
            } else if (e.key === '5') {
                $('input[name="quick_status"][value="out_for_delivery"]').prop('checked', true).trigger('change');
            } else if (e.key === '6') {
                $('input[name="quick_status"][value="customer_not_found"]').prop('checked', true).trigger('change');
            }
        }
    });

    // ===== NOTIFICATIONS SYSTEM =====
    let notificationSettings = {
        enabled: true,
        sound: true,
        desktop: true,
        volume: 70
    };

    let notificationTypes = {
        new_order: true,
        status_update: true,
        urgent: true,
        reminder: true
    };

    // Load settings from localStorage
    function loadNotificationSettings() {
        const saved = localStorage.getItem('hozi_notification_settings');
        if (saved) {
            notificationSettings = { ...notificationSettings, ...JSON.parse(saved) };
        }

        const savedTypes = localStorage.getItem('hozi_notification_types');
        if (savedTypes) {
            notificationTypes = { ...notificationTypes, ...JSON.parse(savedTypes) };
        }

        // Apply settings to UI
        $('#notifications-enabled').prop('checked', notificationSettings.enabled);
        $('#sound-enabled').prop('checked', notificationSettings.sound);
        $('#desktop-notifications').prop('checked', notificationSettings.desktop);
        $('#notification-volume').val(notificationSettings.volume);
        $('.hozi-volume-value').text(notificationSettings.volume + '%');

        $('.notification-type-toggle').each(function() {
            const type = $(this).data('type');
            $(this).prop('checked', notificationTypes[type]);
        });
    }

    // Save settings to localStorage
    function saveNotificationSettings() {
        localStorage.setItem('hozi_notification_settings', JSON.stringify(notificationSettings));
        localStorage.setItem('hozi_notification_types', JSON.stringify(notificationTypes));
    }

    // Initialize settings
    loadNotificationSettings();

    // Handle settings changes
    $('#notifications-enabled').on('change', function() {
        notificationSettings.enabled = $(this).is(':checked');
        saveNotificationSettings();
    });

    $('#sound-enabled').on('change', function() {
        notificationSettings.sound = $(this).is(':checked');
        saveNotificationSettings();
    });

    $('#desktop-notifications').on('change', function() {
        notificationSettings.desktop = $(this).is(':checked');
        saveNotificationSettings();

        if ($(this).is(':checked')) {
            requestNotificationPermission();
        }
    });

    $('#notification-volume').on('input', function() {
        const volume = $(this).val();
        notificationSettings.volume = volume;
        $('.hozi-volume-value').text(volume + '%');
        saveNotificationSettings();
    });

    $('.notification-type-toggle').on('change', function() {
        const type = $(this).data('type');
        notificationTypes[type] = $(this).is(':checked');
        saveNotificationSettings();
    });

    // Request notification permission
    function requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    showNotificationFeedItem('success', '<?php _e('تم تفعيل إشعارات سطح المكتب', 'hozi-akadly'); ?>', '<?php _e('سيتم إشعارك بالتحديثات الجديدة', 'hozi-akadly'); ?>');
                } else {
                    showNotificationFeedItem('warning', '<?php _e('تم رفض إشعارات سطح المكتب', 'hozi-akadly'); ?>', '<?php _e('يمكنك تفعيلها من إعدادات المتصفح', 'hozi-akadly'); ?>');
                }
            });
        }
    }

    // Sound files (using data URLs for simple beeps)
    const notificationSounds = {
        new_order: createBeepSound(800, 0.3, 'sine'),
        status_update: createBeepSound(600, 0.2, 'square'),
        urgent: createBeepSound(1000, 0.5, 'sawtooth'),
        reminder: createBeepSound(400, 0.4, 'triangle')
    };

    // Create beep sound using Web Audio API
    function createBeepSound(frequency, duration, type = 'sine') {
        return function() {
            if (!notificationSettings.sound) return;

            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(notificationSettings.volume / 100, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);

            // Show sound indicator
            showSoundIndicator();
        };
    }

    // Show sound indicator
    function showSoundIndicator() {
        const $indicator = $('<div class="hozi-sound-indicator"><span class="dashicons dashicons-controls-volumeon"></span></div>');
        $('body').append($indicator);

        setTimeout(() => {
            $indicator.remove();
        }, 600);
    }

    // Test sound buttons
    $('.hozi-test-sound').on('click', function() {
        const soundType = $(this).data('sound');
        const $btn = $(this);

        $btn.addClass('playing');
        notificationSounds[soundType]();

        setTimeout(() => {
            $btn.removeClass('playing');
        }, 500);
    });

    // Test desktop notification
    $('.hozi-test-notification').on('click', function() {
        showDesktopNotification(
            '<?php _e('إشعار تجريبي', 'hozi-akadly'); ?>',
            '<?php _e('هذا إشعار تجريبي من نظام أكدلي', 'hozi-akadly'); ?>',
            'info'
        );
    });

    // Show notification in feed
    function showNotificationFeedItem(type, title, message) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const $notification = $(`
            <div class="hozi-notification-item hozi-${type}">
                <div class="hozi-notification-icon">
                    <span class="dashicons dashicons-${getIconForType(type)}"></span>
                </div>
                <div class="hozi-notification-content">
                    <div class="hozi-notification-title">${title}</div>
                    <div class="hozi-notification-message">${message}</div>
                    <div class="hozi-notification-time">${timeString}</div>
                </div>
            </div>
        `);

        $('#notification-feed').prepend($notification);

        // Remove old notifications (keep only last 10)
        $('#notification-feed .hozi-notification-item').slice(10).remove();

        // Update last check time
        $('#last-check-time').text(timeString);
    }

    // Get icon for notification type
    function getIconForType(type) {
        const icons = {
            info: 'info',
            success: 'yes-alt',
            warning: 'warning',
            error: 'dismiss'
        };
        return icons[type] || 'info';
    }

    // Show desktop notification
    function showDesktopNotification(title, message, type = 'info', actions = []) {
        if (!notificationSettings.enabled || !notificationSettings.desktop) return;

        // Try browser notification first
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: '<?php echo plugin_dir_url(__FILE__); ?>../assets/icon-128x128.png',
                badge: '<?php echo plugin_dir_url(__FILE__); ?>../assets/icon-64x64.png'
            });

            notification.onclick = function() {
                window.focus();
                notification.close();
            };

            setTimeout(() => {
                notification.close();
            }, 5000);
        } else {
            // Fallback to custom notification
            showCustomDesktopNotification(title, message, type, actions);
        }
    }

    // Show custom desktop notification
    function showCustomDesktopNotification(title, message, type = 'info', actions = []) {
        const $notification = $(`
            <div class="hozi-desktop-notification">
                <div class="hozi-desktop-notification-header">
                    <div class="hozi-desktop-notification-title">${title}</div>
                    <button class="hozi-desktop-notification-close">&times;</button>
                </div>
                <div class="hozi-desktop-notification-body">
                    <div class="hozi-desktop-notification-message">${message}</div>
                    <div class="hozi-desktop-notification-actions"></div>
                </div>
            </div>
        `);

        // Add actions
        if (actions.length > 0) {
            const $actionsContainer = $notification.find('.hozi-desktop-notification-actions');
            actions.forEach(action => {
                const $btn = $(`<button class="button ${action.class || ''}">${action.text}</button>`);
                $btn.on('click', action.callback || function() {});
                $actionsContainer.append($btn);
            });
        }

        $('body').append($notification);

        // Auto close after 5 seconds
        setTimeout(() => {
            closeDesktopNotification($notification);
        }, 5000);

        // Close button handler
        $notification.find('.hozi-desktop-notification-close').on('click', () => {
            closeDesktopNotification($notification);
        });
    }

    // Close desktop notification
    function closeDesktopNotification($notification) {
        $notification.addClass('hozi-slide-out');
        setTimeout(() => {
            $notification.remove();
        }, 300);
    }

    // Simulate real-time notifications (for demo)
    function simulateNotifications() {
        const notifications = [
            {
                type: 'success',
                title: '<?php _e('تم تحديث الطلبية #1234', 'hozi-akadly'); ?>',
                message: '<?php _e('تم تغيير الحالة إلى "تم التوصيل"', 'hozi-akadly'); ?>',
                sound: 'status_update'
            },
            {
                type: 'info',
                title: '<?php _e('طلبية جديدة #1235', 'hozi-akadly'); ?>',
                message: '<?php _e('تم تخصيص طلبية جديدة لك', 'hozi-akadly'); ?>',
                sound: 'new_order'
            },
            {
                type: 'warning',
                title: '<?php _e('تذكير: طلبيات معلقة', 'hozi-akadly'); ?>',
                message: '<?php _e('لديك 3 طلبيات تحتاج متابعة', 'hozi-akadly'); ?>',
                sound: 'reminder'
            },
            {
                type: 'error',
                title: '<?php _e('طلبية عاجلة #1236', 'hozi-akadly'); ?>',
                message: '<?php _e('طلبية تحتاج متابعة فورية', 'hozi-akadly'); ?>',
                sound: 'urgent'
            }
        ];

        let index = 0;
        setInterval(() => {
            if (notificationSettings.enabled && index < notifications.length) {
                const notification = notifications[index];

                // Show in feed
                showNotificationFeedItem(notification.type, notification.title, notification.message);

                // Play sound
                if (notificationTypes[notification.sound]) {
                    notificationSounds[notification.sound]();
                }

                // Show desktop notification
                if (notificationTypes[notification.sound]) {
                    showDesktopNotification(notification.title, notification.message, notification.type);
                }

                index = (index + 1) % notifications.length;
            }
        }, 30000); // Every 30 seconds
    }

    // Start simulation after 10 seconds
    setTimeout(simulateNotifications, 10000);

    // Connection status simulation
    function updateConnectionStatus() {
        const isConnected = Math.random() > 0.1; // 90% chance of being connected
        const $status = $('#connection-status');
        const $dot = $status.find('.hozi-status-dot');
        const $text = $status.find('.hozi-status-text');

        if (isConnected) {
            $dot.removeClass('hozi-disconnected').addClass('hozi-connected');
            $text.text('<?php _e('متصل', 'hozi-akadly'); ?>');
        } else {
            $dot.removeClass('hozi-connected').addClass('hozi-disconnected');
            $text.text('<?php _e('غير متصل', 'hozi-akadly'); ?>');
        }

        // Update last check time
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        $('#last-check-time').text(timeString);
    }

    // Update connection status every 5 seconds
    setInterval(updateConnectionStatus, 5000);

    // Global notification function for other parts of the system
    window.hoziShowNotification = function(type, title, message, soundType = null) {
        if (!notificationSettings.enabled) return;

        // Show in feed
        showNotificationFeedItem(type, title, message);

        // Play sound
        if (soundType && notificationTypes[soundType] && notificationSounds[soundType]) {
            notificationSounds[soundType]();
        }

        // Show desktop notification
        if (notificationTypes[soundType] || soundType === null) {
            showDesktopNotification(title, message, type);
        }
    };

    // Initialize notification permission on page load
    if (notificationSettings.desktop && 'Notification' in window && Notification.permission === 'default') {
        setTimeout(() => {
            requestNotificationPermission();
        }, 2000);
    }

    // Add CSS for active fields
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .hozi-active-field {
                border-color: #0073aa !important;
                background-color: #f0f8ff !important;
            }

            .hozi-order-checkbox {
                position: relative;
                margin-right: 10px;
            }

            .hozi-order-checkbox input[type="checkbox"] {
                display: none;
            }

            .hozi-order-checkbox label {
                display: block;
                width: 20px;
                height: 20px;
                border: 2px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
            }

            .hozi-order-checkbox label:hover {
                border-color: #0073aa;
                background: #f0f8ff;
            }

            .hozi-order-checkbox input[type="checkbox"]:checked + label {
                background: #0073aa;
                border-color: #0073aa;
            }

            .hozi-order-checkbox input[type="checkbox"]:checked + label:after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-weight: bold;
                font-size: 12px;
            }

            .hozi-quick-filters {
                margin-bottom: 20px;
                padding: 15px;
                background: #f0f8ff;
                border-radius: 8px;
                border-left: 4px solid #0073aa;
            }

            .hozi-quick-filters h4 {
                margin: 0 0 10px 0;
                color: #0073aa;
                font-size: 14px;
            }

            .hozi-quick-filter-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }

            .hozi-quick-filter {
                display: flex;
                align-items: center;
                gap: 5px;
                padding: 6px 12px;
                font-size: 13px;
                border-radius: 4px;
                transition: all 0.3s ease;
            }

            .hozi-quick-filter:hover {
                background-color: #0073aa;
                color: white;
                border-color: #0073aa;
            }

            /* Mobile-Friendly Header Actions */
            .hozi-header-actions-mobile {
                text-align: center;
                margin-bottom: 20px;
                padding: 15px;
                background: white;
                border-radius: 8px;
                border: 1px solid #e1e5e9;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .hozi-simple-header {
                text-align: center;
                margin-bottom: 20px;
                padding: 15px 0;
                border-bottom: 2px solid #e1e5e9;
            }

            .hozi-simple-header h1 {
                margin: 0 0 10px 0;
                font-size: 24px;
                color: #333;
            }

            .hozi-tracking-subtitle {
                margin: 0;
                color: #666;
                font-size: 14px;
            }

            /* Navigation Tabs */
            .hozi-nav-tabs {
                display: flex;
                gap: 0;
                margin: 20px 0;
                border-bottom: 2px solid #e1e5e9;
                background: white;
                border-radius: 8px 8px 0 0;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .hozi-nav-tab {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 16px 24px;
                text-decoration: none;
                color: #666;
                background: #f8f9fa;
                border: none;
                border-bottom: 3px solid transparent;
                transition: all 0.3s ease;
                font-weight: 500;
                position: relative;
                flex: 1;
                justify-content: center;
            }

            .hozi-nav-tab:hover {
                background: #e9ecef;
                color: #495057;
                text-decoration: none;
            }

            .hozi-nav-tab-active {
                background: white !important;
                color: #4f46e5 !important;
                border-bottom-color: #4f46e5 !important;
                font-weight: 600;
            }

            .hozi-nav-tab-active::before {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #4f46e5, #7c3aed);
            }

            .hozi-nav-tab .dashicons {
                font-size: 18px;
                width: 18px;
                height: 18px;
            }

            @media (max-width: 768px) {
                .hozi-quick-filter-buttons {
                    flex-direction: column;
                }

                .hozi-quick-filter {
                    justify-content: center;
                    width: 100%;
                }

                .hozi-simple-header h1 {
                    font-size: 20px;
                }

                .hozi-tracking-subtitle {
                    font-size: 13px;
                }

                .hozi-nav-tabs {
                    flex-direction: column;
                    gap: 0;
                }

                .hozi-nav-tab {
                    padding: 12px 16px;
                    text-align: center;
                }

                .hozi-header-actions-mobile {
                    padding: 12px;
                }
            }
        `)
        .appendTo('head');
});
</script>
