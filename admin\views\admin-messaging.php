<?php
/**
 * Admin Messaging Interface
 * Allows admin to send messages to agents
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get all agents - Fixed query
$agents = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'hozi_akadly_agent',
            'value' => '1',
            'compare' => '='
        )
    ),
    'fields' => array('ID', 'display_name', 'user_email')
));

// Debug: Check if we have agents
if (empty($agents)) {
    // Fallback: Get users with agent role or capability
    $agents = get_users(array(
        'capability' => 'hozi_view_assigned_orders'
    ));
}

// If still empty, try another approach
if (empty($agents)) {
    // Get all users and check manually
    $all_users = get_users();
    $agents = array();

    foreach ($all_users as $user) {
        $is_agent = get_user_meta($user->ID, 'hozi_akadly_agent', true);
        if ($is_agent == '1' || $is_agent === true) {
            $agents[] = $user;
        }
    }
}

// Handle message sending
if (isset($_POST['send_message']) && wp_verify_nonce($_POST['hozi_messaging_nonce'], 'hozi_messaging_action')) {
    $recipient_type = sanitize_text_field($_POST['recipient_type']);
    $recipient_id = intval($_POST['recipient_id']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);
    $priority = sanitize_text_field($_POST['priority']);
    $requires_response = isset($_POST['requires_response']) ? 1 : 0;

    global $wpdb;
    $table_name = $wpdb->prefix . 'hozi_messages';

    if ($recipient_type === 'all_agents') {
        // Send to all agents
        foreach ($agents as $agent) {
            $wpdb->insert(
                $table_name,
                array(
                    'sender_id' => get_current_user_id(),
                    'recipient_id' => $agent->ID,
                    'recipient_type' => 'agent',
                    'message_type' => 'general',
                    'subject' => $subject,
                    'message' => $message,
                    'priority' => $priority,
                    'category' => 'general',
                    'requires_response' => $requires_response,
                    'created_at' => current_time('mysql'),
                    'is_read' => 0
                ),
                array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%d')
            );
        }
        $success_message = 'تم إرسال الرسالة لجميع الوكلاء بنجاح!';
    } else {
        // Send to specific agent
        $result = $wpdb->insert(
            $table_name,
            array(
                'sender_id' => get_current_user_id(),
                'recipient_id' => $recipient_id,
                'recipient_type' => 'agent',
                'message_type' => 'general',
                'subject' => $subject,
                'message' => $message,
                'priority' => $priority,
                'category' => 'general',
                'requires_response' => $requires_response,
                'created_at' => current_time('mysql'),
                'is_read' => 0
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%d')
        );

        if ($result) {
            $success_message = 'تم إرسال الرسالة بنجاح!';
        } else {
            $error_message = 'حدث خطأ أثناء إرسال الرسالة.';
        }
    }
}

// Get recent messages (exclude archived)
global $wpdb;
$table_name = $wpdb->prefix . 'hozi_messages';

// Check if archived columns exist
$columns = $wpdb->get_col("SHOW COLUMNS FROM {$table_name}");
$has_archived = in_array('is_archived', $columns);

if ($has_archived) {
    $recent_messages = $wpdb->get_results(
        "SELECT m.*,
                sender.display_name as sender_name,
                recipient.display_name as recipient_name
         FROM {$table_name} m
         LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
         LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
         WHERE (m.is_archived IS NULL OR m.is_archived = 0)
         ORDER BY m.created_at DESC
         LIMIT 20"
    );
} else {
    $recent_messages = $wpdb->get_results(
        "SELECT m.*,
                sender.display_name as sender_name,
                recipient.display_name as recipient_name
         FROM {$table_name} m
         LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
         LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
         ORDER BY m.created_at DESC
         LIMIT 20"
    );
}
?>

<div class="wrap">
    <h1><?php _e('مراسلة الوكلاء', 'hozi-akadly'); ?></h1>

    <?php if (isset($success_message)): ?>
        <div class="notice notice-success is-dismissible">
            <p><?php echo esc_html($success_message); ?></p>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="notice notice-error is-dismissible">
            <p><?php echo esc_html($error_message); ?></p>
        </div>
    <?php endif; ?>

    <?php if (current_user_can('manage_options') && empty($agents)): ?>
        <div class="notice notice-warning">
            <p><strong>تحذير:</strong> لم يتم العثور على أي وكلاء. تأكد من إضافة وكلاء من صفحة "الوكلاء" أولاً.</p>
        </div>
    <?php endif; ?>

    <!-- Message Tabs -->
    <div class="hozi-message-tabs">
        <button class="hozi-tab-btn active" data-tab="inbox">📥 الرسائل الواردة</button>
        <button class="hozi-tab-btn" data-tab="archived">📁 الرسائل المؤرشفة</button>
        <button class="hozi-tab-btn" data-tab="compose">📤 إرسال رسالة</button>
    </div>

    <div class="hozi-admin-messaging-container">
        <!-- Send Message Form -->
        <div class="hozi-message-form-container hozi-tab-content" data-tab="compose" style="display: none;">
            <h2>📤 إرسال رسالة جديدة</h2>

            <form method="post" class="hozi-admin-message-form">
                <?php wp_nonce_field('hozi_messaging_action', 'hozi_messaging_nonce'); ?>

                <div class="hozi-form-row">
                    <div class="hozi-form-group">
                        <label for="recipient_type">المرسل إليه:</label>
                        <select name="recipient_type" id="recipient_type" required>
                            <option value="">اختر المستقبل</option>
                            <option value="all_agents">جميع الوكلاء</option>
                            <option value="specific_agent">وكيل محدد</option>
                        </select>
                    </div>

                    <div class="hozi-form-group" id="specific_agent_group" style="display: none;">
                        <label for="recipient_id">الوكيل:</label>
                        <select name="recipient_id" id="recipient_id">
                            <option value="">اختر الوكيل</option>
                            <?php if (!empty($agents)): ?>
                                <?php foreach ($agents as $agent): ?>
                                    <option value="<?php echo esc_attr($agent->ID); ?>">
                                        <?php echo esc_html($agent->display_name . ' (' . $agent->user_email . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <option value="" disabled>لا يوجد وكلاء مسجلين</option>
                            <?php endif; ?>
                        </select>

                        <?php if (current_user_can('manage_options')): ?>
                        <small style="color: #666; display: block; margin-top: 5px;">
                            عدد الوكلاء المتاحين: <?php echo count($agents); ?>
                        </small>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="hozi-form-row">
                    <div class="hozi-form-group">
                        <label for="priority">الأولوية:</label>
                        <select name="priority" id="priority" required>
                            <option value="normal">عادية</option>
                            <option value="urgent">عاجلة</option>
                            <option value="low">منخفضة</option>
                        </select>
                    </div>

                    <div class="hozi-form-group">
                        <label>
                            <input type="checkbox" name="requires_response" value="1">
                            يتطلب رد
                        </label>
                    </div>
                </div>

                <div class="hozi-form-group">
                    <label for="subject">الموضوع:</label>
                    <input type="text" name="subject" id="subject" placeholder="موضوع الرسالة..." required>
                </div>

                <div class="hozi-form-group">
                    <label for="message">الرسالة:</label>
                    <textarea name="message" id="message" rows="6" placeholder="اكتب رسالتك هنا..." required></textarea>
                </div>

                <div class="hozi-form-actions">
                    <button type="submit" name="send_message" class="button button-primary">
                        📤 إرسال الرسالة
                    </button>
                </div>
            </form>
        </div>

        <!-- Recent Messages -->
        <div class="hozi-recent-messages-container hozi-tab-content" data-tab="inbox">
            <h2>📬 الرسائل الأخيرة</h2>

            <?php if (!empty($recent_messages)): ?>
                <div class="hozi-messages-list">
                    <?php foreach ($recent_messages as $msg): ?>
                        <div class="hozi-message-item <?php echo $msg->priority === 'urgent' ? 'urgent' : ''; ?>">
                            <div class="hozi-message-header">
                                <div class="hozi-message-info">
                                    <strong><?php echo esc_html($msg->subject); ?></strong>
                                    <span class="hozi-message-meta">
                                        من: <?php echo esc_html($msg->sender_name); ?>
                                        إلى: <?php echo esc_html($msg->recipient_name); ?>
                                    </span>
                                </div>
                                <div class="hozi-message-time">
                                    <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($msg->created_at))); ?>
                                </div>
                            </div>
                            <div class="hozi-message-content">
                                <?php echo esc_html(wp_trim_words($msg->message, 20)); ?>
                            </div>
                            <div class="hozi-message-status">
                                <?php if ($msg->is_read): ?>
                                    <span class="status-read">✅ مقروءة</span>
                                <?php else: ?>
                                    <span class="status-unread">📩 غير مقروءة</span>
                                <?php endif; ?>

                                <?php if ($msg->priority === 'urgent'): ?>
                                    <span class="priority-urgent">🔴 عاجلة</span>
                                <?php endif; ?>

                                <?php if ($msg->requires_response): ?>
                                    <span class="requires-response">💬 يتطلب رد</span>
                                <?php endif; ?>

                                <div class="hozi-message-actions" style="margin-top: 10px;">
                                    <button type="button" class="button button-small hozi-view-message"
                                            data-message-id="<?php echo esc_attr($msg->id); ?>">
                                        👁️ عرض كامل
                                    </button>
                                    <button type="button" class="button button-small button-primary hozi-reply-message"
                                            data-message-id="<?php echo esc_attr($msg->id); ?>"
                                            data-sender-id="<?php echo esc_attr($msg->sender_id); ?>"
                                            data-sender-name="<?php echo esc_attr($msg->sender_name); ?>"
                                            data-subject="<?php echo esc_attr($msg->subject); ?>">
                                        💬 رد
                                    </button>
                                    <button type="button" class="button button-small hozi-archive-message"
                                            data-message-id="<?php echo esc_attr($msg->id); ?>"
                                            title="أرشفة الرسالة">
                                        📁 أرشفة
                                    </button>
                                    <button type="button" class="button button-small button-link-delete hozi-delete-message"
                                            data-message-id="<?php echo esc_attr($msg->id); ?>"
                                            title="حذف الرسالة">
                                        🗑️ حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="hozi-no-messages">
                    <p>لا توجد رسائل حتى الآن.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Archived Messages -->
        <div class="hozi-recent-messages-container hozi-tab-content" data-tab="archived" style="display: none;">
            <h2>📁 الرسائل المؤرشفة</h2>

            <?php
            // Get archived messages - Admin sees all, others see only their own
            if (current_user_can('manage_options')) {
                // Admin sees all archived messages
                $archived_messages = $wpdb->get_results(
                    "SELECT m.*,
                            sender.display_name as sender_name,
                            recipient.display_name as recipient_name
                     FROM {$wpdb->prefix}hozi_messages m
                     LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
                     LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
                     WHERE m.is_archived = 1
                     ORDER BY m.archived_at DESC
                     LIMIT 20"
                );
            } else {
                // Regular users see only their own archived messages
                $archived_messages = $wpdb->get_results($wpdb->prepare(
                    "SELECT m.*,
                            sender.display_name as sender_name,
                            recipient.display_name as recipient_name
                     FROM {$wpdb->prefix}hozi_messages m
                     LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
                     LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
                     WHERE (m.sender_id = %d OR m.recipient_id = %d)
                     AND m.is_archived = 1
                     ORDER BY m.archived_at DESC
                     LIMIT 20",
                    get_current_user_id(),
                    get_current_user_id()
                ));
            }
            ?>

            <?php if (!empty($archived_messages)): ?>
                <div class="hozi-messages-list">
                    <?php foreach ($archived_messages as $msg): ?>
                        <div class="hozi-message-item archived <?php echo $msg->priority === 'urgent' ? 'urgent' : ''; ?>">
                            <div class="hozi-message-header">
                                <div class="hozi-message-info">
                                    <strong><?php echo esc_html($msg->subject); ?></strong>
                                    <span class="hozi-message-meta">
                                        من: <?php echo esc_html($msg->sender_name); ?>
                                        إلى: <?php echo esc_html($msg->recipient_name); ?>
                                        <span class="archived-badge">📁 مؤرشفة</span>
                                    </span>
                                </div>
                                <div class="hozi-message-time">
                                    أُرشفت: <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($msg->archived_at))); ?>
                                </div>
                            </div>
                            <div class="hozi-message-content">
                                <?php echo esc_html(wp_trim_words($msg->message, 20)); ?>
                            </div>
                            <div class="hozi-message-status">
                                <span class="status-archived">📁 مؤرشفة</span>

                                <?php if ($msg->priority === 'urgent'): ?>
                                    <span class="priority-urgent">🔴 عاجلة</span>
                                <?php endif; ?>

                                <div class="hozi-message-actions" style="margin-top: 10px;">
                                    <button type="button" class="button button-small hozi-view-message"
                                            data-message-id="<?php echo esc_attr($msg->id); ?>">
                                        👁️ عرض كامل
                                    </button>
                                    <button type="button" class="button button-small hozi-unarchive-message"
                                            data-message-id="<?php echo esc_attr($msg->id); ?>"
                                            title="إلغاء الأرشفة">
                                        📤 إلغاء الأرشفة
                                    </button>
                                    <button type="button" class="button button-small button-link-delete hozi-delete-message"
                                            data-message-id="<?php echo esc_attr($msg->id); ?>"
                                            title="حذف الرسالة">
                                        🗑️ حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="hozi-no-messages">
                    <p>لا توجد رسائل مؤرشفة حتى الآن.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Message View Modal -->
<div id="hozi-message-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content">
        <div class="hozi-modal-header">
            <h3 id="hozi-modal-subject">عرض الرسالة</h3>
            <span class="hozi-modal-close">&times;</span>
        </div>
        <div class="hozi-modal-body">
            <div id="hozi-modal-message-content">
                <!-- Message content will be loaded here -->
            </div>
        </div>
        <div class="hozi-modal-footer">
            <button type="button" class="button" id="hozi-modal-close-btn">إغلاق</button>
            <button type="button" class="button button-primary" id="hozi-modal-reply-btn">💬 رد</button>
        </div>
    </div>
</div>

<!-- Reply Modal -->
<div id="hozi-reply-modal" class="hozi-modal" style="display: none;">
    <div class="hozi-modal-content">
        <div class="hozi-modal-header">
            <h3>💬 رد على الرسالة</h3>
            <span class="hozi-modal-close">&times;</span>
        </div>
        <div class="hozi-modal-body">
            <form id="hozi-reply-form">
                <input type="hidden" id="reply-parent-id" name="parent_message_id">
                <input type="hidden" id="reply-recipient-id" name="recipient_id">
                <input type="hidden" name="recipient_type" value="agent">
                <input type="hidden" name="message_type" value="general">
                <input type="hidden" name="category" value="general">

                <div class="hozi-form-group">
                    <label>الموضوع:</label>
                    <input type="text" id="reply-subject" name="subject" required>
                </div>

                <div class="hozi-form-group">
                    <label>الرد:</label>
                    <textarea name="message" rows="6" placeholder="اكتب ردك هنا..." required style="min-height: 120px;"></textarea>
                </div>

                <div class="hozi-form-group">
                    <label>الأولوية:</label>
                    <select name="priority">
                        <option value="normal">عادي</option>
                        <option value="high">مهم</option>
                        <option value="urgent">عاجل</option>
                    </select>
                </div>

                <div class="hozi-form-group">
                    <label>
                        <input type="checkbox" name="requires_response" value="1">
                        يتطلب رد
                    </label>
                </div>
            </form>
        </div>
        <div class="hozi-modal-footer">
            <button type="button" class="button" id="hozi-reply-close-btn">إلغاء</button>
            <button type="button" class="button button-primary" id="hozi-send-reply-btn">📤 إرسال الرد</button>
        </div>
    </div>
</div>

<style>
/* Message Tabs */
.hozi-message-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid #e1e5e9;
    padding-bottom: 10px;
}

.hozi-tab-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    padding: 12px 20px;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: none;
}

.hozi-tab-btn:hover {
    background: #e9ecef;
    color: #333;
}

.hozi-tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

.hozi-tab-content {
    display: none;
}

.hozi-tab-content.active {
    display: block;
}

.hozi-admin-messaging-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.hozi-message-form-container,
.hozi-recent-messages-container {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.hozi-message-form-container h2,
.hozi-recent-messages-container h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.hozi-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.hozi-form-group {
    margin-bottom: 20px;
}

.hozi-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.hozi-form-group input,
.hozi-form-group select,
.hozi-form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
}

.hozi-form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.hozi-form-actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.hozi-messages-list {
    max-height: 500px;
    overflow-y: auto;
}

.hozi-message-item {
    padding: 15px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.hozi-message-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hozi-message-item.urgent {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.hozi-message-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.hozi-message-info strong {
    display: block;
    color: #333;
    margin-bottom: 5px;
}

.hozi-message-meta {
    font-size: 12px;
    color: #666;
}

.hozi-message-time {
    font-size: 12px;
    color: #999;
}

.hozi-message-content {
    color: #555;
    line-height: 1.5;
    margin-bottom: 10px;
}

.hozi-message-status {
    display: flex;
    gap: 10px;
    font-size: 12px;
}

.status-read { color: #28a745; }
.status-unread { color: #007cba; }
.status-archived { color: #6c757d; }
.priority-urgent { color: #dc3545; }
.requires-response { color: #ffc107; }

.hozi-message-item.archived {
    background: #f8f9fa;
    border-left: 4px solid #6c757d;
}

.archived-badge {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    margin-left: 10px;
}

.hozi-no-messages {
    text-align: center;
    padding: 40px;
    color: #666;
}

/* Modal Styles */
.hozi-modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(2px);
}

.hozi-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.hozi-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hozi-modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.hozi-modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.hozi-modal-close:hover {
    opacity: 1;
}

.hozi-modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

.hozi-modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: left;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

.hozi-modal-footer .button {
    margin-left: 10px;
}

.hozi-message-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.hozi-message-actions .button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.2;
}

/* Message details styles */
.message-details {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.message-meta {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.message-meta p {
    margin: 5px 0;
    font-size: 14px;
}

.message-meta strong {
    color: #333;
    font-weight: 600;
}

.message-body h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 16px;
}

.message-text {
    background: white;
    padding: 20px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    line-height: 1.6;
    font-size: 14px;
    color: #555;
    min-height: 100px;
}

@media (max-width: 600px) {
    .hozi-modal-content {
        width: 95%;
        margin: 2% auto;
    }

    .hozi-modal-body {
        padding: 15px;
    }

    .message-meta {
        padding: 10px;
    }

    .message-text {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .hozi-admin-messaging-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .hozi-form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Tab switching
    $('.hozi-tab-btn').on('click', function() {
        var targetTab = $(this).data('tab');

        // Update active tab button
        $('.hozi-tab-btn').removeClass('active');
        $(this).addClass('active');

        // Show/hide tab content
        $('.hozi-tab-content').removeClass('active').hide();
        $('.hozi-tab-content[data-tab="' + targetTab + '"]').addClass('active').show();

        // Adjust grid layout for compose tab
        if (targetTab === 'compose') {
            $('.hozi-admin-messaging-container').css('grid-template-columns', '1fr');
        } else {
            $('.hozi-admin-messaging-container').css('grid-template-columns', '1fr 1fr');
        }
    });

    // Show inbox tab by default
    $('.hozi-tab-content[data-tab="inbox"]').addClass('active').show();

    // Show/hide specific agent dropdown
    $('#recipient_type').on('change', function() {
        var selectedValue = $(this).val();
        console.log('Selected recipient type:', selectedValue);

        if (selectedValue === 'specific_agent') {
            $('#specific_agent_group').slideDown();
            $('#recipient_id').prop('required', true);
            console.log('Showing agent dropdown');
        } else {
            $('#specific_agent_group').slideUp();
            $('#recipient_id').prop('required', false);
            console.log('Hiding agent dropdown');
        }
    });

    // Debug: Log available agents
    var agentCount = $('#recipient_id option').length - 1; // Exclude the first "choose" option
    console.log('Available agents count:', agentCount);

    // Form validation and AJAX submission
    $('.hozi-admin-message-form').on('submit', function(e) {
        e.preventDefault();

        var recipientType = $('#recipient_type').val();
        if (recipientType === 'specific_agent') {
            var agentId = $('#recipient_id').val();
            if (!agentId) {
                alert('يرجى اختيار وكيل محدد');
                return false;
            }
        }

        // Send via AJAX
        var formData = new FormData(this);
        formData.append('action', 'hozi_send_message');

        // Set recipient_id for all_agents
        if (recipientType === 'all_agents') {
            formData.set('recipient_id', 'all_agents');
            formData.set('recipient_type', 'all_agents');
        }

        var $submitBtn = $(this).find('button[type="submit"]');
        $submitBtn.prop('disabled', true).text('جاري الإرسال...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert('✅ ' + response.data.message);
                    location.reload();
                } else {
                    alert('❌ فشل في الإرسال: ' + response.data);
                }
            },
            error: function() {
                alert('❌ حدث خطأ في الإرسال');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text('📤 إرسال الرسالة');
            }
        });
    });

    // Message actions
    $(document).on('click', '.hozi-view-message', function() {
        var messageId = $(this).data('message-id');
        viewMessage(messageId);
    });

    $(document).on('click', '.hozi-reply-message', function() {
        var messageId = $(this).data('message-id');
        var senderId = $(this).data('sender-id');
        var senderName = $(this).data('sender-name');
        var subject = $(this).data('subject');
        showReplyModal(messageId, senderId, senderName, subject);
    });

    // Modal controls
    $(document).on('click', '.hozi-modal-close, #hozi-modal-close-btn, #hozi-reply-close-btn', function() {
        $('.hozi-modal').hide();
    });

    $(document).on('click', '#hozi-modal-reply-btn', function() {
        var messageId = $(this).data('message-id');
        var senderId = $(this).data('sender-id');
        var senderName = $(this).data('sender-name');
        var subject = $(this).data('subject');
        $('#hozi-message-modal').hide();
        showReplyModal(messageId, senderId, senderName, subject);
    });

    $(document).on('click', '#hozi-send-reply-btn', function() {
        sendReply();
    });

    // Close modal when clicking outside
    $(document).on('click', '.hozi-modal', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
});

// View message function
function viewMessage(messageId) {
    console.log('📖 Viewing message:', messageId);

    $.post(ajaxurl, {
        action: 'hozi_get_message_details',
        message_id: messageId,
        nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>'
    }, function(response) {
        if (response.success) {
            var message = response.data;
            $('#hozi-modal-subject').text(message.subject);

            var content = `
                <div class="message-details">
                    <div class="message-meta">
                        <p><strong>من:</strong> ${message.sender_name}</p>
                        <p><strong>إلى:</strong> ${message.recipient_name}</p>
                        <p><strong>التاريخ:</strong> ${message.created_at}</p>
                        <p><strong>الأولوية:</strong> ${message.priority}</p>
                    </div>
                    <div class="message-body">
                        <h4>الرسالة:</h4>
                        <div class="message-text">${message.message}</div>
                    </div>
                </div>
            `;

            $('#hozi-modal-message-content').html(content);

            // Set reply button data
            $('#hozi-modal-reply-btn').data({
                'message-id': messageId,
                'sender-id': message.sender_id,
                'sender-name': message.sender_name,
                'subject': message.subject
            });

            $('#hozi-message-modal').show();
        } else {
            alert('فشل في تحميل الرسالة: ' + response.data);
        }
    }).fail(function() {
        alert('حدث خطأ في تحميل الرسالة');
    });
}

// Show reply modal
function showReplyModal(messageId, senderId, senderName, subject) {
    console.log('💬 Replying to message:', messageId);

    $('#reply-parent-id').val(messageId);
    $('#reply-recipient-id').val(senderId);
    $('#reply-subject').val('رد: ' + subject);

    // Clear previous message
    $('#hozi-reply-form textarea[name="message"]').val('');

    $('#hozi-reply-modal').show();
}

// Send reply function
function sendReply() {
    console.log('📤 Sending reply...');

    var formData = new FormData($('#hozi-reply-form')[0]);
    formData.append('action', 'hozi_send_message');
    formData.append('nonce', '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>');

    $('#hozi-send-reply-btn').prop('disabled', true).text('جاري الإرسال...');

    $.ajax({
        url: ajaxurl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                alert('✅ تم إرسال الرد بنجاح!');
                $('#hozi-reply-modal').hide();
                location.reload(); // Refresh to show new message
            } else {
                alert('❌ فشل في إرسال الرد: ' + response.data);
            }
        },
        error: function() {
            alert('❌ حدث خطأ في إرسال الرد');
        },
        complete: function() {
            $('#hozi-send-reply-btn').prop('disabled', false).text('📤 إرسال الرد');
        }
    });
}

// Add archive and delete buttons to existing messages
$('.hozi-message-actions').each(function() {
    var messageId = $(this).find('.hozi-view-message').data('message-id');

    if (messageId && !$(this).find('.hozi-archive-message').length) {
        $(this).append(`
            <button type="button" class="button button-small hozi-archive-message"
                    data-message-id="${messageId}"
                    title="أرشفة الرسالة">
                📁 أرشفة
            </button>
            <button type="button" class="button button-small button-link-delete hozi-delete-message"
                    data-message-id="${messageId}"
                    title="حذف الرسالة">
                🗑️ حذف
            </button>
        `);
    }
});

// Archive message
$(document).on('click', '.hozi-archive-message', function() {
    var messageId = $(this).data('message-id');

    if (confirm('هل أنت متأكد من أرشفة هذه الرسالة؟')) {
        $.post(ajaxurl, {
            action: 'hozi_archive_message',
            message_id: messageId,
            nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                alert('✅ تم أرشفة الرسالة بنجاح');
                location.reload();
            } else {
                alert('❌ فشل في أرشفة الرسالة: ' + response.data);
            }
        }).fail(function() {
            alert('❌ حدث خطأ في أرشفة الرسالة');
        });
    }
});

// Delete message
$(document).on('click', '.hozi-delete-message', function() {
    var messageId = $(this).data('message-id');

    if (confirm('هل أنت متأكد من حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        $.post(ajaxurl, {
            action: 'hozi_delete_message_enhanced',
            message_id: messageId,
            nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                alert('✅ تم حذف الرسالة بنجاح');
                location.reload();
            } else {
                alert('❌ فشل في حذف الرسالة: ' + response.data);
            }
        }).fail(function() {
            alert('❌ حدث خطأ في حذف الرسالة');
        });
    }
});

// Unarchive message
$(document).on('click', '.hozi-unarchive-message', function() {
    var messageId = $(this).data('message-id');

    if (confirm('هل أنت متأكد من إلغاء أرشفة هذه الرسالة؟')) {
        $.post(ajaxurl, {
            action: 'hozi_unarchive_message',
            message_id: messageId,
            nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                alert('✅ تم إلغاء أرشفة الرسالة بنجاح');
                location.reload();
            } else {
                alert('❌ فشل في إلغاء أرشفة الرسالة: ' + response.data);
            }
        }).fail(function() {
            alert('❌ حدث خطأ في إلغاء أرشفة الرسالة');
        });
    }
});

</script>

<style>
.hozi-message-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.hozi-message-actions .button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.2;
}

.button-link-delete {
    color: #d63638 !important;
    border-color: #d63638 !important;
}

.button-link-delete:hover {
    background: #d63638 !important;
    color: white !important;
}
</style>
