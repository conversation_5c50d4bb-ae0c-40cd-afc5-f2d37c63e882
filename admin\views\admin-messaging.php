<?php
/**
 * Admin Messaging Interface
 * Allows admin to send messages to agents
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get all agents - Fixed query
$agents = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'hozi_akadly_agent',
            'value' => '1',
            'compare' => '='
        )
    ),
    'fields' => array('ID', 'display_name', 'user_email')
));

// Debug: Check if we have agents
if (empty($agents)) {
    // Fallback: Get users with agent role or capability
    $agents = get_users(array(
        'capability' => 'hozi_view_assigned_orders'
    ));
}

// If still empty, try another approach
if (empty($agents)) {
    // Get all users and check manually
    $all_users = get_users();
    $agents = array();

    foreach ($all_users as $user) {
        $is_agent = get_user_meta($user->ID, 'hozi_akadly_agent', true);
        if ($is_agent == '1' || $is_agent === true) {
            $agents[] = $user;
        }
    }
}

// Handle message sending
if (isset($_POST['send_message']) && wp_verify_nonce($_POST['hozi_messaging_nonce'], 'hozi_messaging_action')) {
    $recipient_type = sanitize_text_field($_POST['recipient_type']);
    $recipient_id = intval($_POST['recipient_id']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);
    $priority = sanitize_text_field($_POST['priority']);
    $requires_response = isset($_POST['requires_response']) ? 1 : 0;
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'hozi_messages';
    
    if ($recipient_type === 'all_agents') {
        // Send to all agents
        foreach ($agents as $agent) {
            $wpdb->insert(
                $table_name,
                array(
                    'sender_id' => get_current_user_id(),
                    'recipient_id' => $agent->ID,
                    'recipient_type' => 'agent',
                    'message_type' => 'general',
                    'subject' => $subject,
                    'message' => $message,
                    'priority' => $priority,
                    'category' => 'general',
                    'requires_response' => $requires_response,
                    'created_at' => current_time('mysql'),
                    'is_read' => 0
                ),
                array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%d')
            );
        }
        $success_message = 'تم إرسال الرسالة لجميع الوكلاء بنجاح!';
    } else {
        // Send to specific agent
        $result = $wpdb->insert(
            $table_name,
            array(
                'sender_id' => get_current_user_id(),
                'recipient_id' => $recipient_id,
                'recipient_type' => 'agent',
                'message_type' => 'general',
                'subject' => $subject,
                'message' => $message,
                'priority' => $priority,
                'category' => 'general',
                'requires_response' => $requires_response,
                'created_at' => current_time('mysql'),
                'is_read' => 0
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%d')
        );
        
        if ($result) {
            $success_message = 'تم إرسال الرسالة بنجاح!';
        } else {
            $error_message = 'حدث خطأ أثناء إرسال الرسالة.';
        }
    }
}

// Get recent messages
global $wpdb;
$table_name = $wpdb->prefix . 'hozi_messages';
$recent_messages = $wpdb->get_results(
    "SELECT m.*, 
            sender.display_name as sender_name,
            recipient.display_name as recipient_name
     FROM {$table_name} m
     LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
     LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
     ORDER BY m.created_at DESC
     LIMIT 20"
);
?>

<div class="wrap">
    <h1><?php _e('مراسلة الوكلاء', 'hozi-akadly'); ?></h1>
    
    <?php if (isset($success_message)): ?>
        <div class="notice notice-success is-dismissible">
            <p><?php echo esc_html($success_message); ?></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
        <div class="notice notice-error is-dismissible">
            <p><?php echo esc_html($error_message); ?></p>
        </div>
    <?php endif; ?>

    <?php if (current_user_can('manage_options') && empty($agents)): ?>
        <div class="notice notice-warning">
            <p><strong>تحذير:</strong> لم يتم العثور على أي وكلاء. تأكد من إضافة وكلاء من صفحة "الوكلاء" أولاً.</p>
        </div>
    <?php endif; ?>

    <div class="hozi-admin-messaging-container">
        <!-- Send Message Form -->
        <div class="hozi-message-form-container">
            <h2>📤 إرسال رسالة جديدة</h2>
            
            <form method="post" class="hozi-admin-message-form">
                <?php wp_nonce_field('hozi_messaging_action', 'hozi_messaging_nonce'); ?>
                
                <div class="hozi-form-row">
                    <div class="hozi-form-group">
                        <label for="recipient_type">المرسل إليه:</label>
                        <select name="recipient_type" id="recipient_type" required>
                            <option value="">اختر المستقبل</option>
                            <option value="all_agents">جميع الوكلاء</option>
                            <option value="specific_agent">وكيل محدد</option>
                        </select>
                    </div>
                    
                    <div class="hozi-form-group" id="specific_agent_group" style="display: none;">
                        <label for="recipient_id">الوكيل:</label>
                        <select name="recipient_id" id="recipient_id">
                            <option value="">اختر الوكيل</option>
                            <?php if (!empty($agents)): ?>
                                <?php foreach ($agents as $agent): ?>
                                    <option value="<?php echo esc_attr($agent->ID); ?>">
                                        <?php echo esc_html($agent->display_name . ' (' . $agent->user_email . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <option value="" disabled>لا يوجد وكلاء مسجلين</option>
                            <?php endif; ?>
                        </select>

                        <?php if (current_user_can('manage_options')): ?>
                        <small style="color: #666; display: block; margin-top: 5px;">
                            عدد الوكلاء المتاحين: <?php echo count($agents); ?>
                        </small>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="hozi-form-row">
                    <div class="hozi-form-group">
                        <label for="priority">الأولوية:</label>
                        <select name="priority" id="priority" required>
                            <option value="normal">عادية</option>
                            <option value="urgent">عاجلة</option>
                            <option value="low">منخفضة</option>
                        </select>
                    </div>
                    
                    <div class="hozi-form-group">
                        <label>
                            <input type="checkbox" name="requires_response" value="1">
                            يتطلب رد
                        </label>
                    </div>
                </div>
                
                <div class="hozi-form-group">
                    <label for="subject">الموضوع:</label>
                    <input type="text" name="subject" id="subject" placeholder="موضوع الرسالة..." required>
                </div>
                
                <div class="hozi-form-group">
                    <label for="message">الرسالة:</label>
                    <textarea name="message" id="message" rows="6" placeholder="اكتب رسالتك هنا..." required></textarea>
                </div>
                
                <div class="hozi-form-actions">
                    <button type="submit" name="send_message" class="button button-primary">
                        📤 إرسال الرسالة
                    </button>
                </div>
            </form>
        </div>

        <!-- Recent Messages -->
        <div class="hozi-recent-messages-container">
            <h2>📬 الرسائل الأخيرة</h2>
            
            <?php if (!empty($recent_messages)): ?>
                <div class="hozi-messages-list">
                    <?php foreach ($recent_messages as $msg): ?>
                        <div class="hozi-message-item <?php echo $msg->priority === 'urgent' ? 'urgent' : ''; ?>">
                            <div class="hozi-message-header">
                                <div class="hozi-message-info">
                                    <strong><?php echo esc_html($msg->subject); ?></strong>
                                    <span class="hozi-message-meta">
                                        من: <?php echo esc_html($msg->sender_name); ?> 
                                        إلى: <?php echo esc_html($msg->recipient_name); ?>
                                    </span>
                                </div>
                                <div class="hozi-message-time">
                                    <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($msg->created_at))); ?>
                                </div>
                            </div>
                            <div class="hozi-message-content">
                                <?php echo esc_html(wp_trim_words($msg->message, 20)); ?>
                            </div>
                            <div class="hozi-message-status">
                                <?php if ($msg->is_read): ?>
                                    <span class="status-read">✅ مقروءة</span>
                                <?php else: ?>
                                    <span class="status-unread">📩 غير مقروءة</span>
                                <?php endif; ?>
                                
                                <?php if ($msg->priority === 'urgent'): ?>
                                    <span class="priority-urgent">🔴 عاجلة</span>
                                <?php endif; ?>
                                
                                <?php if ($msg->requires_response): ?>
                                    <span class="requires-response">💬 يتطلب رد</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="hozi-no-messages">
                    <p>لا توجد رسائل حتى الآن.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.hozi-admin-messaging-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.hozi-message-form-container,
.hozi-recent-messages-container {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.hozi-message-form-container h2,
.hozi-recent-messages-container h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.hozi-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.hozi-form-group {
    margin-bottom: 20px;
}

.hozi-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.hozi-form-group input,
.hozi-form-group select,
.hozi-form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
}

.hozi-form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.hozi-form-actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.hozi-messages-list {
    max-height: 500px;
    overflow-y: auto;
}

.hozi-message-item {
    padding: 15px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.hozi-message-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hozi-message-item.urgent {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.hozi-message-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.hozi-message-info strong {
    display: block;
    color: #333;
    margin-bottom: 5px;
}

.hozi-message-meta {
    font-size: 12px;
    color: #666;
}

.hozi-message-time {
    font-size: 12px;
    color: #999;
}

.hozi-message-content {
    color: #555;
    line-height: 1.5;
    margin-bottom: 10px;
}

.hozi-message-status {
    display: flex;
    gap: 10px;
    font-size: 12px;
}

.status-read { color: #28a745; }
.status-unread { color: #007cba; }
.priority-urgent { color: #dc3545; }
.requires-response { color: #ffc107; }

.hozi-no-messages {
    text-align: center;
    padding: 40px;
    color: #666;
}

@media (max-width: 768px) {
    .hozi-admin-messaging-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .hozi-form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Show/hide specific agent dropdown
    $('#recipient_type').on('change', function() {
        var selectedValue = $(this).val();
        console.log('Selected recipient type:', selectedValue);

        if (selectedValue === 'specific_agent') {
            $('#specific_agent_group').slideDown();
            $('#recipient_id').prop('required', true);
            console.log('Showing agent dropdown');
        } else {
            $('#specific_agent_group').slideUp();
            $('#recipient_id').prop('required', false);
            console.log('Hiding agent dropdown');
        }
    });

    // Debug: Log available agents
    var agentCount = $('#recipient_id option').length - 1; // Exclude the first "choose" option
    console.log('Available agents count:', agentCount);

    // Form validation
    $('form').on('submit', function(e) {
        var recipientType = $('#recipient_type').val();
        if (recipientType === 'specific_agent') {
            var agentId = $('#recipient_id').val();
            if (!agentId) {
                e.preventDefault();
                alert('يرجى اختيار وكيل محدد');
                return false;
            }
        }
    });
});
</script>
