<?php
/**
 * Test Delivery Tracking Fix
 * 
 * This file tests the fix for delivery tracking permissions and auto-transfer functionality.
 * Place this file in your WordPress root directory and access via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🧪 اختبار إصلاح نظام متابعة التوصيل - Akadly</h1>";

// Check if we're running the test
$run_test = isset($_GET['run_test']) && $_GET['run_test'] == '1';
$test_agent_id = isset($_GET['test_agent_id']) ? intval($_GET['test_agent_id']) : 0;

if ($run_test && $test_agent_id) {
    echo "<h2>⚡ جاري تشغيل الاختبار للوكيل ID: {$test_agent_id}...</h2>";
    
    try {
        global $wpdb;
        
        // Step 1: Check delivery tracking settings
        echo "<h3>1️⃣ فحص إعدادات متابعة التوصيل:</h3>";
        $tracking_enabled = get_option('hozi_akadly_enable_delivery_tracking', 'yes');
        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');
        
        echo "<p>تفعيل نظام متابعة التوصيل: " . ($tracking_enabled === 'yes' ? '✅ مفعل' : '❌ معطل') . "</p>";
        echo "<p>صلاحيات الوصول: " . $access_control . "</p>";
        
        if ($tracking_enabled !== 'yes') {
            echo "<p style='color: red;'>❌ نظام متابعة التوصيل معطل! يجب تفعيله من الإعدادات.</p>";
            return;
        }
        
        if ($access_control !== 'assigned_agent_only') {
            echo "<p style='color: orange;'>⚠️ صلاحيات الوصول ليست 'الوكيل المخصص فقط'. الاختبار سيستمر لكن النتائج قد تختلف.</p>";
        }
        
        // Step 2: Check agent exists
        echo "<h3>2️⃣ فحص الوكيل:</h3>";
        $agent = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
            $test_agent_id
        ));
        
        if (!$agent) {
            echo "<p style='color: red;'>❌ الوكيل غير موجود!</p>";
            return;
        }
        
        echo "<p>✅ الوكيل موجود: {$agent->name} (ID: {$agent->id})</p>";
        echo "<p>حالة الوكيل: " . ($agent->is_active ? '✅ نشط' : '❌ غير نشط') . "</p>";
        
        // Step 3: Check confirmed orders for this agent
        echo "<h3>3️⃣ فحص الطلبات المؤكدة للوكيل:</h3>";
        $confirmed_orders = $wpdb->get_results($wpdb->prepare(
            "SELECT oa.*, p.post_status 
             FROM {$wpdb->prefix}hozi_order_assignments oa
             LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
             WHERE oa.agent_id = %d 
             AND oa.confirmation_status = 'confirmed'
             AND oa.is_archived = 0",
            $test_agent_id
        ));
        
        if (empty($confirmed_orders)) {
            echo "<p style='color: orange;'>⚠️ لا توجد طلبات مؤكدة غير مؤرشفة لهذا الوكيل.</p>";
        } else {
            echo "<p>✅ عدد الطلبات المؤكدة: " . count($confirmed_orders) . "</p>";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>حالة الطلب</th><th>مؤرشف</th></tr>";
            foreach ($confirmed_orders as $order) {
                echo "<tr>";
                echo "<td>{$order->order_id}</td>";
                echo "<td>{$order->confirmed_at}</td>";
                echo "<td>{$order->post_status}</td>";
                echo "<td>" . ($order->is_archived ? 'نعم' : 'لا') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Step 4: Check tracking table
        echo "<h3>4️⃣ فحص جدول التتبع:</h3>";
        $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
        
        if (!$table_exists) {
            echo "<p style='color: red;'>❌ جدول التتبع غير موجود!</p>";
        } else {
            echo "<p>✅ جدول التتبع موجود</p>";
            
            // Check orders in tracking for this agent
            $tracking_orders = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE agent_id = %d",
                $test_agent_id
            ));
            
            echo "<p>عدد الطلبات في جدول التتبع للوكيل: " . count($tracking_orders) . "</p>";
            
            if (!empty($tracking_orders)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr><th>رقم الطلب</th><th>الحالة</th><th>تاريخ التحديث</th><th>الملاحظات</th></tr>";
                foreach ($tracking_orders as $track) {
                    echo "<tr>";
                    echo "<td>{$track->order_id}</td>";
                    echo "<td>{$track->status}</td>";
                    echo "<td>{$track->updated_at}</td>";
                    echo "<td>" . substr($track->notes, 0, 50) . "...</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        
        // Step 5: Test the new permission function
        echo "<h3>5️⃣ اختبار دالة الصلاحيات الجديدة:</h3>";
        
        // Get agent user ID
        $agent_user_id = $agent->user_id;
        if (!$agent_user_id) {
            echo "<p style='color: orange;'>⚠️ الوكيل غير مرتبط بمستخدم WordPress</p>";
        } else {
            echo "<p>معرف المستخدم للوكيل: {$agent_user_id}</p>";
            
            // Test can_access_delivery_tracking
            $can_access = Hozi_Akadly_Delivery_Tracking_Permissions::can_access_delivery_tracking($agent_user_id);
            echo "<p>يمكن الوصول لمتابعة التوصيل: " . ($can_access ? '✅ نعم' : '❌ لا') . "</p>";
            
            // Test get_trackable_orders_for_user
            echo "<p>🔄 جاري اختبار دالة جلب الطلبات القابلة للتتبع...</p>";
            $trackable_orders = Hozi_Akadly_Delivery_Tracking_Permissions::get_trackable_orders_for_user($agent_user_id);
            
            echo "<p>عدد الطلبات القابلة للتتبع: " . count($trackable_orders) . "</p>";
            
            if (!empty($trackable_orders)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr><th>رقم الطلب</th></tr>";
                foreach ($trackable_orders as $order) {
                    echo "<tr><td>{$order->order_id}</td></tr>";
                }
                echo "</table>";
            }
        }
        
        // Step 6: Test auto-transfer function
        echo "<h3>6️⃣ اختبار دالة النقل التلقائي:</h3>";
        $transferred_count = Hozi_Akadly_Delivery_Tracking_Permissions::ensure_confirmed_orders_in_tracking($test_agent_id);
        
        if ($transferred_count > 0) {
            echo "<p style='color: green;'>✅ تم نقل {$transferred_count} طلب إلى جدول التتبع تلقائياً</p>";
        } else {
            echo "<p>✅ جميع الطلبات المؤكدة موجودة في جدول التتبع</p>";
        }
        
        echo "<h3>🎯 النتيجة النهائية:</h3>";
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<p><strong>✅ تم اختبار الإصلاح بنجاح!</strong></p>";
        echo "<p>الآن يجب أن تظهر الطلبات المؤكدة في صفحة متابعة التوصيل للوكيل المخصص.</p>";
        echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-delivery-tracking') . "' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>انتقل إلى متابعة التوصيل</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ حدث خطأ أثناء الاختبار: " . $e->getMessage() . "</p>";
    }
} else {
    // Display test form
    echo "<h2>🛠️ وصف الاختبار</h2>";
    echo "<p>هذا الاختبار يتحقق من إصلاح مشكلة عدم ظهور الطلبات المؤكدة في متابعة التوصيل للوكيل المخصص.</p>";
    
    echo "<h2>🔧 ما سيقوم به الاختبار:</h2>";
    echo "<ol>";
    echo "<li>فحص إعدادات متابعة التوصيل</li>";
    echo "<li>التحقق من وجود الوكيل المحدد</li>";
    echo "<li>فحص الطلبات المؤكدة للوكيل</li>";
    echo "<li>التحقق من جدول التتبع</li>";
    echo "<li>اختبار دالة الصلاحيات الجديدة</li>";
    echo "<li>تشغيل دالة النقل التلقائي</li>";
    echo "</ol>";
    
    // Get available agents
    global $wpdb;
    $agents = $wpdb->get_results("SELECT id, name FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 ORDER BY name");
    
    if (empty($agents)) {
        echo "<p style='color: red;'>❌ لا توجد وكلاء نشطين في النظام!</p>";
    } else {
        echo "<h2>👨‍💻 اختيار الوكيل للاختبار:</h2>";
        echo "<form method='GET'>";
        echo "<input type='hidden' name='run_test' value='1'>";
        echo "<p><label>اختر الوكيل:</label></p>";
        echo "<select name='test_agent_id' required style='padding: 8px; margin: 10px 0;'>";
        echo "<option value=''>-- اختر وكيل --</option>";
        foreach ($agents as $agent) {
            echo "<option value='{$agent->id}'>{$agent->name} (ID: {$agent->id})</option>";
        }
        echo "</select><br>";
        echo "<button type='submit' style='background: #0073aa; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer;'>تشغيل الاختبار</button>";
        echo "</form>";
    }
}

echo "<hr>";
echo "<p>تم إنشاء هذا الاختبار بواسطة فريق Akadly - " . date('Y-m-d H:i:s') . "</p>";
?>
