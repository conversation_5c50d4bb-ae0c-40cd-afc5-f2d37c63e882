<?php
/**
 * Messaging System V2 - Complete Rewrite
 * Simple, efficient, and bug-free messaging system
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Messaging_System_V2 {
    
    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'hozi_messages_v2';
        
        // Initialize hooks
        add_action('wp_ajax_hozi_send_message_v2', array($this, 'ajax_send_message'));
        add_action('wp_ajax_hozi_get_messages_v2', array($this, 'ajax_get_messages'));
        add_action('wp_ajax_hozi_mark_read_v2', array($this, 'ajax_mark_read'));
        add_action('wp_ajax_hozi_get_unread_count_v2', array($this, 'ajax_get_unread_count'));
        add_action('wp_ajax_hozi_archive_message_v2', array($this, 'ajax_archive_message'));
        add_action('wp_ajax_hozi_delete_message_v2', array($this, 'ajax_delete_message'));
        
        // Create table on activation
        $this->create_table();
    }
    
    /**
     * Create messages table with proper structure
     */
    public function create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            sender_id bigint(20) NOT NULL,
            recipient_id bigint(20) DEFAULT NULL,
            recipient_type enum('user', 'all_agents', 'all_admins') DEFAULT 'user',
            subject varchar(255) NOT NULL,
            message text NOT NULL,
            priority enum('normal', 'high', 'urgent') DEFAULT 'normal',
            is_read tinyint(1) DEFAULT 0,
            is_archived tinyint(1) DEFAULT 0,
            is_deleted tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            read_at datetime DEFAULT NULL,
            archived_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY sender_id (sender_id),
            KEY recipient_id (recipient_id),
            KEY recipient_type (recipient_type),
            KEY is_read (is_read),
            KEY is_archived (is_archived),
            KEY is_deleted (is_deleted),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Send message - Simple and reliable
     */
    public function send_message($sender_id, $recipient_type, $recipient_id, $subject, $message, $priority = 'normal') {
        global $wpdb;
        
        // Validate inputs
        if (empty($subject) || empty($message)) {
            return new WP_Error('invalid_input', 'الموضوع والرسالة مطلوبان');
        }
        
        $messages_sent = 0;
        
        if ($recipient_type === 'all_agents') {
            // Send to all agents
            $agents = $this->get_all_agents();
            
            foreach ($agents as $agent) {
                $result = $wpdb->insert(
                    $this->table_name,
                    array(
                        'sender_id' => $sender_id,
                        'recipient_id' => $agent->ID,
                        'recipient_type' => 'user',
                        'subject' => $subject,
                        'message' => $message,
                        'priority' => $priority,
                        'created_at' => current_time('mysql')
                    ),
                    array('%d', '%d', '%s', '%s', '%s', '%s', '%s')
                );
                
                if ($result) {
                    $messages_sent++;
                }
            }
            
            return $messages_sent;
            
        } else {
            // Send to specific user
            $result = $wpdb->insert(
                $this->table_name,
                array(
                    'sender_id' => $sender_id,
                    'recipient_id' => $recipient_id,
                    'recipient_type' => 'user',
                    'subject' => $subject,
                    'message' => $message,
                    'priority' => $priority,
                    'created_at' => current_time('mysql')
                ),
                array('%d', '%d', '%s', '%s', '%s', '%s', '%s')
            );
            
            return $result ? $wpdb->insert_id : false;
        }
    }
    
    /**
     * Get messages for user - Simple query
     */
    public function get_messages($user_id, $limit = 50, $archived = false) {
        global $wpdb;
        
        $archived_condition = $archived ? 'is_archived = 1' : 'is_archived = 0';
        
        $messages = $wpdb->get_results($wpdb->prepare("
            SELECT m.*, 
                   sender.display_name as sender_name,
                   recipient.display_name as recipient_name
            FROM {$this->table_name} m
            LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
            LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
            WHERE (m.recipient_id = %d OR m.sender_id = %d)
            AND m.is_deleted = 0
            AND {$archived_condition}
            ORDER BY m.created_at DESC
            LIMIT %d
        ", $user_id, $user_id, $limit));
        
        return $messages;
    }
    
    /**
     * Get unread count - Simple and accurate
     */
    public function get_unread_count($user_id) {
        global $wpdb;
        
        return $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) 
            FROM {$this->table_name} 
            WHERE recipient_id = %d 
            AND is_read = 0 
            AND is_deleted = 0 
            AND is_archived = 0
        ", $user_id));
    }
    
    /**
     * Mark message as read
     */
    public function mark_as_read($message_id, $user_id) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_name,
            array(
                'is_read' => 1,
                'read_at' => current_time('mysql')
            ),
            array(
                'id' => $message_id,
                'recipient_id' => $user_id
            ),
            array('%d', '%s'),
            array('%d', '%d')
        );
    }
    
    /**
     * Archive message
     */
    public function archive_message($message_id, $user_id) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_name,
            array(
                'is_archived' => 1,
                'archived_at' => current_time('mysql')
            ),
            array(
                'id' => $message_id,
                'recipient_id' => $user_id
            ),
            array('%d', '%s'),
            array('%d', '%d')
        );
    }
    
    /**
     * Delete message
     */
    public function delete_message($message_id, $user_id) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_name,
            array('is_deleted' => 1),
            array(
                'id' => $message_id,
                'recipient_id' => $user_id
            ),
            array('%d'),
            array('%d', '%d')
        );
    }
    
    /**
     * Get all agents - Simple method
     */
    private function get_all_agents() {
        $agents = get_users(array(
            'meta_query' => array(
                array(
                    'key' => 'hozi_akadly_agent',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));
        
        // Fallback method
        if (empty($agents)) {
            $agents = get_users(array('capability' => 'hozi_view_assigned_orders'));
        }
        
        return $agents;
    }
    
    /**
     * AJAX: Send message
     */
    public function ajax_send_message() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');
        
        $sender_id = get_current_user_id();
        $recipient_type = sanitize_text_field($_POST['recipient_type'] ?? '');
        $recipient_id = intval($_POST['recipient_id'] ?? 0);
        $subject = sanitize_text_field($_POST['subject'] ?? '');
        $message = sanitize_textarea_field($_POST['message'] ?? '');
        $priority = sanitize_text_field($_POST['priority'] ?? 'normal');
        
        $result = $this->send_message($sender_id, $recipient_type, $recipient_id, $subject, $message, $priority);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } elseif ($result) {
            wp_send_json_success(array(
                'message' => 'تم إرسال الرسالة بنجاح',
                'sent_count' => is_numeric($result) ? $result : 1
            ));
        } else {
            wp_send_json_error('فشل في إرسال الرسالة');
        }
    }
    
    /**
     * AJAX: Get messages
     */
    public function ajax_get_messages() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $archived = !empty($_POST['archived']);
        $limit = intval($_POST['limit'] ?? 50);
        
        $messages = $this->get_messages($user_id, $limit, $archived);
        
        wp_send_json_success($messages);
    }
    
    /**
     * AJAX: Mark as read
     */
    public function ajax_mark_read() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');
        
        $message_id = intval($_POST['message_id']);
        $user_id = get_current_user_id();
        
        $result = $this->mark_as_read($message_id, $user_id);
        
        if ($result) {
            wp_send_json_success('تم تحديث حالة القراءة');
        } else {
            wp_send_json_error('فشل في تحديث حالة القراءة');
        }
    }
    
    /**
     * AJAX: Get unread count
     */
    public function ajax_get_unread_count() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $count = $this->get_unread_count($user_id);
        
        wp_send_json_success(array('count' => $count));
    }
    
    /**
     * AJAX: Archive message
     */
    public function ajax_archive_message() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');
        
        $message_id = intval($_POST['message_id']);
        $user_id = get_current_user_id();
        
        $result = $this->archive_message($message_id, $user_id);
        
        if ($result) {
            wp_send_json_success('تم أرشفة الرسالة');
        } else {
            wp_send_json_error('فشل في أرشفة الرسالة');
        }
    }
    
    /**
     * AJAX: Delete message
     */
    public function ajax_delete_message() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');
        
        $message_id = intval($_POST['message_id']);
        $user_id = get_current_user_id();
        
        $result = $this->delete_message($message_id, $user_id);
        
        if ($result) {
            wp_send_json_success('تم حذف الرسالة');
        } else {
            wp_send_json_error('فشل في حذف الرسالة');
        }
    }
}