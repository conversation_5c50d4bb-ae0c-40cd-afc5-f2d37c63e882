<?php
/**
 * Order assignments view
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('توزيع الطلبات', 'hozi-akadly'); ?></h1>

    <!-- Bulk Assignment Form -->
    <?php if (!empty($unassigned_orders) && !empty($agents)) : ?>
        <div class="hozi-bulk-assignment">
            <h2><?php _e('تخصيص الطلبات غير المخصصة', 'hozi-akadly'); ?></h2>
            <form method="post" id="bulk-assignment-form">
                <?php wp_nonce_field('hozi_bulk_assign'); ?>
                <input type="hidden" name="bulk_assign" value="1">
                
                <div class="hozi-assignment-controls">
                    <div class="hozi-agent-selector">
                        <label for="agent_id"><?php _e('اختر الوكيل:', 'hozi-akadly'); ?></label>
                        <select name="agent_id" id="agent_id" required>
                            <option value=""><?php _e('اختر وكيل', 'hozi-akadly'); ?></option>
                            <?php foreach ($agents as $agent) : ?>
                                <option value="<?php echo esc_attr($agent->id); ?>">
                                    <?php echo esc_html($agent->name); ?>
                                    (<?php echo esc_html($agent->current_orders_count); ?> طلب حالي)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="hozi-bulk-actions">
                        <button type="button" id="select-all-orders" class="button">
                            <?php _e('تحديد الكل', 'hozi-akadly'); ?>
                        </button>
                        <button type="button" id="deselect-all-orders" class="button">
                            <?php _e('إلغاء التحديد', 'hozi-akadly'); ?>
                        </button>
                        <button type="submit" class="button button-primary" disabled id="assign-selected">
                            <?php _e('تخصيص المحدد', 'hozi-akadly'); ?>
                        </button>
                    </div>
                </div>

                <!-- Unassigned Orders Table -->
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <td class="manage-column column-cb check-column">
                                <input type="checkbox" id="cb-select-all">
                            </td>
                            <th scope="col"><?php _e('رقم الطلب', 'hozi-akadly'); ?></th>
                            <th scope="col"><?php _e('تاريخ الطلب', 'hozi-akadly'); ?></th>
                            <th scope="col"><?php _e('العميل', 'hozi-akadly'); ?></th>
                            <th scope="col"><?php _e('المبلغ', 'hozi-akadly'); ?></th>
                            <th scope="col"><?php _e('الحالة', 'hozi-akadly'); ?></th>
                            <th scope="col"><?php _e('الإجراءات', 'hozi-akadly'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($unassigned_orders as $order_data) : 
                            $order = wc_get_order($order_data->order_id);
                            if (!$order) continue;
                        ?>
                            <tr>
                                <th scope="row" class="check-column">
                                    <input type="checkbox" name="order_ids[]" value="<?php echo esc_attr($order_data->order_id); ?>" class="order-checkbox">
                                </th>
                                <td>
                                    <strong>
                                        <a href="<?php echo admin_url('post.php?post=' . $order_data->order_id . '&action=edit'); ?>">
                                            #<?php echo esc_html($order_data->order_id); ?>
                                        </a>
                                    </strong>
                                </td>
                                <td>
                                    <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->order_date))); ?>
                                </td>
                                <td>
                                    <div class="hozi-customer-info">
                                        <strong><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></strong>
                                        <br>
                                        <a href="tel:<?php echo esc_attr($order->get_billing_phone()); ?>">
                                            <?php echo esc_html($order->get_billing_phone()); ?>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <strong><?php echo wc_price($order->get_total()); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $status_name = wc_get_order_status_name($order->get_status());
                                    echo '<span class="hozi-order-status status-' . esc_attr($order->get_status()) . '">' . esc_html($status_name) . '</span>';
                                    ?>
                                </td>
                                <td>
                                    <div class="hozi-quick-assign">
                                        <select class="quick-agent-select" data-order-id="<?php echo esc_attr($order_data->order_id); ?>">
                                            <option value=""><?php _e('تخصيص سريع', 'hozi-akadly'); ?></option>
                                            <?php foreach ($agents as $agent) : ?>
                                                <option value="<?php echo esc_attr($agent->id); ?>">
                                                    <?php echo esc_html($agent->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </form>
        </div>
    <?php elseif (empty($unassigned_orders)) : ?>
        <div class="hozi-no-unassigned">
            <div class="notice notice-success">
                <p><?php _e('ممتاز! جميع الطلبات مخصصة للوكلاء.', 'hozi-akadly'); ?></p>
            </div>
        </div>
    <?php elseif (empty($agents)) : ?>
        <div class="hozi-no-agents">
            <div class="notice notice-warning">
                <p>
                    <?php _e('لا يوجد وكلاء نشطون لتخصيص الطلبات لهم.', 'hozi-akadly'); ?>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-agents'); ?>" class="button button-primary">
                        <?php _e('إضافة وكيل', 'hozi-akadly'); ?>
                    </a>
                </p>
            </div>
        </div>
    <?php endif; ?>

    <!-- Assignment Statistics -->
    <div class="hozi-assignment-stats">
        <h2><?php _e('إحصائيات التوزيع', 'hozi-akadly'); ?></h2>
        <div class="hozi-stats-grid">
            <?php foreach ($agents as $agent) : ?>
                <div class="hozi-agent-stat-card">
                    <h3><?php echo esc_html($agent->name); ?></h3>
                    <div class="hozi-agent-numbers">
                        <div class="hozi-stat-item">
                            <span class="hozi-stat-number"><?php echo esc_html($agent->current_orders_count); ?></span>
                            <span class="hozi-stat-label"><?php _e('طلبات حالية', 'hozi-akadly'); ?></span>
                        </div>
                        <div class="hozi-stat-item">
                            <span class="hozi-stat-number confirmed"><?php echo esc_html($agent->total_confirmed); ?></span>
                            <span class="hozi-stat-label"><?php _e('مؤكد', 'hozi-akadly'); ?></span>
                        </div>
                        <div class="hozi-stat-item">
                            <span class="hozi-stat-number rejected"><?php echo esc_html($agent->total_rejected); ?></span>
                            <span class="hozi-stat-label"><?php _e('مرفوض', 'hozi-akadly'); ?></span>
                        </div>
                    </div>
                    <div class="hozi-agent-status">
                        <?php if ($agent->is_active) : ?>
                            <span class="hozi-status active"><?php _e('نشط', 'hozi-akadly'); ?></span>
                        <?php else : ?>
                            <span class="hozi-status inactive"><?php _e('غير نشط', 'hozi-akadly'); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<style>
.hozi-bulk-assignment {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.hozi-assignment-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.hozi-agent-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.hozi-agent-selector label {
    font-weight: bold;
    white-space: nowrap;
}

.hozi-agent-selector select {
    min-width: 200px;
}

.hozi-bulk-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-customer-info {
    font-size: 13px;
}

.hozi-customer-info a {
    color: #0073aa;
    text-decoration: none;
}

.hozi-customer-info a:hover {
    text-decoration: underline;
}

.hozi-order-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.hozi-order-status.status-pending {
    background: #fff3cd;
    color: #856404;
}

.hozi-order-status.status-processing {
    background: #d1ecf1;
    color: #0c5460;
}

.hozi-order-status.status-on-hold {
    background: #f8d7da;
    color: #721c24;
}

.hozi-quick-assign select {
    font-size: 12px;
    padding: 2px 4px;
}

.hozi-assignment-stats {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.hozi-agent-stat-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.hozi-agent-stat-card h3 {
    margin: 0 0 15px 0;
    color: #333;
}

.hozi-agent-numbers {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
}

.hozi-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.hozi-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.hozi-stat-number.confirmed {
    color: #4CAF50;
}

.hozi-stat-number.rejected {
    color: #f44336;
}

.hozi-stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.hozi-agent-status {
    margin-top: 10px;
}

.hozi-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.hozi-status.active {
    background: #d4edda;
    color: #155724;
}

.hozi-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.hozi-no-unassigned, .hozi-no-agents {
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .hozi-assignment-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .hozi-agent-selector {
        flex-direction: column;
        align-items: stretch;
    }
    
    .hozi-bulk-actions {
        justify-content: center;
    }
    
    .hozi-stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Select/Deselect all functionality
    $('#cb-select-all').on('change', function() {
        $('.order-checkbox').prop('checked', this.checked);
        updateAssignButton();
    });
    
    $('.order-checkbox').on('change', function() {
        updateAssignButton();
        
        // Update select all checkbox
        var totalCheckboxes = $('.order-checkbox').length;
        var checkedCheckboxes = $('.order-checkbox:checked').length;
        $('#cb-select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
    });
    
    // Select/Deselect all buttons
    $('#select-all-orders').on('click', function() {
        $('.order-checkbox').prop('checked', true);
        $('#cb-select-all').prop('checked', true);
        updateAssignButton();
    });
    
    $('#deselect-all-orders').on('click', function() {
        $('.order-checkbox').prop('checked', false);
        $('#cb-select-all').prop('checked', false);
        updateAssignButton();
    });
    
    // Agent selection
    $('#agent_id').on('change', function() {
        updateAssignButton();
    });
    
    // Update assign button state
    function updateAssignButton() {
        var hasSelectedOrders = $('.order-checkbox:checked').length > 0;
        var hasSelectedAgent = $('#agent_id').val() !== '';
        $('#assign-selected').prop('disabled', !(hasSelectedOrders && hasSelectedAgent));
    }
    
    // Quick assignment
    $('.quick-agent-select').on('change', function() {
        var orderId = $(this).data('order-id');
        var agentId = $(this).val();
        
        if (agentId && orderId) {
            if (confirm('هل تريد تخصيص هذا الطلب للوكيل المحدد؟')) {
                // Send AJAX request for quick assignment
                $.post(ajaxurl, {
                    action: 'hozi_assign_order',
                    order_id: orderId,
                    agent_id: agentId,
                    nonce: '<?php echo wp_create_nonce('hozi_akadly_nonce'); ?>'
                }, function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('فشل في تخصيص الطلب: ' + response.data.message);
                    }
                });
            } else {
                $(this).val('');
            }
        }
    });
    
    // Form submission confirmation
    $('#bulk-assignment-form').on('submit', function(e) {
        var selectedCount = $('.order-checkbox:checked').length;
        var agentName = $('#agent_id option:selected').text();
        
        if (!confirm('هل تريد تخصيص ' + selectedCount + ' طلب للوكيل "' + agentName + '"؟')) {
            e.preventDefault();
        }
    });
});
</script>
