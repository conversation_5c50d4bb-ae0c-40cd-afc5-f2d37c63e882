<?php
/**
 * Akadly Tracking Fix
 * 
 * This file fixes the issue where orders aren't being transferred to the tracking system after confirmation.
 * Place this file in your WordPress root directory and access via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🛠️ إصلاح نظام متابعة التوصيل - Akadly</h1>";

// Check if we're applying the fix
$applying_fix = isset($_GET['apply_fix']) && $_GET['apply_fix'] == '1';
$force_create_tracking_table = isset($_GET['force_create_table']) && $_GET['force_create_table'] == '1';

if ($applying_fix) {
    echo "<h2>⚡ جاري تطبيق الإصلاح...</h2>";
    
    try {
        // Step 1: Ensure tracking table exists
        global $wpdb;
        $table_name = $wpdb->prefix . 'hozi_order_tracking';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        
        if (!$table_exists || $force_create_tracking_table) {
            echo "<p>🔧 جدول التتبع غير موجود. جاري إنشاء الجدول...</p>";
            
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                order_id bigint(20) NOT NULL,
                agent_id int(11) NOT NULL,
                status varchar(50) NOT NULL,
                previous_status varchar(50) DEFAULT NULL,
                reason_category varchar(100) DEFAULT NULL,
                reason_details text DEFAULT NULL,
                notes text DEFAULT NULL,
                updated_by int(11) NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY order_id (order_id),
                KEY agent_id (agent_id),
                KEY status (status),
                KEY updated_at (updated_at)
            ) $charset_collate;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
            
            // Verify table was created
            $table_exists_after = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
            if ($table_exists_after) {
                echo "<p style='color: green;'>✅ تم إنشاء جدول التتبع بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء جدول التتبع</p>";
                echo "<pre>" . print_r($wpdb->last_error, true) . "</pre>";
            }
        } else {
            echo "<p>✅ جدول التتبع موجود بالفعل</p>";
        }
        
        // Step 2: Apply the patch to fix auto_transfer_to_tracking function
        echo "<p>🔧 جاري تطبيق التصحيح لدالة النقل التلقائي...</p>";
        
        // Create a patched version of the method
        function create_patched_auto_transfer() {
            return '
    /**
     * Automatically transfer confirmed order to tracking system - PATCHED VERSION
     */
    public function auto_transfer_to_tracking($order_id, $agent_id) {
        global $wpdb;

        try {
            // Debug logging
            error_log("Hozi Akadly: Starting auto_transfer_to_tracking for order {$order_id}, agent {$agent_id}");

            // Ensure tracking table exists
            $this->ensure_tracking_table_exists();

            // Check if order is already in tracking
            $existing_tracking = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $order_id
            ));

            if ($existing_tracking) {
                error_log("Hozi Akadly: Order {$order_id} already in tracking, skipping");
                return; // Already in tracking
            }

            error_log("Hozi Akadly: Order {$order_id} not in tracking, proceeding with insert");

            // FIXED VERSION - Directly insert the tracking record
            $result = $wpdb->insert(
                $wpdb->prefix . "hozi_order_tracking",
                array(
                    "order_id" => $order_id,
                    "agent_id" => $agent_id,
                    "status" => "out_for_delivery",
                    "previous_status" => null,
                    "reason_category" => "auto_confirmed",
                    "reason_details" => "",
                    "notes" => "تم النقل تلقائياً إلى متابعة التوصيل فور تأكيد الطلب",
                    "updated_by" => get_current_user_id() ?: 1,
                    "updated_at" => current_time("mysql"),
                    "created_at" => current_time("mysql")
                ),
                array("%d", "%d", "%s", "%s", "%s", "%s", "%s", "%d", "%s", "%s")
            );

            if ($result) {
                error_log("Hozi Akadly: ✅ Successfully inserted order {$order_id} into tracking table with ID: " . $wpdb->insert_id);

                // Add order note
                $order = wc_get_order($order_id);
                if ($order) {
                    // Get agent name for note
                    $agent_info = $wpdb->get_row($wpdb->prepare(
                        "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                        $agent_id
                    ));

                    $order->add_order_note(
                        sprintf(
                            "🚀 تم نقل الطلب تلقائياً إلى نظام متابعة التوصيل%s%s",
                            "\n👤 الوكيل: " . ($agent_info->name ?? "غير محدد"),
                            "\n📦 متاح الآن في: متابعة التوصيل"
                        ),
                        0 // Private note
                    );

                    error_log("Hozi Akadly: ✅ Added order note for order {$order_id}");
                } else {
                    error_log("Hozi Akadly: ❌ Failed to get WooCommerce order {$order_id}");
                }

                // Log the auto transfer
                if (class_exists("Hozi_Akadly_Database")) {
                    Hozi_Akadly_Database::log_action(
                        $order_id,
                        $agent_id,
                        "auto_tracking_transfer",
                        "confirmed",
                        "out_for_delivery",
                        "تم النقل تلقائياً إلى متابعة التوصيل فور التأكيد"
                    );
                    error_log("Hozi Akadly: ✅ Logged auto transfer action for order {$order_id}");
                }

                // Clear cache
                if (function_exists("wp_cache_delete")) {
                    wp_cache_delete("hozi_agent_confirmed_orders_" . $agent_id, "hozi_akadly");
                    wp_cache_delete("hozi_agent_tracking_stats_" . $agent_id, "hozi_akadly");
                    error_log("Hozi Akadly: ✅ Cleared cache for agent {$agent_id}");
                }

                error_log("Hozi Akadly: ✅ Auto transfer completed successfully for order {$order_id}");
                return true;
            } else {
                error_log("Hozi Akadly: ❌ Failed to insert order {$order_id} into tracking table. Error: " . $wpdb->last_error);
                error_log("Hozi Akadly: ❌ SQL Query: " . $wpdb->last_query);
                return false;
            }
        } catch (Exception $e) {
            // Log error but don\'t break the confirmation process
            error_log("Hozi Akadly Auto Transfer Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Ensure tracking table exists
     */
    private function ensure_tracking_table_exists() {
        global $wpdb;

        $table_name = $wpdb->prefix . "hozi_order_tracking";

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE \'$table_name\'") == $table_name;

        if (!$table_exists) {
            error_log("Hozi Akadly: Tracking table missing, creating it now...");

            // Create the table
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                order_id bigint(20) NOT NULL,
                agent_id int(11) NOT NULL,
                status varchar(50) NOT NULL,
                previous_status varchar(50) DEFAULT NULL,
                reason_category varchar(100) DEFAULT NULL,
                reason_details text DEFAULT NULL,
                notes text DEFAULT NULL,
                updated_by int(11) NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY order_id (order_id),
                KEY agent_id (agent_id),
                KEY status (status),
                KEY updated_at (updated_at)
            ) $charset_collate;";

            require_once(ABSPATH . "wp-admin/includes/upgrade.php");
            dbDelta($sql);

            // Verify table was created
            $table_exists_after = $wpdb->get_var("SHOW TABLES LIKE \'$table_name\'") == $table_name;
            if ($table_exists_after) {
                error_log("Hozi Akadly: Tracking table created successfully");
            } else {
                error_log("Hozi Akadly: Failed to create tracking table");
            }
        }
    }
            ';
        }
        
        // Get the distributor class file path
        $distributor_file = WP_PLUGIN_DIR . '/hozi-akadly/includes/class-order-distributor.php';
        
        if (file_exists($distributor_file)) {
            // Create a backup first
            $backup_file = WP_PLUGIN_DIR . '/hozi-akadly/includes/class-order-distributor.php.bak';
            if (!file_exists($backup_file)) {
                copy($distributor_file, $backup_file);
                echo "<p>✅ تم إنشاء نسخة احتياطية من ملف class-order-distributor.php</p>";
            }
            
            // Read the current file content
            $file_content = file_get_contents($distributor_file);
            
            if ($file_content) {
                // Replace the method with our patched version
                $patched_method = create_patched_auto_transfer();
                
                // Look for the method and replace it
                $pattern = '/private\s+function\s+auto_transfer_to_tracking\s*\(.*?\)\s*{.*?}/s';
                if (preg_match($pattern, $file_content)) {
                    $file_content = preg_replace($pattern, 'private function auto_transfer_to_tracking($order_id, $agent_id) {', $file_content);
                    
                    // Add our implementation after the method signature
                    $pos = strpos($file_content, 'private function auto_transfer_to_tracking($order_id, $agent_id) {');
                    if ($pos !== false) {
                        $pos += strlen('private function auto_transfer_to_tracking($order_id, $agent_id) {');
                        $start = substr($file_content, 0, $pos);
                        $end = substr($file_content, $pos);
                        
                        // Find the closing brace of the method
                        $brace_count = 1;
                        $char_pos = 0;
                        $method_end = 0;
                        
                        foreach (str_split($end) as $char) {
                            if ($char === '{') $brace_count++;
                            if ($char === '}') $brace_count--;
                            
                            if ($brace_count === 0) {
                                $method_end = $char_pos;
                                break;
                            }
                            
                            $char_pos++;
                        }
                        
                        if ($method_end > 0) {
                            $end = substr($end, $method_end);
                            
                            // Generate the new implementation
                            $implementation = '
        global $wpdb;

        try {
            // Debug logging
            error_log("Hozi Akadly: Starting auto_transfer_to_tracking for order {$order_id}, agent {$agent_id}");

            // Ensure tracking table exists
            $this->ensure_tracking_table_exists();

            // Check if order is already in tracking
            $existing_tracking = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $order_id
            ));

            if ($existing_tracking) {
                error_log("Hozi Akadly: Order {$order_id} already in tracking, skipping");
                return; // Already in tracking
            }

            error_log("Hozi Akadly: Order {$order_id} not in tracking, proceeding with insert");

            // FIXED VERSION - Directly insert the tracking record
            $result = $wpdb->insert(
                $wpdb->prefix . "hozi_order_tracking",
                array(
                    "order_id" => $order_id,
                    "agent_id" => $agent_id,
                    "status" => "out_for_delivery",
                    "previous_status" => null,
                    "reason_category" => "auto_confirmed",
                    "reason_details" => "",
                    "notes" => "تم النقل تلقائياً إلى متابعة التوصيل فور تأكيد الطلب",
                    "updated_by" => get_current_user_id() ?: 1,
                    "updated_at" => current_time("mysql"),
                    "created_at" => current_time("mysql")
                ),
                array("%d", "%d", "%s", "%s", "%s", "%s", "%s", "%d", "%s", "%s")
            );

            if ($result) {
                error_log("Hozi Akadly: ✅ Successfully inserted order {$order_id} into tracking table with ID: " . $wpdb->insert_id);

                // Add order note
                $order = wc_get_order($order_id);
                if ($order) {
                    // Get agent name for note
                    $agent_info = $wpdb->get_row($wpdb->prepare(
                        "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                        $agent_id
                    ));

                    $order->add_order_note(
                        sprintf(
                            "🚀 تم نقل الطلب تلقائياً إلى نظام متابعة التوصيل%s%s",
                            "\n👤 الوكيل: " . ($agent_info->name ?? "غير محدد"),
                            "\n📦 متاح الآن في: متابعة التوصيل"
                        ),
                        0 // Private note
                    );

                    error_log("Hozi Akadly: ✅ Added order note for order {$order_id}");
                } else {
                    error_log("Hozi Akadly: ❌ Failed to get WooCommerce order {$order_id}");
                }

                // Log the auto transfer
                if (class_exists("Hozi_Akadly_Database")) {
                    Hozi_Akadly_Database::log_action(
                        $order_id,
                        $agent_id,
                        "auto_tracking_transfer",
                        "confirmed",
                        "out_for_delivery",
                        "تم النقل تلقائياً إلى متابعة التوصيل فور التأكيد"
                    );
                    error_log("Hozi Akadly: ✅ Logged auto transfer action for order {$order_id}");
                }

                // Clear cache
                if (function_exists("wp_cache_delete")) {
                    wp_cache_delete("hozi_agent_confirmed_orders_" . $agent_id, "hozi_akadly");
                    wp_cache_delete("hozi_agent_tracking_stats_" . $agent_id, "hozi_akadly");
                    error_log("Hozi Akadly: ✅ Cleared cache for agent {$agent_id}");
                }

                error_log("Hozi Akadly: ✅ Auto transfer completed successfully for order {$order_id}");
                return true;
            } else {
                error_log("Hozi Akadly: ❌ Failed to insert order {$order_id} into tracking table. Error: " . $wpdb->last_error);
                error_log("Hozi Akadly: ❌ SQL Query: " . $wpdb->last_query);
                return false;
            }
        } catch (Exception $e) {
            // Log error but don\'t break the confirmation process
            error_log("Hozi Akadly Auto Transfer Error: " . $e->getMessage());
            return false;
        }';
                            
                            $file_content = $start . $implementation . $end;
                            
                            // Add the ensure_tracking_table_exists method if it doesn't exist
                            if (strpos($file_content, 'function ensure_tracking_table_exists') === false) {
                                // Find the last closing brace of the class
                                $last_brace = strrpos($file_content, '}');
                                if ($last_brace !== false) {
                                    $before = substr($file_content, 0, $last_brace);
                                    $after = substr($file_content, $last_brace);
                                    
                                    $table_method = '
    /**
     * Ensure tracking table exists
     */
    private function ensure_tracking_table_exists() {
        global $wpdb;

        $table_name = $wpdb->prefix . "hozi_order_tracking";

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE \'$table_name\'") == $table_name;

        if (!$table_exists) {
            error_log("Hozi Akadly: Tracking table missing, creating it now...");

            // Create the table
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                order_id bigint(20) NOT NULL,
                agent_id int(11) NOT NULL,
                status varchar(50) NOT NULL,
                previous_status varchar(50) DEFAULT NULL,
                reason_category varchar(100) DEFAULT NULL,
                reason_details text DEFAULT NULL,
                notes text DEFAULT NULL,
                updated_by int(11) NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY order_id (order_id),
                KEY agent_id (agent_id),
                KEY status (status),
                KEY updated_at (updated_at)
            ) $charset_collate;";

            require_once(ABSPATH . "wp-admin/includes/upgrade.php");
            dbDelta($sql);

            // Verify table was created
            $table_exists_after = $wpdb->get_var("SHOW TABLES LIKE \'$table_name\'") == $table_name;
            if ($table_exists_after) {
                error_log("Hozi Akadly: Tracking table created successfully");
            } else {
                error_log("Hozi Akadly: Failed to create tracking table");
            }
        }
    }
';
                                    $file_content = $before . $table_method . $after;
                                }
                            }
                            
                            // Write the modified content back to the file
                            file_put_contents($distributor_file, $file_content);
                            echo "<p style='color: green;'>✅ تم تطبيق التصحيح بنجاح على دالة النقل التلقائي</p>";
                        } else {
                            echo "<p style='color: red;'>❌ لم نتمكن من تحديد نهاية الدالة</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ لم نتمكن من تحديد موقع الدالة بعد التعديل الأولي</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ لم نتمكن من العثور على دالة auto_transfer_to_tracking في الملف</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ لم نتمكن من قراءة محتوى الملف</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ لم نتمكن من العثور على ملف class-order-distributor.php</p>";
            echo "<p>مسار الملف المتوقع: " . $distributor_file . "</p>";
        }
        
        // Step 3: Test the fix with a confirmed order
        echo "<h3>🧪 اختبار الإصلاح</h3>";
        echo "<p>لاختبار الإصلاح، استخدم ملف التشخيص:</p>";
        echo "<p><a href='debug-auto-tracking-transfer.php' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>فتح ملف التشخيص</a></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ حدث خطأ أثناء تطبيق الإصلاح: " . $e->getMessage() . "</p>";
    }
} else {
    // Display information about the fix
    echo "<h2>🛠️ وصف الإصلاح</h2>";
    echo "<p>هذا الإصلاح يعالج مشكلة عدم انتقال الطلبات إلى نظام متابعة التوصيل بعد تأكيدها. المشكلة تكمن في دالة <code>auto_transfer_to_tracking</code> في ملف <code>class-order-distributor.php</code>.</p>";
    
    echo "<h2>👷‍♂️ ما سيقوم به الإصلاح:</h2>";
    echo "<ol>";
    echo "<li>التأكد من وجود جدول <code>hozi_order_tracking</code> وإنشائه إذا لم يكن موجوداً</li>";
    echo "<li>تعديل دالة <code>auto_transfer_to_tracking</code> لتعمل بشكل صحيح</li>";
    echo "<li>إضافة دالة <code>ensure_tracking_table_exists</code> إذا لم تكن موجودة</li>";
    echo "<li>تحسين سجلات الأخطاء لتسهيل تشخيص المشكلات المستقبلية</li>";
    echo "</ol>";
    
    echo "<h2>🛡️ احتياطات الأمان:</h2>";
    echo "<ol>";
    echo "<li>سيتم إنشاء نسخة احتياطية من الملف الأصلي قبل تطبيق أي تغييرات</li>";
    echo "<li>يمكنك استعادة النسخة الاحتياطية في أي وقت</li>";
    echo "</ol>";
    
    echo "<h2>👨‍💻 خيارات:</h2>";
    echo "<p><a href='?apply_fix=1' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>تطبيق الإصلاح</a>";
    echo "<a href='?apply_fix=1&force_create_table=1' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>تطبيق الإصلاح مع إعادة إنشاء الجدول</a></p>";
}

echo "<hr>";
echo "<p>تم إنشاء هذا الإصلاح بواسطة فريق Akadly - " . date('Y-m-d H:i:s') . "</p>";
?>
