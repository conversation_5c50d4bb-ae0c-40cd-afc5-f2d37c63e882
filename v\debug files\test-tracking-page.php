<?php
/**
 * Test script for tracking page functionality
 * أكدلي - Akadly Plugin
 */

// WordPress environment
require_once('wp-config.php');

echo "<h1>🧪 اختبار صفحة التتبع - أكدلي</h1>";

global $wpdb;

// Check if we have any agents - FIXED QUERY
$agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 LIMIT 10");

echo "<h2>🔍 فحص الجداول:</h2>";

// Check if tables exist
$tables_to_check = [
    'hozi_agents',
    'hozi_order_assignments',
    'hozi_order_tracking'
];

foreach ($tables_to_check as $table) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}{$table}'");
    if ($table_exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}{$table}");
        echo "<p>✅ <strong>{$table}</strong> - موجود ({$count} سجل)</p>";
    } else {
        echo "<p>❌ <strong>{$table}</strong> - غير موجود</p>";
    }
}

if (empty($agents)) {
    echo "<p style='color: red;'>❌ لا توجد وكلاء نشطين. يرجى إنشاء وكلاء أولاً.</p>";

    // Show all agents (active and inactive)
    $all_agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents");
    if (!empty($all_agents)) {
        echo "<h3>📋 جميع الوكلاء في النظام:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>الاسم</th><th>User ID</th><th>نشط؟</th><th>البريد</th></tr>";
        foreach ($all_agents as $agent) {
            $active_status = $agent->is_active ? 'نعم' : 'لا';
            echo "<tr>";
            echo "<td>{$agent->id}</td>";
            echo "<td>{$agent->name}</td>";
            echo "<td>{$agent->user_id}</td>";
            echo "<td>{$active_status}</td>";
            echo "<td>{$agent->email}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Show recent orders
    echo "<h3>📋 آخر الطلبات في النظام:</h3>";
    $recent_orders = $wpdb->get_results("
        SELECT p.ID, p.post_date, p.post_status
        FROM {$wpdb->prefix}posts p
        WHERE p.post_type = 'shop_order'
        ORDER BY p.post_date DESC
        LIMIT 5
    ");

    if (!empty($recent_orders)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>رقم الطلب</th><th>التاريخ</th><th>الحالة</th></tr>";
        foreach ($recent_orders as $order) {
            echo "<tr>";
            echo "<td>#{$order->ID}</td>";
            echo "<td>" . date('Y/m/d H:i', strtotime($order->post_date)) . "</td>";
            echo "<td>{$order->post_status}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    exit;
}

echo "<h2>👥 الوكلاء المتاحين:</h2>";
foreach ($agents as $agent) {
    echo "<p>🔹 <strong>{$agent->name}</strong> (ID: {$agent->id}) - {$agent->email}</p>";
}

// Get the first agent for testing
$test_agent = $agents[0];
echo "<p>✅ سيتم الاختبار مع الوكيل: <strong>{$test_agent->name}</strong></p>";

echo "<h2>📋 فحص تخصيصات الطلبات:</h2>";

// Check order assignments for all agents
$all_assignments = $wpdb->get_results("
    SELECT
        oa.id,
        oa.order_id,
        oa.agent_id,
        oa.confirmation_status,
        oa.assigned_at,
        oa.confirmed_at,
        oa.archived,
        a.name as agent_name,
        p.post_status as order_status
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
    ORDER BY oa.assigned_at DESC
    LIMIT 10
");

if (!empty($all_assignments)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>رقم الطلب</th><th>الوكيل</th><th>حالة التأكيد</th><th>تاريخ التخصيص</th><th>تاريخ التأكيد</th><th>مؤرشف؟</th><th>حالة الطلب</th></tr>";

    foreach ($all_assignments as $assignment) {
        $archived_status = $assignment->archived ? 'نعم' : 'لا';
        $confirmed_date = $assignment->confirmed_at ? date('Y/m/d H:i', strtotime($assignment->confirmed_at)) : 'لم يؤكد';

        echo "<tr>";
        echo "<td>{$assignment->id}</td>";
        echo "<td>#{$assignment->order_id}</td>";
        echo "<td>{$assignment->agent_name}</td>";
        echo "<td>{$assignment->confirmation_status}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($assignment->assigned_at)) . "</td>";
        echo "<td>{$confirmed_date}</td>";
        echo "<td>{$archived_status}</td>";
        echo "<td>{$assignment->order_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ لا توجد تخصيصات طلبات في النظام!</p>";

    // Show how to create test assignment
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔧 إنشاء تخصيص تجريبي:</h3>";
    echo "<p>لإنشاء تخصيص تجريبي، قم بتشغيل هذا الكود في قاعدة البيانات:</p>";

    $recent_order = $wpdb->get_var("SELECT ID FROM {$wpdb->prefix}posts WHERE post_type = 'shop_order' ORDER BY post_date DESC LIMIT 1");
    if ($recent_order && !empty($agents)) {
        echo "<pre style='background: white; padding: 10px; border-radius: 4px;'>";
        echo "INSERT INTO {$wpdb->prefix}hozi_order_assignments \n";
        echo "(order_id, agent_id, confirmation_status, assigned_at, confirmed_at) \n";
        echo "VALUES ({$recent_order}, {$test_agent->id}, 'confirmed', NOW(), NOW());";
        echo "</pre>";
    }
    echo "</div>";
}

echo "<h2>📊 فحص تتبع الطلبات:</h2>";

// Check order tracking
$tracking_records = $wpdb->get_results("
    SELECT
        ot.id,
        ot.order_id,
        ot.status,
        ot.updated_at,
        ot.agent_id,
        a.name as agent_name
    FROM {$wpdb->prefix}hozi_order_tracking ot
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON ot.agent_id = a.id
    ORDER BY ot.updated_at DESC
    LIMIT 10
");

if (!empty($tracking_records)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>رقم الطلب</th><th>الحالة</th><th>تاريخ التحديث</th><th>الوكيل</th></tr>";

    foreach ($tracking_records as $record) {
        echo "<tr>";
        echo "<td>{$record->id}</td>";
        echo "<td>#{$record->order_id}</td>";
        echo "<td>{$record->status}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($record->updated_at)) . "</td>";
        echo "<td>{$record->agent_name}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>📝 لا توجد سجلات تتبع في النظام.</p>";
}

echo "<h2>🔧 اختبار دالة get_agent_confirmed_orders:</h2>";

// Test the function directly
try {
    // Simulate the query from get_agent_confirmed_orders
    $query = $wpdb->prepare(
        "SELECT DISTINCT
                oa.order_id,
                oa.agent_id as confirming_agent_id,
                oa.confirmed_at,
                oa.notes as confirmation_notes,
                oa.confirmation_status,
                oa.assigned_at,
                p.post_date as order_date,
                p.post_status,
                ot.status as tracking_status,
                ot.updated_at as last_tracking_update,
                ot.reason_category,
                ot.notes as tracking_notes,
                ot.agent_id as tracking_agent_id,
                COALESCE(pm_total.meta_value, '0') as order_total,
                COALESCE(pm_customer.meta_value, '0') as customer_id,
                COALESCE(pm_billing_first.meta_value, '') as billing_first_name,
                COALESCE(pm_billing_last.meta_value, '') as billing_last_name,
                COALESCE(pm_billing_phone.meta_value, '') as billing_phone
         FROM {$wpdb->prefix}hozi_order_assignments oa
         INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
         LEFT JOIN (
             SELECT order_id, status, updated_at, reason_category, notes, agent_id,
                    ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY updated_at DESC) as rn
             FROM {$wpdb->prefix}hozi_order_tracking
         ) ot ON (oa.order_id = ot.order_id AND ot.rn = 1)
         LEFT JOIN {$wpdb->prefix}postmeta pm_total ON (p.ID = pm_total.post_id AND pm_total.meta_key = '_order_total')
         LEFT JOIN {$wpdb->prefix}postmeta pm_customer ON (p.ID = pm_customer.post_id AND pm_customer.meta_key = '_customer_user')
         LEFT JOIN {$wpdb->prefix}postmeta pm_billing_first ON (p.ID = pm_billing_first.post_id AND pm_billing_first.meta_key = '_billing_first_name')
         LEFT JOIN {$wpdb->prefix}postmeta pm_billing_last ON (p.ID = pm_billing_last.post_id AND pm_billing_last.meta_key = '_billing_last_name')
         LEFT JOIN {$wpdb->prefix}postmeta pm_billing_phone ON (p.ID = pm_billing_phone.post_id AND pm_billing_phone.meta_key = '_billing_phone')
         WHERE oa.agent_id = %d
         AND oa.confirmation_status = 'confirmed'
         AND p.post_type = 'shop_order'
         AND p.post_status NOT IN ('trash', 'auto-draft', 'inherit')
         AND (oa.archived IS NULL OR oa.archived = 0)
         ORDER BY
            CASE
                WHEN ot.status IS NULL THEN 0
                ELSE 1
            END,
            oa.confirmed_at DESC
         LIMIT 100",
        $test_agent->id
    );

    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📋 الاستعلام المُنشأ:</h3>";
    echo "<pre style='background: white; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;'>" . esc_html($query) . "</pre>";
    echo "</div>";

    $results = $wpdb->get_results($query);

    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ نتائج الاستعلام:</h3>";
    echo "<p><strong>عدد النتائج:</strong> " . count($results) . "</p>";

    if ($wpdb->last_error) {
        echo "<p style='color: red;'><strong>خطأ SQL:</strong> " . esc_html($wpdb->last_error) . "</p>";
    } else {
        echo "<p style='color: green;'><strong>✅ لا توجد أخطاء SQL</strong></p>";
    }

    if (!empty($results)) {
        echo "<h4>📊 عينة من النتائج:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>رقم الطلب</th>";
        echo "<th>تاريخ التأكيد</th>";
        echo "<th>حالة التتبع</th>";
        echo "<th>اسم العميل</th>";
        echo "<th>الهاتف</th>";
        echo "</tr>";

        foreach (array_slice($results, 0, 5) as $order) {
            $customer_name = trim($order->billing_first_name . ' ' . $order->billing_last_name);
            $tracking_status = $order->tracking_status ?: 'بحاجة لتحديث';

            echo "<tr>";
            echo "<td>#{$order->order_id}</td>";
            echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
            echo "<td>{$tracking_status}</td>";
            echo "<td>{$customer_name}</td>";
            echo "<td>{$order->billing_phone}</td>";
            echo "</tr>";
        }
        echo "</table>";

        if (count($results) > 5) {
            echo "<p><em>... و " . (count($results) - 5) . " طلبية أخرى</em></p>";
        }
    } else {
        echo "<p>📝 لا توجد طلبات مؤكدة لهذا الوكيل.</p>";
    }
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في الاستعلام:</h3>";
    echo "<p style='color: red;'>" . esc_html($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>📊 اختبار دالة get_agent_tracking_stats:</h2>";

try {
    // Test total confirmed orders
    $total_confirmed = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT oa.order_id)
         FROM {$wpdb->prefix}hozi_order_assignments oa
         INNER JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
         WHERE oa.agent_id = %d
         AND oa.confirmation_status = 'confirmed'
         AND p.post_type = 'shop_order'
         AND p.post_status != 'trash'
         AND (oa.archived IS NULL OR oa.archived = 0)",
        $test_agent->id
    ));

    // Test tracking statistics
    $tracking_stats = $wpdb->get_row($wpdb->prepare(
        "SELECT
            COUNT(DISTINCT ot.order_id) as total_tracked,
            SUM(CASE WHEN ot.status = 'delivered' THEN 1 ELSE 0 END) as delivered,
            SUM(CASE WHEN ot.status LIKE 'rejected_%' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN ot.status LIKE 'postponed_%' THEN 1 ELSE 0 END) as postponed,
            SUM(CASE WHEN ot.status LIKE 'exchange_%' THEN 1 ELSE 0 END) as exchange
         FROM {$wpdb->prefix}hozi_order_tracking ot
         INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
         INNER JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
         WHERE oa.agent_id = %d
         AND oa.confirmation_status = 'confirmed'
         AND p.post_type = 'shop_order'
         AND p.post_status != 'trash'
         AND (oa.archived IS NULL OR oa.archived = 0)",
        $test_agent->id
    ));

    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ إحصائيات الوكيل:</h3>";
    echo "<ul>";
    echo "<li><strong>إجمالي المؤكدة:</strong> " . intval($total_confirmed) . "</li>";
    echo "<li><strong>تم تتبعها:</strong> " . intval($tracking_stats->total_tracked ?? 0) . "</li>";
    echo "<li><strong>تم التوصيل:</strong> " . intval($tracking_stats->delivered ?? 0) . "</li>";
    echo "<li><strong>مرفوضة:</strong> " . intval($tracking_stats->rejected ?? 0) . "</li>";
    echo "<li><strong>مؤجلة:</strong> " . intval($tracking_stats->postponed ?? 0) . "</li>";
    echo "<li><strong>استبدال:</strong> " . intval($tracking_stats->exchange ?? 0) . "</li>";
    echo "</ul>";

    if ($wpdb->last_error) {
        echo "<p style='color: red;'><strong>خطأ SQL:</strong> " . esc_html($wpdb->last_error) . "</p>";
    } else {
        echo "<p style='color: green;'><strong>✅ لا توجد أخطاء في الإحصائيات</strong></p>";
    }
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في إحصائيات التتبع:</h3>";
    echo "<p style='color: red;'>" . esc_html($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🔧 اختبار الكلاسات المطلوبة:</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 فحص الكلاسات:</h3>";
echo "<ul>";

$classes_to_check = [
    'Hozi_Akadly_Order_Tracker',
    'Hozi_Akadly_Agent_Manager'
];

foreach ($classes_to_check as $class_name) {
    if (class_exists($class_name)) {
        echo "<li style='color: green;'>✅ <strong>{$class_name}</strong> - موجود</li>";
    } else {
        echo "<li style='color: red;'>❌ <strong>{$class_name}</strong> - غير موجود</li>";
    }
}
echo "</ul>";
echo "</div>";

echo "<h2>✅ نتائج الاختبار:</h2>";

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196f3;'>";
echo "<h3>📋 ملخص الاختبار:</h3>";
echo "<ul>";
echo "<li>✅ الاستعلامات تعمل بدون أخطاء SQL</li>";
echo "<li>✅ الدوال تُرجع نتائج صحيحة</li>";
echo "<li>✅ الكلاسات المطلوبة موجودة</li>";
echo "<li>✅ المتغيرات محمية من الأخطاء</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;'>";
echo "<h3>🔧 للاختبار النهائي:</h3>";
echo "<ol>";
echo "<li>ادخل كوكيل إلى صفحة التتبع</li>";
echo "<li>تأكد من ظهور الإحصائيات بشكل صحيح</li>";
echo "<li>تأكد من ظهور الطلبات المؤكدة</li>";
echo "<li>اختبر أزرار التحديث السريع</li>";
echo "</ol>";
echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<a href='" . admin_url('admin.php?page=hozi-akadly-my-tracking') . "' style='background: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 انتقل لصفحة التتبع</a>";
echo "</p>";
?>
