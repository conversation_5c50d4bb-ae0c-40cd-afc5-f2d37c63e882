<?php
/**
 * Unarchive confirmed orders for testing
 */

require_once('wp-config.php');

global $wpdb;

echo "<h1>🔄 إلغاء أرشفة الطلبات المؤكدة</h1>";

// Get current agent
$user_id = get_current_user_id();
$agent_data = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d AND is_active = 1",
    $user_id
));

if (!$agent_data) {
    echo "<p>❌ لم يتم العثور على بيانات الوكيل للمستخدم الحالي</p>";
    exit;
}

echo "<p>✅ <strong>الوكيل:</strong> {$agent_data->name} (ID: {$agent_data->id})</p>";

// Check archived confirmed orders
$archived_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmed_at, oa.archived, oa.archived_at
     FROM {$wpdb->prefix}hozi_order_assignments oa
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND oa.archived = 1
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $agent_data->id
));

echo "<h2>📋 الطلبات المؤكدة المؤرشفة:</h2>";
echo "<p><strong>عدد الطلبات المؤرشفة:</strong> " . count($archived_orders) . "</p>";

if ($archived_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>رقم الطلب</th>";
    echo "<th>تاريخ التأكيد</th>";
    echo "<th>تاريخ الأرشفة</th>";
    echo "</tr>";
    
    foreach ($archived_orders as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>" . ($order->archived_at ? date('Y/m/d H:i', strtotime($order->archived_at)) : '-') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Unarchive button
    if (isset($_POST['unarchive_all'])) {
        $result = $wpdb->update(
            $wpdb->prefix . 'hozi_order_assignments',
            array('archived' => 0, 'archived_at' => null),
            array('agent_id' => $agent_data->id, 'confirmation_status' => 'confirmed', 'archived' => 1),
            array('%d', '%s'),
            array('%d', '%s', '%d')
        );
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>";
        echo "<h3>✅ تم إلغاء الأرشفة!</h3>";
        echo "<p>تم إلغاء أرشفة {$result} طلب مؤكد.</p>";
        echo "<p><a href='http://localhost/akadly/wp-admin/admin.php?page=hozi-akadly-delivery-tracking'>انتقل إلى صفحة التتبع</a></p>";
        echo "</div>";
    } else {
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<button type='submit' name='unarchive_all' class='button button-primary' style='background: #28a745; border-color: #28a745; padding: 10px 20px;'>";
        echo "إلغاء أرشفة جميع الطلبات المؤكدة";
        echo "</button>";
        echo "</form>";
    }
} else {
    echo "<p>✅ لا توجد طلبات مؤكدة مؤرشفة.</p>";
}

// Check non-archived confirmed orders
$non_archived = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmed_at, p.post_status
     FROM {$wpdb->prefix}hozi_order_assignments oa
     INNER JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND (oa.archived IS NULL OR oa.archived = 0)
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $agent_data->id
));

echo "<h2>📋 الطلبات المؤكدة غير المؤرشفة:</h2>";
echo "<p><strong>عدد الطلبات غير المؤرشفة:</strong> " . count($non_archived) . "</p>";

if ($non_archived) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>رقم الطلب</th>";
    echo "<th>تاريخ التأكيد</th>";
    echo "<th>حالة WC</th>";
    echo "</tr>";
    
    foreach ($non_archived as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>{$order->post_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><a href='http://localhost/akadly/wp-admin/admin.php?page=hozi-akadly-delivery-tracking' class='button button-primary'>انتقل إلى صفحة التتبع</a></p>";
} else {
    echo "<p>❌ لا توجد طلبات مؤكدة غير مؤرشفة.</p>";
}
?>
