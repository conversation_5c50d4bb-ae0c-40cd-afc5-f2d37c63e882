<?php
/**
 * Debug Messaging Issues
 * Check database and messaging system status
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

global $wpdb;

echo "<h2>🔍 تشخيص مشاكل نظام المراسلة</h2>";

// Check if messaging table exists
$table_name = $wpdb->prefix . 'hozi_messages';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

echo "<h3>📋 حالة جدول الرسائل</h3>";
if ($table_exists) {
    echo "✅ الجدول موجود: $table_name<br>";
    
    // Show table structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    echo "<strong>أعمدة الجدول:</strong><br>";
    foreach ($columns as $column) {
        echo "- {$column->Field} ({$column->Type})<br>";
    }
    
    // Count total messages
    $total_messages = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "<br><strong>إجمالي الرسائل:</strong> $total_messages<br>";
    
    // Show recent messages
    echo "<h3>📬 آخر 10 رسائل</h3>";
    $recent_messages = $wpdb->get_results("
        SELECT m.*, 
               sender.display_name as sender_name,
               recipient.display_name as recipient_name
        FROM $table_name m
        LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
        LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
        ORDER BY m.created_at DESC
        LIMIT 10
    ");
    
    if ($recent_messages) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>من</th><th>إلى</th><th>نوع المستقبل</th><th>الموضوع</th><th>مقروءة</th><th>محذوفة</th><th>مؤرشفة</th><th>التاريخ</th></tr>";
        foreach ($recent_messages as $msg) {
            echo "<tr>";
            echo "<td>{$msg->id}</td>";
            echo "<td>{$msg->sender_name} (ID: {$msg->sender_id})</td>";
            echo "<td>{$msg->recipient_name} (ID: {$msg->recipient_id})</td>";
            echo "<td>{$msg->recipient_type}</td>";
            echo "<td>{$msg->subject}</td>";
            echo "<td>" . ($msg->is_read ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . ($msg->is_deleted ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . (isset($msg->is_archived) && $msg->is_archived ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$msg->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ لا توجد رسائل في الجدول";
    }
    
} else {
    echo "❌ الجدول غير موجود: $table_name<br>";
}

// Check agents
echo "<h3>👥 قائمة الوكلاء</h3>";
$agents = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'hozi_akadly_agent',
            'value' => '1',
            'compare' => '='
        )
    )
));

if ($agents) {
    echo "<strong>الوكلاء الموجودين:</strong><br>";
    foreach ($agents as $agent) {
        echo "- {$agent->display_name} (ID: {$agent->ID}, Email: {$agent->user_email})<br>";
    }
} else {
    echo "❌ لم يتم العثور على وكلاء<br>";
    
    // Try alternative method
    $agents_alt = get_users(array('capability' => 'hozi_view_assigned_orders'));
    if ($agents_alt) {
        echo "<strong>وكلاء بالصلاحيات:</strong><br>";
        foreach ($agents_alt as $agent) {
            echo "- {$agent->display_name} (ID: {$agent->ID})<br>";
        }
    }
}

// Check current user
$current_user = wp_get_current_user();
echo "<h3>👤 المستخدم الحالي</h3>";
echo "الاسم: {$current_user->display_name}<br>";
echo "ID: {$current_user->ID}<br>";
echo "البريد: {$current_user->user_email}<br>";
echo "الأدوار: " . implode(', ', $current_user->roles) . "<br>";

// Check if current user is agent
$is_agent = get_user_meta($current_user->ID, 'hozi_akadly_agent', true);
echo "هل هو وكيل: " . ($is_agent ? 'نعم' : 'لا') . "<br>";

// Test message query for current user
echo "<h3>🔍 اختبار استعلام الرسائل للمستخدم الحالي</h3>";
$user_messages = $wpdb->get_results($wpdb->prepare("
    SELECT m.*, 
           sender.display_name as sender_name,
           recipient.display_name as recipient_name
    FROM $table_name m
    LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
    LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
    WHERE (m.recipient_id = %d OR (m.recipient_type = 'all' AND m.sender_id != %d) OR m.sender_id = %d)
    AND m.is_deleted = 0
    AND (m.is_archived IS NULL OR m.is_archived = 0)
    ORDER BY m.created_at DESC
", $current_user->ID, $current_user->ID, $current_user->ID));

echo "عدد الرسائل للمستخدم الحالي: " . count($user_messages) . "<br>";

if ($user_messages) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>من</th><th>إلى</th><th>نوع المستقبل</th><th>الموضوع</th><th>مقروءة</th><th>التاريخ</th></tr>";
    foreach ($user_messages as $msg) {
        echo "<tr>";
        echo "<td>{$msg->id}</td>";
        echo "<td>{$msg->sender_name}</td>";
        echo "<td>{$msg->recipient_name}</td>";
        echo "<td>{$msg->recipient_type}</td>";
        echo "<td>{$msg->subject}</td>";
        echo "<td>" . ($msg->is_read ? 'نعم' : 'لا') . "</td>";
        echo "<td>{$msg->created_at}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check order assignments table
echo "<h3>📋 جدول تخصيص الطلبات</h3>";
$assignments_table = $wpdb->prefix . 'hozi_order_assignments';
$assignments_exists = $wpdb->get_var("SHOW TABLES LIKE '$assignments_table'") == $assignments_table;

if ($assignments_exists) {
    echo "✅ الجدول موجود: $assignments_table<br>";
    
    // Show table structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table");
    echo "<strong>أعمدة الجدول:</strong><br>";
    foreach ($columns as $column) {
        echo "- {$column->Field} ({$column->Type})<br>";
    }
    
    // Show recent assignments
    echo "<h4>آخر 5 تخصيصات:</h4>";
    $recent_assignments = $wpdb->get_results("
        SELECT * FROM $assignments_table 
        ORDER BY assigned_at DESC 
        LIMIT 5
    ");
    
    if ($recent_assignments) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>طلب</th><th>وكيل</th><th>حالة التأكيد</th><th>مؤكد بواسطة مدير</th><th>معرف المدير</th><th>التاريخ</th></tr>";
        foreach ($recent_assignments as $assignment) {
            echo "<tr>";
            echo "<td>{$assignment->id}</td>";
            echo "<td>{$assignment->order_id}</td>";
            echo "<td>{$assignment->agent_id}</td>";
            echo "<td>{$assignment->confirmation_status}</td>";
            echo "<td>" . (isset($assignment->confirmed_by_admin) ? ($assignment->confirmed_by_admin ? 'نعم' : 'لا') : 'غير محدد') . "</td>";
            echo "<td>" . (isset($assignment->admin_user_id) ? $assignment->admin_user_id : 'غير محدد') . "</td>";
            echo "<td>{$assignment->assigned_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "❌ الجدول غير موجود: $assignments_table<br>";
}

echo "<br><p><strong>تم الانتهاء من التشخيص</strong></p>";
?>