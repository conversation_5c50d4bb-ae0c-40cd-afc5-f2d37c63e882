<?php
/**
 * Debug Delivery Tracking System
 * 
 * Place this file in your WordPress root directory and access it via browser
 * to test the delivery tracking system.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🔍 تشخيص نظام متابعة التوصيل - Akadly</h1>";

global $wpdb;

// 1. Check if tracking table exists
echo "<h2>📋 فحص جدول التتبع:</h2>";
$table_name = $wpdb->prefix . 'hozi_order_tracking';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "<p style='color: green;'>✅ جدول hozi_order_tracking موجود</p>";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo "<h3>📊 هيكل الجدول:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th style='padding: 8px;'>العمود</th><th style='padding: 8px;'>النوع</th><th style='padding: 8px;'>Null</th><th style='padding: 8px;'>Key</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$column->Field}</td>";
        echo "<td style='padding: 8px;'>{$column->Type}</td>";
        echo "<td style='padding: 8px;'>{$column->Null}</td>";
        echo "<td style='padding: 8px;'>{$column->Key}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Count records
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "<p><strong>عدد السجلات:</strong> {$count}</p>";
    
} else {
    echo "<p style='color: red;'>❌ جدول hozi_order_tracking غير موجود</p>";
    echo "<p>🔧 محاولة إنشاء الجدول...</p>";
    
    // Try to create table
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        order_id bigint(20) NOT NULL,
        agent_id int(11) NOT NULL,
        status varchar(50) NOT NULL,
        previous_status varchar(50) DEFAULT NULL,
        reason_category varchar(100) DEFAULT NULL,
        reason_details text DEFAULT NULL,
        notes text DEFAULT NULL,
        updated_by int(11) NOT NULL,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY order_id (order_id),
        KEY agent_id (agent_id),
        KEY status (status),
        KEY updated_at (updated_at)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $result = dbDelta($sql);
    
    // Check if created
    $table_exists_after = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    if ($table_exists_after) {
        echo "<p style='color: green;'>✅ تم إنشاء الجدول بنجاح!</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء الجدول</p>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    }
}

// 2. Check delivery tracking permissions
echo "<h2>🔐 فحص أذونات متابعة التوصيل:</h2>";
if (class_exists('Hozi_Akadly_Delivery_Tracking_Permissions')) {
    $enabled = Hozi_Akadly_Delivery_Tracking_Permissions::is_delivery_tracking_enabled();
    $can_access = Hozi_Akadly_Delivery_Tracking_Permissions::can_access_delivery_tracking();
    $description = Hozi_Akadly_Delivery_Tracking_Permissions::get_access_control_description();
    
    echo "<p><strong>نظام متابعة التوصيل:</strong> " . ($enabled ? '✅ مفعل' : '❌ معطل') . "</p>";
    echo "<p><strong>يمكن للمستخدم الحالي الوصول:</strong> " . ($can_access ? '✅ نعم' : '❌ لا') . "</p>";
    echo "<p><strong>وصف الوصول:</strong> {$description}</p>";
} else {
    echo "<p style='color: red;'>❌ كلاس Hozi_Akadly_Delivery_Tracking_Permissions غير موجود</p>";
}

// 3. Check recent confirmed orders
echo "<h2>📦 فحص الطلبات المؤكدة الحديثة:</h2>";
$confirmed_orders = $wpdb->get_results("
    SELECT oa.order_id, oa.agent_id, oa.confirmation_status, oa.confirmed_at,
           ag.name as agent_name,
           ot.id as tracking_id, ot.status as tracking_status
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents ag ON oa.agent_id = ag.id
    LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
    WHERE oa.confirmation_status = 'confirmed'
    ORDER BY oa.confirmed_at DESC
    LIMIT 10
");

if ($confirmed_orders) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>رقم الطلب</th>";
    echo "<th style='padding: 8px;'>الوكيل</th>";
    echo "<th style='padding: 8px;'>تاريخ التأكيد</th>";
    echo "<th style='padding: 8px;'>في التتبع؟</th>";
    echo "<th style='padding: 8px;'>حالة التتبع</th>";
    echo "</tr>";
    
    foreach ($confirmed_orders as $order) {
        $in_tracking = $order->tracking_id ? '✅ نعم' : '❌ لا';
        $tracking_status = $order->tracking_status ?: 'غير محدد';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$order->order_id}</td>";
        echo "<td style='padding: 8px;'>{$order->agent_name}</td>";
        echo "<td style='padding: 8px;'>{$order->confirmed_at}</td>";
        echo "<td style='padding: 8px;'>{$in_tracking}</td>";
        echo "<td style='padding: 8px;'>{$tracking_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات مؤكدة حديثة</p>";
}

// 4. Test auto transfer function
echo "<h2>🧪 اختبار دالة النقل التلقائي:</h2>";

// Find a confirmed order without tracking
$test_order = $wpdb->get_row("
    SELECT oa.order_id, oa.agent_id
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
    WHERE oa.confirmation_status = 'confirmed' 
    AND ot.id IS NULL
    LIMIT 1
");

if ($test_order) {
    echo "<p>🎯 وجد طلب مؤكد بدون تتبع: رقم {$test_order->order_id}</p>";
    
    if (isset($_GET['test_transfer']) && $_GET['test_transfer'] == '1') {
        echo "<p>🔄 تشغيل اختبار النقل التلقائي...</p>";
        
        // Test the auto transfer
        if (class_exists('Hozi_Akadly_Order_Distributor')) {
            $distributor = new Hozi_Akadly_Order_Distributor();
            
            // Use reflection to call private method
            $reflection = new ReflectionClass($distributor);
            $method = $reflection->getMethod('auto_transfer_to_tracking');
            $method->setAccessible(true);
            
            try {
                $method->invoke($distributor, $test_order->order_id, $test_order->agent_id);
                echo "<p style='color: green;'>✅ تم تشغيل دالة النقل التلقائي</p>";
                
                // Check if it worked
                $tracking_check = $wpdb->get_var($wpdb->prepare(
                    "SELECT id FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                    $test_order->order_id
                ));
                
                if ($tracking_check) {
                    echo "<p style='color: green;'>✅ تم إنشاء سجل التتبع بنجاح!</p>";
                } else {
                    echo "<p style='color: red;'>❌ لم يتم إنشاء سجل التتبع</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ خطأ في تشغيل دالة النقل: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ كلاس Hozi_Akadly_Order_Distributor غير موجود</p>";
        }
    } else {
        echo "<p><a href='?test_transfer=1' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🧪 اختبار النقل التلقائي</a></p>";
    }
} else {
    echo "<p>لا توجد طلبات مؤكدة بدون تتبع للاختبار</p>";
}

// 5. Check error logs
echo "<h2>📝 فحص سجلات الأخطاء:</h2>";
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $akadly_logs = array();
    
    $lines = explode("\n", $log_content);
    foreach ($lines as $line) {
        if (strpos($line, 'Hozi Akadly') !== false) {
            $akadly_logs[] = $line;
        }
    }
    
    if ($akadly_logs) {
        echo "<p>آخر 10 سجلات متعلقة بـ Akadly:</p>";
        echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;'>";
        $recent_logs = array_slice($akadly_logs, -10);
        foreach ($recent_logs as $log) {
            echo "<p style='margin: 5px 0; font-family: monospace; font-size: 12px;'>" . esc_html($log) . "</p>";
        }
        echo "</div>";
    } else {
        echo "<p>لا توجد سجلات متعلقة بـ Akadly</p>";
    }
} else {
    echo "<p>ملف السجلات غير موجود</p>";
}

echo "<hr>";
echo "<p><strong>تم الانتهاء من التشخيص</strong> - " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل</a></p>";
?>
