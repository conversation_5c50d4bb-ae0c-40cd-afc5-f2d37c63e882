<?php
/**
 * Agent orders view - Shows all orders for a specific agent
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1>
        <?php printf(__('طلبات الوكيل: %s', 'hozi-akadly'), esc_html($agent->name)); ?>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-analytics'); ?>" class="page-title-action">
            <?php _e('العودة للتحليلات', 'hozi-akadly'); ?>
        </a>
    </h1>

    <!-- Agent Stats Summary -->
    <div class="hozi-agent-summary">
        <div class="hozi-stats-grid">
            <div class="hozi-stat-card total">
                <div class="hozi-stat-icon">📊</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($agent_stats->total ?? 0); ?></h3>
                    <p><?php _e('إجمالي الطلبات', 'hozi-akadly'); ?></p>
                </div>
            </div>
            <div class="hozi-stat-card pending">
                <div class="hozi-stat-icon">⏳</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($agent_stats->pending ?? 0); ?></h3>
                    <p><?php _e('في الانتظار', 'hozi-akadly'); ?></p>
                </div>
            </div>
            <div class="hozi-stat-card confirmed">
                <div class="hozi-stat-icon">✅</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($agent_stats->confirmed ?? 0); ?></h3>
                    <p><?php _e('مؤكدة', 'hozi-akadly'); ?></p>
                </div>
            </div>
            <div class="hozi-stat-card rejected">
                <div class="hozi-stat-icon">❌</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($agent_stats->rejected ?? 0); ?></h3>
                    <p><?php _e('مرفوضة', 'hozi-akadly'); ?></p>
                </div>
            </div>
            <div class="hozi-stat-card no-answer">
                <div class="hozi-stat-icon">📞</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($agent_stats->no_answer ?? 0); ?></h3>
                    <p><?php _e('لم يرد', 'hozi-akadly'); ?></p>
                </div>
            </div>
            <div class="hozi-stat-card callback">
                <div class="hozi-stat-icon">🔄</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($agent_stats->callback_later ?? 0); ?></h3>
                    <p><?php _e('إعادة اتصال', 'hozi-akadly'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="hozi-filters">
        <div class="hozi-filter-tabs">
            <?php
            $base_url = admin_url('admin.php?page=hozi-akadly-assignments&agent_id=' . $agent->id);
            $filters = array(
                'all' => __('الكل', 'hozi-akadly'),
                'pending_confirmation' => __('في الانتظار', 'hozi-akadly'),
                'confirmed' => __('مؤكدة', 'hozi-akadly'),
                'rejected' => __('مرفوضة', 'hozi-akadly'),
                'no_answer' => __('لم يرد', 'hozi-akadly'),
                'callback_later' => __('إعادة اتصال', 'hozi-akadly')
            );

            foreach ($filters as $filter_key => $filter_label) {
                $filter_url = add_query_arg('status', $filter_key, $base_url);
                $active_class = ($status_filter === $filter_key) ? ' active' : '';
                echo '<a href="' . esc_url($filter_url) . '" class="hozi-filter-tab' . $active_class . '">' . esc_html($filter_label) . '</a>';
            }
            ?>
        </div>

        <?php if ($pagination_info && $pagination_info['total_orders'] > 0) : ?>
            <div class="hozi-pagination-info">
                <?php printf(
                    __('عرض %d-%d من %d طلب', 'hozi-akadly'),
                    $pagination_info['showing_from'],
                    $pagination_info['showing_to'],
                    $pagination_info['total_orders']
                ); ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Orders Table -->
    <?php if (!empty($agent_orders)) : ?>
        <div class="hozi-orders-table">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('رقم الطلب', 'hozi-akadly'); ?></th>
                        <th><?php _e('العميل', 'hozi-akadly'); ?></th>
                        <th><?php _e('المبلغ', 'hozi-akadly'); ?></th>
                        <th><?php _e('الحالة', 'hozi-akadly'); ?></th>
                        <th><?php _e('تاريخ التخصيص', 'hozi-akadly'); ?></th>
                        <th><?php _e('تاريخ التأكيد', 'hozi-akadly'); ?></th>
                        <th><?php _e('الإجراءات', 'hozi-akadly'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($agent_orders as $assignment) :
                        $order = wc_get_order($assignment->order_id);
                        if (!$order) continue;

                        $status_labels = array(
                            'pending_confirmation' => __('في الانتظار', 'hozi-akadly'),
                            'confirmed' => __('مؤكد', 'hozi-akadly'),
                            'rejected' => __('مرفوض', 'hozi-akadly'),
                            'no_answer' => __('لم يرد', 'hozi-akadly'),
                            'callback_later' => __('إعادة اتصال', 'hozi-akadly')
                        );

                        $status_classes = array(
                            'pending_confirmation' => 'pending',
                            'confirmed' => 'confirmed',
                            'rejected' => 'rejected',
                            'no_answer' => 'no-answer',
                            'callback_later' => 'callback'
                        );
                    ?>
                        <tr>
                            <td>
                                <strong>#<?php echo esc_html($assignment->order_id); ?></strong>
                                <div class="order-meta">
                                    <?php echo esc_html(date_i18n('Y/m/d', strtotime($assignment->order_date))); ?>
                                </div>
                            </td>
                            <td>
                                <strong><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></strong>
                                <div class="customer-meta">
                                    <?php echo esc_html($order->get_billing_phone()); ?>
                                </div>
                            </td>
                            <td>
                                <strong><?php echo $order->get_formatted_order_total(); ?></strong>
                            </td>
                            <td>
                                <span class="hozi-badge <?php echo esc_attr($status_classes[$assignment->confirmation_status] ?? 'pending'); ?>">
                                    <?php echo esc_html($status_labels[$assignment->confirmation_status] ?? $assignment->confirmation_status); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($assignment->assigned_at))); ?>
                            </td>
                            <td>
                                <?php if ($assignment->confirmed_at) : ?>
                                    <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($assignment->confirmed_at))); ?>
                                <?php else : ?>
                                    <span class="hozi-no-data">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('post.php?post=' . $assignment->order_id . '&action=edit'); ?>"
                                   class="button button-small">
                                    <?php _e('عرض', 'hozi-akadly'); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else : ?>
        <div class="hozi-no-orders">
            <div class="hozi-no-orders-icon">
                <span class="dashicons dashicons-cart"></span>
            </div>
            <h3><?php _e('لا توجد طلبات', 'hozi-akadly'); ?></h3>
            <p><?php _e('لا توجد طلبات للوكيل في هذا الفلتر', 'hozi-akadly'); ?></p>
        </div>
    <?php endif; ?>

    <!-- Pagination -->
    <?php if ($pagination_info && $pagination_info['total_pages'] > 1) : ?>
        <div class="hozi-pagination">
            <?php
            $current_page = $pagination_info['current_page'];
            $total_pages = $pagination_info['total_pages'];
            $base_url = admin_url('admin.php?page=hozi-akadly-assignments&agent_id=' . $agent->id);
            if ($status_filter !== 'all') {
                $base_url = add_query_arg('status', $status_filter, $base_url);
            }

            // Previous page
            if ($current_page > 1) {
                $prev_url = add_query_arg('paged', $current_page - 1, $base_url);
                echo '<a href="' . esc_url($prev_url) . '" class="hozi-pagination-btn hozi-pagination-prev">';
                echo '<span class="dashicons dashicons-arrow-right-alt2"></span> ' . __('السابق', 'hozi-akadly');
                echo '</a>';
            }

            // Page numbers
            echo '<div class="hozi-pagination-numbers">';
            for ($i = 1; $i <= $total_pages; $i++) {
                $page_url = add_query_arg('paged', $i, $base_url);
                $active_class = ($i == $current_page) ? ' hozi-pagination-active' : '';
                echo '<a href="' . esc_url($page_url) . '" class="hozi-pagination-number' . $active_class . '">' . $i . '</a>';
            }
            echo '</div>';

            // Next page
            if ($current_page < $total_pages) {
                $next_url = add_query_arg('paged', $current_page + 1, $base_url);
                echo '<a href="' . esc_url($next_url) . '" class="hozi-pagination-btn hozi-pagination-next">';
                echo __('التالي', 'hozi-akadly') . ' <span class="dashicons dashicons-arrow-left-alt2"></span>';
                echo '</a>';
            }
            ?>
        </div>
    <?php endif; ?>
</div>

<style>
.hozi-agent-summary {
    margin-bottom: 30px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.hozi-stat-card {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease;
}

.hozi-stat-card:hover {
    transform: translateY(-2px);
}

.hozi-stat-card.total { border-left: 4px solid #3498db; }
.hozi-stat-card.pending { border-left: 4px solid #95a5a6; }
.hozi-stat-card.confirmed { border-left: 4px solid #27ae60; }
.hozi-stat-card.rejected { border-left: 4px solid #e74c3c; }
.hozi-stat-card.no-answer { border-left: 4px solid #f39c12; }
.hozi-stat-card.callback { border-left: 4px solid #3498db; }

.hozi-stat-icon {
    font-size: 24px;
    opacity: 0.8;
}

.hozi-stat-content h3 {
    font-size: 24px;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.hozi-stat-content p {
    margin: 0;
    color: #7f8c8d;
    font-size: 13px;
}

.hozi-filters {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.hozi-filter-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-filter-tab {
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    text-decoration: none;
    color: #495057;
    font-size: 14px;
    transition: all 0.3s ease;
}

.hozi-filter-tab:hover {
    background: #e9ecef;
    text-decoration: none;
    color: #495057;
}

.hozi-filter-tab.active {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
}

.hozi-pagination-info {
    background: #f8f9fa;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    border: 1px solid #e9ecef;
}

.hozi-orders-table {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.hozi-orders-table table {
    margin: 0;
}

.order-meta, .customer-meta {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.hozi-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.hozi-badge.pending { background: #95a5a6; }
.hozi-badge.confirmed { background: #27ae60; }
.hozi-badge.rejected { background: #e74c3c; }
.hozi-badge.no-answer { background: #f39c12; }
.hozi-badge.callback { background: #3498db; }

.hozi-no-data {
    color: #999;
    font-style: italic;
}

.hozi-no-orders {
    background: #fff;
    padding: 60px 20px;
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-no-orders-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.hozi-no-orders h3 {
    color: #666;
    margin-bottom: 10px;
}

.hozi-no-orders p {
    color: #999;
}

/* Pagination Styles */
.hozi-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    padding: 20px 0;
}

.hozi-pagination-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-pagination-btn:hover {
    background: #f8f9fa;
    border-color: #007cba;
    color: #007cba;
    text-decoration: none;
}

.hozi-pagination-numbers {
    display: flex;
    gap: 5px;
}

.hozi-pagination-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-pagination-number:hover {
    background: #f8f9fa;
    border-color: #007cba;
    color: #007cba;
    text-decoration: none;
}

.hozi-pagination-number.hozi-pagination-active {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
}

.hozi-pagination-number.hozi-pagination-active:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
}

@media (max-width: 768px) {
    .hozi-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .hozi-stat-card {
        padding: 15px;
    }

    .hozi-stat-content h3 {
        font-size: 20px;
    }

    .hozi-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .hozi-filter-tabs {
        justify-content: center;
    }

    .hozi-pagination {
        flex-wrap: wrap;
        gap: 8px;
    }

    .hozi-pagination-btn {
        padding: 8px 12px;
        font-size: 14px;
    }

    .hozi-pagination-number {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}
</style>
