<?php
/**
 * Fix assignments and create test data
 * أكدلي - Akadly Plugin
 */

// WordPress environment
require_once('wp-config.php');

echo "<h1>🔧 إصلاح التخصيصات - أكدلي</h1>";

global $wpdb;

// Get agents
$agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1");
$recent_orders = $wpdb->get_results("
    SELECT p.ID, p.post_date, p.post_status
    FROM {$wpdb->prefix}posts p
    WHERE p.post_type = 'shop_order'
    AND p.post_status NOT IN ('trash', 'auto-draft')
    ORDER BY p.post_date DESC
    LIMIT 5
");

if (empty($agents)) {
    echo "<p style='color: red;'>❌ لا توجد وكلاء نشطين!</p>";
    exit;
}

if (empty($recent_orders)) {
    echo "<p style='color: red;'>❌ لا توجد طلبات!</p>";
    exit;
}

echo "<h2>🔧 إصلاح المشاكل:</h2>";

// Handle actions
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'clear_assignments':
            $result = $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_order_assignments");
            echo "<div style='background: #d1edff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم مسح جميع التخصيصات!</h3>";
            echo "<p>تم مسح {$result} تخصيص.</p>";
            echo "</div>";
            break;

        case 'create_test_assignments':
            $created = 0;
            $first_agent = $agents[0];

            foreach (array_slice($recent_orders, 0, 3) as $order) {
                // Delete existing assignment first
                $wpdb->delete(
                    $wpdb->prefix . 'hozi_order_assignments',
                    array('order_id' => $order->ID),
                    array('%d')
                );

                // Create new assignment
                $result = $wpdb->insert(
                    $wpdb->prefix . 'hozi_order_assignments',
                    array(
                        'order_id' => $order->ID,
                        'agent_id' => $first_agent->id,
                        'confirmation_status' => 'confirmed',
                        'assigned_at' => current_time('mysql'),
                        'confirmed_at' => current_time('mysql'),
                        'assignment_method' => 'manual',
                        'archived' => 0
                    ),
                    array('%d', '%d', '%s', '%s', '%s', '%s', '%d')
                );

                if ($result) {
                    $created++;
                }
            }

            echo "<div style='background: #d1edff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم إنشاء {$created} تخصيص تجريبي!</h3>";
            echo "<p>تم تخصيص الطلبات للوكيل: <strong>{$first_agent->name}</strong> (ID: {$first_agent->id})</p>";
            echo "</div>";
            break;

        case 'assign_to_current_user':
            // Get current user
            $current_user_id = get_current_user_id();
            $current_agent = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d AND is_active = 1",
                $current_user_id
            ));

            if (!$current_agent) {
                echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h3>❌ المستخدم الحالي ليس وكيل!</h3>";
                echo "<p>User ID: {$current_user_id}</p>";
                echo "</div>";
                break;
            }

            $created = 0;
            foreach (array_slice($recent_orders, 0, 3) as $order) {
                // Delete existing assignment first
                $wpdb->delete(
                    $wpdb->prefix . 'hozi_order_assignments',
                    array('order_id' => $order->ID),
                    array('%d')
                );

                // Create new assignment for current agent
                $result = $wpdb->insert(
                    $wpdb->prefix . 'hozi_order_assignments',
                    array(
                        'order_id' => $order->ID,
                        'agent_id' => $current_agent->id,
                        'confirmation_status' => 'confirmed',
                        'assigned_at' => current_time('mysql'),
                        'confirmed_at' => current_time('mysql'),
                        'assignment_method' => 'manual',
                        'archived' => 0
                    ),
                    array('%d', '%d', '%s', '%s', '%s', '%s', '%d')
                );

                if ($result) {
                    $created++;
                }
            }

            echo "<div style='background: #d1edff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم تخصيص {$created} طلب للوكيل الحالي!</h3>";
            echo "<p>الوكيل: <strong>{$current_agent->name}</strong> (ID: {$current_agent->id})</p>";
            echo "<p>User ID: {$current_user_id}</p>";
            echo "</div>";
            break;

        case 'fix_duplicate':
            // Fix the duplicate entry error for order 10179
            $wpdb->delete(
                $wpdb->prefix . 'hozi_order_assignments',
                array('order_id' => 10179),
                array('%d')
            );

            echo "<div style='background: #d1edff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم إصلاح مشكلة الطلب المكرر!</h3>";
            echo "<p>تم حذف التخصيص المكرر للطلب #10179</p>";
            echo "</div>";
            break;
    }
}

echo "<div style='background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd; margin: 20px 0;'>";
echo "<h3>🛠️ أدوات الإصلاح:</h3>";

echo "<p><a href='?action=fix_duplicate' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔧 إصلاح الطلب المكرر #10179</a></p>";

echo "<p><a href='?action=clear_assignments' style='background: #f44336; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;' onclick='return confirm(\"هل أنت متأكد من مسح جميع التخصيصات؟\")'>🗑️ مسح جميع التخصيصات</a></p>";

echo "<p><a href='?action=create_test_assignments' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>➕ إنشاء تخصيصات تجريبية</a></p>";

echo "<p><a href='?action=assign_to_current_user' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>👤 تخصيص للمستخدم الحالي</a></p>";

echo "</div>";

echo "<h2>📊 الحالة الحالية:</h2>";

// Show current user info
$current_user_id = get_current_user_id();
$current_user = wp_get_current_user();
$current_agent = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d",
    $current_user_id
));

echo "<h3>👤 معلومات المستخدم الحالي:</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<p><strong>اسم المستخدم:</strong> {$current_user->display_name}</p>";
echo "<p><strong>User ID:</strong> {$current_user_id}</p>";
echo "<p><strong>البريد الإلكتروني:</strong> {$current_user->user_email}</p>";
echo "<p><strong>الأدوار:</strong> " . implode(', ', $current_user->roles) . "</p>";

if ($current_agent) {
    echo "<p><strong>✅ هذا المستخدم وكيل:</strong> {$current_agent->name} (Agent ID: {$current_agent->id})</p>";
    echo "<p><strong>حالة الوكيل:</strong> " . ($current_agent->is_active ? 'نشط' : 'غير نشط') . "</p>";
} else {
    echo "<p><strong>❌ هذا المستخدم ليس وكيل</strong></p>";
}
echo "</div>";

// Show current assignments
$current_assignments = $wpdb->get_results("
    SELECT
        oa.id,
        oa.order_id,
        oa.agent_id,
        oa.confirmation_status,
        oa.assigned_at,
        oa.confirmed_at,
        oa.archived,
        a.name as agent_name
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    ORDER BY oa.assigned_at DESC
    LIMIT 10
");

echo "<h3>📋 التخصيصات الحالية:</h3>";
if (!empty($current_assignments)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 13px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>رقم الطلب</th><th>الوكيل</th><th>حالة التأكيد</th><th>تاريخ التخصيص</th><th>مؤرشف؟</th></tr>";

    foreach ($current_assignments as $assignment) {
        $archived_status = $assignment->archived ? 'نعم' : 'لا';

        echo "<tr>";
        echo "<td>{$assignment->id}</td>";
        echo "<td>#{$assignment->order_id}</td>";
        echo "<td>{$assignment->agent_name}</td>";
        echo "<td>{$assignment->confirmation_status}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($assignment->assigned_at)) . "</td>";
        echo "<td>{$archived_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ لا توجد تخصيصات حالياً!</p>";
}

echo "<h3>👥 الوكلاء المتاحين:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 13px;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>ID</th><th>الاسم</th><th>User ID</th><th>البريد</th><th>نشط؟</th></tr>";

foreach ($agents as $agent) {
    $active_status = $agent->is_active ? 'نعم' : 'لا';
    echo "<tr>";
    echo "<td>{$agent->id}</td>";
    echo "<td>{$agent->name}</td>";
    echo "<td>{$agent->user_id}</td>";
    echo "<td>{$agent->email}</td>";
    echo "<td>{$active_status}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>📦 الطلبات المتاحة:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 13px;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>رقم الطلب</th><th>التاريخ</th><th>الحالة</th><th>مخصص؟</th></tr>";

foreach ($recent_orders as $order) {
    $assigned = $wpdb->get_var($wpdb->prepare(
        "SELECT agent_id FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
        $order->ID
    ));

    $assignment_status = $assigned ? "نعم (وكيل #{$assigned})" : 'لا';

    echo "<tr>";
    echo "<td>#{$order->ID}</td>";
    echo "<td>" . date('Y/m/d H:i', strtotime($order->post_date)) . "</td>";
    echo "<td>{$order->post_status}</td>";
    echo "<td>{$assignment_status}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔗 الخطوات التالية:</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;'>";
echo "<h3>📋 خطة الإصلاح:</h3>";
echo "<ol>";
echo "<li><strong>إصلاح الطلب المكرر:</strong> اضغط على زر 'إصلاح الطلب المكرر #10179'</li>";
echo "<li><strong>إنشاء تخصيصات تجريبية:</strong> اضغط على زر 'إنشاء تخصيصات تجريبية'</li>";
echo "<li><strong>اختبار صفحة التتبع:</strong> ادخل كوكيل لصفحة التتبع</li>";
echo "<li><strong>التحقق من النتائج:</strong> تأكد من ظهور الطلبات المؤكدة</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-my-tracking') . "' style='background: #2196f3; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔗 صفحة التتبع</a></p>";
echo "<p><a href='test-tracking-page.php' style='background: #4caf50; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🧪 اختبار التتبع</a></p>";
echo "<p><a href='create-test-assignment.php' style='background: #ff9800; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>➕ إنشاء تخصيص</a></p>";
echo "</div>";
?>
