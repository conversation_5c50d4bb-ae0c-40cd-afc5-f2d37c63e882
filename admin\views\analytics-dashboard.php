<?php
/**
 * Analytics dashboard view
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get analytics data
global $wpdb;

// Date range (last 30 days by default)
$start_date = isset($_GET['start_date']) ? sanitize_text_field($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? sanitize_text_field($_GET['end_date']) : date('Y-m-d');

// Overall stats
$total_orders = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments
     WHERE DATE(assigned_at) BETWEEN %s AND %s",
    $start_date, $end_date
));

$confirmed_orders = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments
     WHERE status = 'confirmed' AND DATE(assigned_at) BETWEEN %s AND %s",
    $start_date, $end_date
));

$rejected_orders = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments
     WHERE status = 'rejected' AND DATE(assigned_at) BETWEEN %s AND %s",
    $start_date, $end_date
));

$no_answer_orders = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments
     WHERE status = 'no_answer' AND DATE(assigned_at) BETWEEN %s AND %s",
    $start_date, $end_date
));

// Agent performance with upsell data
$agent_stats = $wpdb->get_results($wpdb->prepare(
    "SELECT
        a.name as agent_name,
        a.id as agent_id,
        COUNT(oa.id) as total_orders,
        SUM(CASE WHEN oa.confirmation_status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
        SUM(CASE WHEN oa.confirmation_status = 'rejected' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN oa.confirmation_status = 'no_answer' THEN 1 ELSE 0 END) as no_answer,
        SUM(CASE WHEN oa.confirmation_status = 'callback_later' THEN 1 ELSE 0 END) as callback_later,
        SUM(CASE WHEN oa.confirmation_status = 'pending_confirmation' THEN 1 ELSE 0 END) as pending,
        COUNT(ut.id) as upsell_attempts,
        SUM(CASE WHEN ut.upsell_successful = 1 THEN 1 ELSE 0 END) as upsell_success,
        ROUND(
            (SUM(CASE WHEN oa.confirmation_status = 'confirmed' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(oa.id), 0)), 2
        ) as success_rate,
        ROUND(
            (SUM(CASE WHEN ut.upsell_successful = 1 THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(ut.id), 0)), 2
        ) as upsell_rate
    FROM {$wpdb->prefix}hozi_agents a
    LEFT JOIN {$wpdb->prefix}hozi_order_assignments oa ON a.id = oa.agent_id
    LEFT JOIN {$wpdb->prefix}hozi_upsell_tracking ut ON oa.order_id = ut.order_id AND a.id = ut.agent_id
    WHERE a.is_active = 1 AND (oa.assigned_at IS NULL OR DATE(oa.assigned_at) BETWEEN %s AND %s)
    GROUP BY a.id, a.name
    ORDER BY success_rate DESC",
    $start_date, $end_date
));

// Daily stats for chart
$daily_stats = $wpdb->get_results($wpdb->prepare(
    "SELECT
        DATE(assigned_at) as date,
        COUNT(*) as total,
        SUM(CASE WHEN confirmation_status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
        SUM(CASE WHEN confirmation_status = 'rejected' THEN 1 ELSE 0 END) as rejected
    FROM {$wpdb->prefix}hozi_order_assignments
    WHERE DATE(assigned_at) BETWEEN %s AND %s
    GROUP BY DATE(assigned_at)
    ORDER BY date",
    $start_date, $end_date
));

$confirmation_rate = $total_orders > 0 ? round(($confirmed_orders / $total_orders) * 100, 2) : 0;
?>

<div class="wrap">
    <h1><?php _e('تحليلات Hozi Akadly', 'hozi-akadly'); ?></h1>

    <!-- Date Range Filter -->
    <div class="hozi-analytics-filters">
        <form method="get" class="hozi-date-filter">
            <input type="hidden" name="page" value="hozi-akadly-analytics">
            <label for="start_date"><?php _e('من تاريخ:', 'hozi-akadly'); ?></label>
            <input type="date" name="start_date" id="start_date" value="<?php echo esc_attr($start_date); ?>">

            <label for="end_date"><?php _e('إلى تاريخ:', 'hozi-akadly'); ?></label>
            <input type="date" name="end_date" id="end_date" value="<?php echo esc_attr($end_date); ?>">

            <button type="submit" class="button button-primary"><?php _e('تطبيق', 'hozi-akadly'); ?></button>
        </form>
    </div>

    <!-- Overall Stats -->
    <div class="hozi-analytics-overview">
        <div class="hozi-stats-grid">
            <div class="hozi-stat-card total">
                <div class="hozi-stat-icon">📊</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($total_orders); ?></h3>
                    <p><?php _e('إجمالي الطلبات', 'hozi-akadly'); ?></p>
                </div>
            </div>

            <div class="hozi-stat-card confirmed">
                <div class="hozi-stat-icon">✅</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($confirmed_orders); ?></h3>
                    <p><?php _e('طلبات مؤكدة', 'hozi-akadly'); ?></p>
                </div>
            </div>

            <div class="hozi-stat-card rejected">
                <div class="hozi-stat-icon">❌</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($rejected_orders); ?></h3>
                    <p><?php _e('طلبات مرفوضة', 'hozi-akadly'); ?></p>
                </div>
            </div>

            <div class="hozi-stat-card rate">
                <div class="hozi-stat-icon">📈</div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($confirmation_rate); ?>%</h3>
                    <p><?php _e('معدل التأكيد', 'hozi-akadly'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="hozi-analytics-charts">
        <div class="hozi-chart-container">
            <h3><?php _e('الأداء اليومي', 'hozi-akadly'); ?></h3>
            <canvas id="dailyChart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Agent Performance Table -->
    <div class="hozi-analytics-agents">
        <h3><?php _e('أداء الوكلاء', 'hozi-akadly'); ?></h3>

        <?php if (!empty($agent_stats)) : ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('اسم الوكيل', 'hozi-akadly'); ?></th>
                        <th><?php _e('إجمالي الطلبات', 'hozi-akadly'); ?></th>
                        <th><?php _e('في الانتظار', 'hozi-akadly'); ?></th>
                        <th><?php _e('مؤكدة', 'hozi-akadly'); ?></th>
                        <th><?php _e('مرفوضة', 'hozi-akadly'); ?></th>
                        <th><?php _e('لم يرد', 'hozi-akadly'); ?></th>
                        <th><?php _e('إعادة اتصال', 'hozi-akadly'); ?></th>
                        <th><?php _e('معدل النجاح', 'hozi-akadly'); ?></th>
                        <th><?php _e('الإجراءات', 'hozi-akadly'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($agent_stats as $agent) : ?>
                        <tr>
                            <td><strong><?php echo esc_html($agent->agent_name); ?></strong></td>
                            <td><?php echo esc_html($agent->total_orders ?? 0); ?></td>
                            <td><span class="hozi-badge pending"><?php echo esc_html($agent->pending ?? 0); ?></span></td>
                            <td><span class="hozi-badge confirmed"><?php echo esc_html($agent->confirmed ?? 0); ?></span></td>
                            <td><span class="hozi-badge rejected"><?php echo esc_html($agent->rejected ?? 0); ?></span></td>
                            <td><span class="hozi-badge no-answer"><?php echo esc_html($agent->no_answer ?? 0); ?></span></td>
                            <td><span class="hozi-badge callback"><?php echo esc_html($agent->callback_later ?? 0); ?></span></td>
                            <td>
                                <div class="hozi-progress-bar">
                                    <div class="hozi-progress-fill" style="width: <?php echo esc_attr($agent->success_rate ?? 0); ?>%"></div>
                                    <span class="hozi-progress-text"><?php echo esc_html($agent->success_rate ?? 0); ?>%</span>
                                </div>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=hozi-akadly-assignments&agent_id=' . $agent->agent_id); ?>"
                                   class="button button-small">
                                    <?php _e('عرض الطلبات', 'hozi-akadly'); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else : ?>
            <div class="hozi-no-data">
                <p><?php _e('لا توجد بيانات للفترة المحددة', 'hozi-akadly'); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.hozi-analytics-filters {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-date-filter {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.hozi-date-filter label {
    font-weight: bold;
}

.hozi-date-filter input[type="date"] {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.hozi-analytics-overview {
    margin-bottom: 30px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.hozi-stat-card {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.2s ease;
}

.hozi-stat-card:hover {
    transform: translateY(-2px);
}

.hozi-stat-card.total { border-left: 4px solid #3498db; }
.hozi-stat-card.confirmed { border-left: 4px solid #27ae60; }
.hozi-stat-card.rejected { border-left: 4px solid #e74c3c; }
.hozi-stat-card.rate { border-left: 4px solid #f39c12; }

.hozi-stat-icon {
    font-size: 32px;
    opacity: 0.8;
}

.hozi-stat-content h3 {
    font-size: 28px;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.hozi-stat-content p {
    margin: 0;
    color: #7f8c8d;
    font-size: 14px;
}

.hozi-analytics-charts {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-chart-container h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c3e50;
}

.hozi-analytics-agents {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-analytics-agents h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c3e50;
}

.hozi-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.hozi-badge.pending { background: #95a5a6; }
.hozi-badge.confirmed { background: #27ae60; }
.hozi-badge.rejected { background: #e74c3c; }
.hozi-badge.no-answer { background: #f39c12; }
.hozi-badge.callback { background: #3498db; }
.hozi-badge.upsell-attempts { background: #9b59b6; }
.hozi-badge.upsell-success { background: #1abc9c; }

.hozi-progress-bar {
    position: relative;
    background: #ecf0f1;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
}

.hozi-progress-fill {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    height: 100%;
    transition: width 0.3s ease;
}

.hozi-progress-fill.upsell {
    background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.hozi-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: bold;
    color: #2c3e50;
}

.hozi-no-data {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

@media (max-width: 768px) {
    .hozi-date-filter {
        flex-direction: column;
        align-items: stretch;
    }

    .hozi-stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Daily performance chart
const dailyData = <?php echo json_encode($daily_stats); ?>;
const ctx = document.getElementById('dailyChart').getContext('2d');

const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: dailyData.map(item => item.date),
        datasets: [
            {
                label: 'إجمالي الطلبات',
                data: dailyData.map(item => item.total),
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4
            },
            {
                label: 'طلبات مؤكدة',
                data: dailyData.map(item => item.confirmed),
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                tension: 0.4
            },
            {
                label: 'طلبات مرفوضة',
                data: dailyData.map(item => item.rejected),
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                tension: 0.4
            }
        ]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
