<?php
/**
 * Test New Delivery Tracking System
 * Tests the new simple delivery tracking approach
 */

// WordPress environment
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo "<h1>🎯 اختبار نظام متابعة التوصيل الجديد</h1>";

global $wpdb;

// Get test agent
$test_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 LIMIT 1");

if (!$test_agent) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ لا يوجد وكلاء نشطين!</h3>";
    echo "</div>";
    exit;
}

echo "<h2>🧪 الوكيل: {$test_agent->name} (ID: {$test_agent->id})</h2>";

// Check if delivery tracking table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}hozi_delivery_tracking'");

if ($table_exists) {
    echo "<p>✅ جدول hozi_delivery_tracking موجود</p>";
} else {
    echo "<p>❌ جدول hozi_delivery_tracking غير موجود - يجب تفعيل الإضافة مرة أخرى</p>";
    
    // Create table manually for testing
    $sql = "CREATE TABLE {$wpdb->prefix}hozi_delivery_tracking (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        order_id bigint(20) NOT NULL,
        agent_id bigint(20) NOT NULL,
        status varchar(50) NOT NULL,
        notes text DEFAULT NULL,
        updated_at datetime NOT NULL,
        created_at datetime NOT NULL,
        PRIMARY KEY (id),
        KEY order_id (order_id),
        KEY agent_id (agent_id),
        KEY status (status),
        KEY updated_at (updated_at)
    ) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    $result = $wpdb->query($sql);
    if ($result !== false) {
        echo "<p>✅ تم إنشاء الجدول بنجاح</p>";
    } else {
        echo "<p>❌ فشل في إنشاء الجدول: " . $wpdb->last_error . "</p>";
    }
}

// Handle test actions
if (isset($_GET['action'])) {
    $action = sanitize_text_field($_GET['action']);
    
    if ($action === 'create_test_order') {
        echo "<h2>🔧 إنشاء طلب اختبار:</h2>";
        
        // Create a test order
        $order = wc_create_order();
        $order->set_billing_first_name('عميل');
        $order->set_billing_last_name('اختبار التوصيل');
        $order->set_billing_phone('01234567890');
        $order->set_billing_email('<EMAIL>');
        $order->set_billing_address_1('عنوان اختبار التوصيل');
        $order->set_billing_city('القاهرة');
        $order->set_billing_country('EG');
        
        // Add a simple product
        $products = wc_get_products(array('limit' => 1));
        if (!empty($products)) {
            $product = $products[0];
            $order->add_product($product, 1);
        }
        
        $order->calculate_totals();
        $order->set_status('completed'); // Set as completed directly
        $order->save();
        
        $order_id = $order->get_id();
        
        echo "<p>✅ تم إنشاء طلب اختبار: #{$order_id}</p>";
        
        // Create confirmed assignment
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'order_id' => $order_id,
                'agent_id' => $test_agent->id,
                'confirmation_status' => 'confirmed',
                'assigned_at' => current_time('mysql'),
                'confirmed_at' => current_time('mysql'),
                'assignment_method' => 'manual',
                'archived' => 0,
                'notes' => 'طلب اختبار لنظام التوصيل الجديد'
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%d', '%s')
        );
        
        if ($result) {
            echo "<p>✅ تم إنشاء تخصيص مؤكد</p>";
            echo "<p>🎯 الطلب جاهز الآن لاختبار نظام التوصيل الجديد</p>";
            echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 عرض في صفحة متابعة التوصيل</a></p>";
        } else {
            echo "<p>❌ فشل في إنشاء التخصيص</p>";
        }
        
        echo "<hr>";
    }
    
    if ($action === 'test_delivery_update' && isset($_GET['order_id'])) {
        $order_id = intval($_GET['order_id']);
        
        echo "<h2>🎯 اختبار تحديث حالة التوصيل:</h2>";
        
        // Test delivery status update
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_delivery_tracking',
            array(
                'order_id' => $order_id,
                'agent_id' => $test_agent->id,
                'status' => 'delivered',
                'notes' => 'تم التوصيل بنجاح - اختبار النظام الجديد',
                'updated_at' => current_time('mysql'),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            echo "<p>✅ تم تحديث حالة التوصيل بنجاح</p>";
            
            // Add order note
            $order = wc_get_order($order_id);
            if ($order) {
                $order->add_order_note(
                    sprintf(
                        '📦 تحديث حالة التوصيل: تم التوصيل بنجاح%s%s',
                        "\n👤 الوكيل: " . $test_agent->name,
                        "\n📝 ملاحظات: تم التوصيل بنجاح - اختبار النظام الجديد"
                    ),
                    0
                );
                echo "<p>✅ تم إضافة ملاحظة للطلب</p>";
            }
        } else {
            echo "<p>❌ فشل في تحديث حالة التوصيل: " . $wpdb->last_error . "</p>";
        }
        
        echo "<hr>";
    }
}

// Show completed orders for this agent
echo "<h2>📋 الطلبات المكتملة للوكيل:</h2>";

$completed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT DISTINCT
        oa.order_id,
        oa.confirmed_at,
        p.post_date as order_date,
        pm_total.meta_value as order_total,
        pm_billing_first.meta_value as billing_first_name,
        pm_billing_last.meta_value as billing_last_name,
        pm_billing_phone.meta_value as billing_phone,
        dt.status as delivery_status,
        dt.notes as delivery_notes,
        dt.updated_at as delivery_date
    FROM {$wpdb->prefix}hozi_order_assignments oa
    INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
    LEFT JOIN {$wpdb->prefix}postmeta pm_total ON (p.ID = pm_total.post_id AND pm_total.meta_key = '_order_total')
    LEFT JOIN {$wpdb->prefix}postmeta pm_billing_first ON (p.ID = pm_billing_first.post_id AND pm_billing_first.meta_key = '_billing_first_name')
    LEFT JOIN {$wpdb->prefix}postmeta pm_billing_last ON (p.ID = pm_billing_last.post_id AND pm_billing_last.meta_key = '_billing_last_name')
    LEFT JOIN {$wpdb->prefix}postmeta pm_billing_phone ON (p.ID = pm_billing_phone.post_id AND pm_billing_phone.meta_key = '_billing_phone')
    LEFT JOIN (
        SELECT order_id, status, notes, updated_at,
               ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY updated_at DESC) as rn
        FROM {$wpdb->prefix}hozi_delivery_tracking
    ) dt ON (oa.order_id = dt.order_id AND dt.rn = 1)
    WHERE oa.agent_id = %d
    AND oa.confirmation_status = 'confirmed'
    AND p.post_status = 'wc-completed'
    ORDER BY p.post_date DESC
    LIMIT 10",
    $test_agent->id
));

if ($completed_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>العميل</th><th>الهاتف</th><th>المبلغ</th><th>حالة التوصيل</th><th>إجراءات</th></tr>";
    
    foreach ($completed_orders as $order_data) {
        $customer_name = trim(($order_data->billing_first_name ?? '') . ' ' . ($order_data->billing_last_name ?? ''));
        $customer_phone = $order_data->billing_phone ?? '';
        $order_total = $order_data->order_total ?? '0';
        
        $status_text = $order_data->delivery_status ? $order_data->delivery_status : 'بانتظار التوصيل';
        $status_color = $order_data->delivery_status ? '#4caf50' : '#ff9800';
        
        echo "<tr>";
        echo "<td>#{$order_data->order_id}</td>";
        echo "<td>" . esc_html($customer_name ?: 'غير محدد') . "</td>";
        echo "<td>" . esc_html($customer_phone) . "</td>";
        echo "<td>" . wc_price($order_total) . "</td>";
        echo "<td style='color: {$status_color};'>{$status_text}</td>";
        echo "<td>";
        
        if (!$order_data->delivery_status) {
            echo "<a href='?action=test_delivery_update&order_id={$order_data->order_id}' style='background: #4caf50; color: white; padding: 3px 8px; text-decoration: none; border-radius: 3px;'>اختبار التوصيل</a>";
        } else {
            echo "<span style='color: green;'>✅ تم التحديث</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات مكتملة للوكيل.</p>";
}

echo "<h2>🔧 إجراءات الاختبار:</h2>";
echo "<p><a href='?action=create_test_order' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🆕 إنشاء طلب اختبار</a></p>";
echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' target='_blank' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 عرض صفحة متابعة التوصيل</a></p>";
echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل</a></p>";

echo "<h2>📊 إحصائيات النظام الجديد:</h2>";

// System stats
$total_completed = count($completed_orders);
$total_with_delivery_status = 0;

foreach ($completed_orders as $order) {
    if ($order->delivery_status) {
        $total_with_delivery_status++;
    }
}

echo "<div style='display: flex; gap: 20px;'>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; flex: 1;'>";
echo "<h4>📋 إجمالي المكتملة</h4>";
echo "<p style='font-size: 24px; margin: 0;'>{$total_completed}</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; flex: 1;'>";
echo "<h4>📦 تم تحديث التوصيل</h4>";
echo "<p style='font-size: 24px; margin: 0;'>{$total_with_delivery_status}</p>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px; flex: 1;'>";
echo "<h4>⏳ بانتظار التحديث</h4>";
echo "<p style='font-size: 24px; margin: 0;'>" . ($total_completed - $total_with_delivery_status) . "</p>";
echo "</div>";
echo "</div>";

$success_rate = $total_completed > 0 ? round(($total_with_delivery_status / $total_completed) * 100, 1) : 0;
echo "<p><strong>معدل التحديث:</strong> {$success_rate}%</p>";
?>
