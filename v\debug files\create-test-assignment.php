<?php
/**
 * Create test assignment for tracking
 * أكدلي - Akadly Plugin
 */

// WordPress environment
require_once('wp-config.php');

echo "<h1>🔧 إنشاء تخصيص تجريبي - أكدلي</h1>";

global $wpdb;

// Check if we have agents
$agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1");

if (empty($agents)) {
    echo "<p style='color: red;'>❌ لا توجد وكلاء نشطين. يرجى إنشاء وكيل أولاً.</p>";
    exit;
}

// Get recent orders
$recent_orders = $wpdb->get_results("
    SELECT p.ID, p.post_date, p.post_status 
    FROM {$wpdb->prefix}posts p 
    WHERE p.post_type = 'shop_order' 
    AND p.post_status NOT IN ('trash', 'auto-draft')
    ORDER BY p.post_date DESC 
    LIMIT 10
");

if (empty($recent_orders)) {
    echo "<p style='color: red;'>❌ لا توجد طلبات في النظام.</p>";
    exit;
}

// Handle form submission
if (isset($_POST['create_assignment'])) {
    $order_id = intval($_POST['order_id']);
    $agent_id = intval($_POST['agent_id']);
    
    if ($order_id && $agent_id) {
        // Check if assignment already exists
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
            $order_id
        ));
        
        if ($existing) {
            // Update existing assignment
            $result = $wpdb->update(
                $wpdb->prefix . 'hozi_order_assignments',
                array(
                    'agent_id' => $agent_id,
                    'confirmation_status' => 'confirmed',
                    'assigned_at' => current_time('mysql'),
                    'confirmed_at' => current_time('mysql'),
                    'archived' => 0
                ),
                array('order_id' => $order_id),
                array('%d', '%s', '%s', '%s', '%d'),
                array('%d')
            );
            
            if ($result !== false) {
                echo "<div style='background: #d1edff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h3>✅ تم تحديث التخصيص بنجاح!</h3>";
                echo "<p>تم تحديث تخصيص الطلب #{$order_id} للوكيل ID: {$agent_id}</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h3>❌ فشل في تحديث التخصيص</h3>";
                echo "<p>خطأ: " . $wpdb->last_error . "</p>";
                echo "</div>";
            }
        } else {
            // Create new assignment
            $result = $wpdb->insert(
                $wpdb->prefix . 'hozi_order_assignments',
                array(
                    'order_id' => $order_id,
                    'agent_id' => $agent_id,
                    'confirmation_status' => 'confirmed',
                    'assigned_at' => current_time('mysql'),
                    'confirmed_at' => current_time('mysql'),
                    'assignment_method' => 'manual',
                    'archived' => 0
                ),
                array('%d', '%d', '%s', '%s', '%s', '%s', '%d')
            );
            
            if ($result) {
                echo "<div style='background: #d1edff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h3>✅ تم إنشاء التخصيص بنجاح!</h3>";
                echo "<p>تم تخصيص الطلب #{$order_id} للوكيل ID: {$agent_id}</p>";
                echo "<p>ID التخصيص الجديد: " . $wpdb->insert_id . "</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h3>❌ فشل في إنشاء التخصيص</h3>";
                echo "<p>خطأ: " . $wpdb->last_error . "</p>";
                echo "</div>";
            }
        }
    }
}

echo "<h2>📋 إنشاء تخصيص تجريبي:</h2>";

echo "<form method='post' style='background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd;'>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label for='order_id'><strong>اختر الطلب:</strong></label><br>";
echo "<select name='order_id' id='order_id' style='width: 100%; padding: 8px; margin-top: 5px;' required>";
echo "<option value=''>-- اختر طلب --</option>";
foreach ($recent_orders as $order) {
    // Check if already assigned
    $assigned = $wpdb->get_var($wpdb->prepare(
        "SELECT agent_id FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
        $order->ID
    ));
    
    $status_text = $assigned ? " (مخصص للوكيل #{$assigned})" : " (غير مخصص)";
    echo "<option value='{$order->ID}'>#{$order->ID} - " . date('Y/m/d', strtotime($order->post_date)) . " - {$order->post_status}{$status_text}</option>";
}
echo "</select>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label for='agent_id'><strong>اختر الوكيل:</strong></label><br>";
echo "<select name='agent_id' id='agent_id' style='width: 100%; padding: 8px; margin-top: 5px;' required>";
echo "<option value=''>-- اختر وكيل --</option>";
foreach ($agents as $agent) {
    echo "<option value='{$agent->id}'>{$agent->name} (ID: {$agent->id})</option>";
}
echo "</select>";
echo "</div>";

echo "<button type='submit' name='create_assignment' style='background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>إنشاء/تحديث التخصيص</button>";

echo "</form>";

echo "<h2>📊 التخصيصات الحالية:</h2>";

$current_assignments = $wpdb->get_results("
    SELECT 
        oa.id,
        oa.order_id,
        oa.agent_id,
        oa.confirmation_status,
        oa.assigned_at,
        oa.confirmed_at,
        oa.archived,
        a.name as agent_name
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    ORDER BY oa.assigned_at DESC
    LIMIT 10
");

if (!empty($current_assignments)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 13px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>رقم الطلب</th><th>الوكيل</th><th>حالة التأكيد</th><th>تاريخ التخصيص</th><th>تاريخ التأكيد</th><th>مؤرشف؟</th></tr>";
    
    foreach ($current_assignments as $assignment) {
        $archived_status = $assignment->archived ? 'نعم' : 'لا';
        $confirmed_date = $assignment->confirmed_at ? date('Y/m/d H:i', strtotime($assignment->confirmed_at)) : 'لم يؤكد';
        
        echo "<tr>";
        echo "<td>{$assignment->id}</td>";
        echo "<td>#{$assignment->order_id}</td>";
        echo "<td>{$assignment->agent_name}</td>";
        echo "<td>{$assignment->confirmation_status}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($assignment->assigned_at)) . "</td>";
        echo "<td>{$confirmed_date}</td>";
        echo "<td>{$archived_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>📝 لا توجد تخصيصات حالياً.</p>";
}

echo "<h2>🔗 روابط مفيدة:</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-my-tracking') . "' style='background: #2196f3; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔗 صفحة التتبع</a></p>";
echo "<p><a href='test-tracking-page.php' style='background: #4caf50; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🧪 اختبار التتبع</a></p>";
echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-agents') . "' style='background: #ff9800; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>👥 إدارة الوكلاء</a></p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;'>";
echo "<h3>💡 ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>هذه الأداة تنشئ تخصيصات تجريبية للاختبار</li>";
echo "<li>التخصيصات تُنشأ بحالة 'confirmed' مباشرة</li>";
echo "<li>يمكن تحديث التخصيصات الموجودة</li>";
echo "<li>تأكد من وجود وكلاء نشطين قبل الإنشاء</li>";
echo "</ul>";
echo "</div>";
?>
