<?php
/**
 * Agent Archived Orders View - Lightweight List Format
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check if user is an agent
$agent_manager = new Hozi_Akadly_Agent_Manager();
if (!$agent_manager->is_agent()) {
    wp_die(__('غير مصرح لك بالوصول لهذه الصفحة', 'hozi-akadly'));
}

$current_agent = $agent_manager->get_current_agent();
if (!$current_agent) {
    wp_die(__('لم يتم العثور على بيانات الوكيل', 'hozi-akadly'));
}

// Get pagination parameters
$page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20; // Show 20 orders per page
$offset = ($page - 1) * $per_page;

// Get archived orders using notes field
global $wpdb;
$archived_orders = $wpdb->get_results($wpdb->prepare("
    SELECT
        a.order_id,
        a.confirmation_status,
        a.confirmed_at,
        a.notes,
        p.post_date as order_date,
        t.status as tracking_status,
        t.notes as tracking_reason,
        t.updated_at as tracking_updated
    FROM {$wpdb->prefix}hozi_order_assignments a
    LEFT JOIN {$wpdb->posts} p ON a.order_id = p.ID
    LEFT JOIN {$wpdb->prefix}hozi_order_tracking t ON a.order_id = t.order_id
    WHERE a.agent_id = %d
    AND a.notes LIKE 'ARCHIVED:%%'
    ORDER BY a.confirmed_at DESC
    LIMIT %d OFFSET %d
", $current_agent->id, $per_page, $offset));

// Get total count for pagination
$total_archived = $wpdb->get_var($wpdb->prepare("
    SELECT COUNT(*)
    FROM {$wpdb->prefix}hozi_order_assignments a
    WHERE a.agent_id = %d
    AND a.notes LIKE 'ARCHIVED:%%'
", $current_agent->id));

$total_pages = ceil($total_archived / $per_page);

// Get archive statistics
$stats = $wpdb->get_row($wpdb->prepare("
    SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN MONTH(a.confirmed_at) = MONTH(CURRENT_DATE())
                   AND YEAR(a.confirmed_at) = YEAR(CURRENT_DATE()) THEN 1 END) as this_month,
        COUNT(CASE WHEN WEEK(a.confirmed_at) = WEEK(CURRENT_DATE())
                   AND YEAR(a.confirmed_at) = YEAR(CURRENT_DATE()) THEN 1 END) as this_week
    FROM {$wpdb->prefix}hozi_order_assignments a
    WHERE a.agent_id = %d
    AND a.notes LIKE 'ARCHIVED:%%'
", $current_agent->id));
?>

<div class="wrap hozi-archived-page">
    <h1 class="wp-heading-inline">
        🗂️ الطلبات المؤرشفة
        <span class="hozi-agent-name"><?php echo esc_html($current_agent->name); ?></span>
    </h1>

    <!-- Navigation Tabs - 3 Steps Only -->
    <div class="hozi-nav-tabs">
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-orders'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-cart"></span>
            <span class="hozi-step-number">1</span>
            <?php _e('الطلبات المخصصة لي', 'hozi-akadly'); ?>
        </a>
        <?php if (Hozi_Akadly_Delivery_Tracking_Permissions::should_show_delivery_tracking_menu()): ?>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-delivery-tracking'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-truck"></span>
            <span class="hozi-step-number">2</span>
            <?php _e('متابعة التوصيل', 'hozi-akadly'); ?>
        </a>
        <?php else: ?>
        <span class="hozi-nav-tab hozi-nav-tab-disabled" title="<?php echo esc_attr(Hozi_Akadly_Delivery_Tracking_Permissions::get_access_denied_message()); ?>">
            <span class="dashicons dashicons-truck"></span>
            <span class="hozi-step-number">2</span>
            <?php _e('متابعة التوصيل', 'hozi-akadly'); ?>
            <span class="hozi-disabled-badge">معطل</span>
        </span>
        <?php endif; ?>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-archived'); ?>" class="hozi-nav-tab hozi-nav-tab-active">
            <span class="dashicons dashicons-archive"></span>
            <span class="hozi-step-number">3</span>
            <?php _e('الطلبات المؤرشفة', 'hozi-akadly'); ?>
        </a>
    </div>

    <!-- Quick Stats -->
    <div class="hozi-quick-stats">
        <div class="hozi-stat-item">
            <span class="hozi-stat-number"><?php echo $stats->total ?: 0; ?></span>
            <span class="hozi-stat-label">إجمالي المؤرشف</span>
        </div>
        <div class="hozi-stat-item">
            <span class="hozi-stat-number"><?php echo $stats->this_month ?: 0; ?></span>
            <span class="hozi-stat-label">هذا الشهر</span>
        </div>
        <div class="hozi-stat-item">
            <span class="hozi-stat-number"><?php echo $stats->this_week ?: 0; ?></span>
            <span class="hozi-stat-label">هذا الأسبوع</span>
        </div>
    </div>



    <?php if (empty($archived_orders)): ?>
        <div class="hozi-empty-state">
            <div class="hozi-empty-icon">🗂️</div>
            <h3>لا توجد طلبات مؤرشفة</h3>
            <p>لم يتم أرشفة أي طلبات بعد. الطلبات المؤرشفة ستظهر هنا.</p>
            <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-orders'); ?>" class="button button-primary">
                ابدأ بتأكيد الطلبات
            </a>
        </div>
    <?php else: ?>
        <!-- Archived Orders List -->
        <div class="hozi-archived-list">
            <div class="hozi-list-header">
                <span class="hozi-col-order">الطلب</span>
                <span class="hozi-col-status">الحالة</span>
                <span class="hozi-col-date">تاريخ الأرشفة</span>
                <span class="hozi-col-reason">السبب</span>
                <span class="hozi-col-actions">الإجراءات</span>
            </div>

            <?php foreach ($archived_orders as $archived_order):
                $order = wc_get_order($archived_order->order_id);
                if (!$order) continue;

                $customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
                $order_total = $order->get_total();

                // Extract archive info from notes: ARCHIVED:delivered:2025-01-29 10:30:00
                $archive_parts = explode(':', $archived_order->notes);
                $archive_status = isset($archive_parts[1]) ? $archive_parts[1] : 'completed';
                $archive_date = isset($archive_parts[2]) ? $archive_parts[2] : $archived_order->confirmed_at;

                $status_label = $archived_order->tracking_status ?: $archive_status;
                $reason = $archived_order->tracking_reason ?: 'تم إكمال الطلب';
            ?>
                <div class="hozi-list-row">
                    <div class="hozi-col-order">
                        <strong>#<?php echo $archived_order->order_id; ?></strong>
                        <div class="hozi-order-meta">
                            <?php echo esc_html($customer_name); ?> -
                            <?php echo wc_price($order_total); ?>
                        </div>
                    </div>

                    <div class="hozi-col-status">
                        <span class="hozi-status-badge hozi-status-<?php echo esc_attr($status_label); ?>">
                            <?php
                            switch($status_label) {
                                case 'delivered': echo '✅ تم التوصيل'; break;
                                case 'rejected': echo '❌ مرفوض'; break;
                                case 'postponed': echo '⏳ مؤجل'; break;
                                default: echo '✅ مكتمل'; break;
                            }
                            ?>
                        </span>
                    </div>

                    <div class="hozi-col-date">
                        <?php echo date('Y/m/d H:i', strtotime($archive_date)); ?>
                    </div>

                    <div class="hozi-col-reason">
                        <?php echo esc_html(wp_trim_words($reason, 5)); ?>
                    </div>

                    <div class="hozi-col-actions">
                        <a href="<?php echo $order->get_edit_order_url(); ?>"
                           class="button button-small" target="_blank" title="عرض تفاصيل الطلب">
                            👁️ عرض
                        </a>
                        <?php if ($order->get_billing_phone()): ?>
                            <a href="tel:<?php echo esc_attr($order->get_billing_phone()); ?>"
                               class="button button-small" title="اتصال بالعميل"
                               style="background: #28a745; border-color: #28a745; color: white; margin-right: 5px;">
                                📞
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="hozi-pagination">
                <?php
                $pagination_args = array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'current' => $page,
                    'total' => $total_pages,
                    'prev_text' => '← السابق',
                    'next_text' => 'التالي →',
                );
                echo paginate_links($pagination_args);
                ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.hozi-archived-page {
    max-width: 1200px;
    margin: 0 auto;
}

/* Navigation Tabs */
.hozi-nav-tabs {
    display: flex;
    gap: 0;
    margin: 20px 0;
    border-bottom: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    text-decoration: none;
    color: #666;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    transition: all 0.3s ease;
    font-weight: 500;
    flex: 1;
    justify-content: center;
    flex-direction: column;
}

.hozi-step-number {
    background: #0073aa;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}

.hozi-nav-tab:last-child {
    border-right: none;
}

.hozi-nav-tab:hover {
    background: #e9ecef;
    color: #333;
    text-decoration: none;
}

.hozi-nav-tab-active {
    background: #0073aa !important;
    color: white !important;
    font-weight: 600;
}

.hozi-nav-tab-active .hozi-step-number {
    background: white;
    color: #0073aa;
}

.hozi-nav-tab-active:hover {
    background: #005a87 !important;
    color: white !important;
}

.hozi-archived-page h1 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.hozi-agent-name {
    background: #0073aa;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: normal;
}

.hozi-quick-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.hozi-stat-item {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px 20px;
    text-align: center;
    min-width: 120px;
}

.hozi-stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
}

.hozi-stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.hozi-page-nav {
    margin-bottom: 20px;
}

.hozi-page-nav .button {
    margin-left: 10px;
}

.hozi-empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.hozi-empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.hozi-empty-state h3 {
    color: #666;
    margin-bottom: 10px;
}

.hozi-empty-state p {
    color: #999;
    margin-bottom: 20px;
}

.hozi-archived-list {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.hozi-list-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 2fr 1fr;
    gap: 15px;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
    color: #333;
}

.hozi-list-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 2fr 1fr;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    align-items: center;
}

.hozi-list-row:last-child {
    border-bottom: none;
}

.hozi-list-row:hover {
    background: #f8f9fa;
}

.hozi-order-meta {
    font-size: 12px;
    color: #666;
    margin-top: 3px;
}

.hozi-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    background: #28a745;
    color: white;
}

.hozi-status-rejected {
    background: #dc3545;
}

.hozi-status-postponed {
    background: #ffc107;
    color: #333;
}

.hozi-pagination {
    margin-top: 20px;
    text-align: center;
}

.hozi-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #0073aa;
}

.hozi-pagination .page-numbers.current {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.hozi-pagination .page-numbers:hover {
    background: #f8f9fa;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hozi-quick-stats {
        justify-content: center;
    }

    .hozi-stat-item {
        min-width: 100px;
        padding: 10px 15px;
    }

    .hozi-list-header,
    .hozi-list-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .hozi-list-header {
        display: none; /* Hide header on mobile */
    }

    .hozi-list-row {
        padding: 15px;
        border: 1px solid #eee;
        margin-bottom: 10px;
        border-radius: 8px;
    }

    .hozi-col-order,
    .hozi-col-status,
    .hozi-col-date,
    .hozi-col-reason,
    .hozi-col-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .hozi-col-order::before { content: "الطلب: "; font-weight: bold; }
    .hozi-col-status::before { content: "الحالة: "; font-weight: bold; }
    .hozi-col-date::before { content: "التاريخ: "; font-weight: bold; }
    .hozi-col-reason::before { content: "السبب: "; font-weight: bold; }
    .hozi-col-actions::before { content: "الإجراءات: "; font-weight: bold; }

    .hozi-col-actions .button {
        margin: 2px;
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* Disabled Tab Styles */
.hozi-nav-tab-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    position: relative;
    background: #f5f5f5 !important;
    color: #999 !important;
    border-color: #ddd !important;
}

.hozi-nav-tab-disabled:hover {
    background: #f5f5f5 !important;
    color: #999 !important;
    transform: none !important;
}

.hozi-disabled-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    font-size: 9px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

@media (max-width: 768px) {
    .hozi-disabled-badge {
        font-size: 8px;
        padding: 1px 3px;
    }
}
</style>
