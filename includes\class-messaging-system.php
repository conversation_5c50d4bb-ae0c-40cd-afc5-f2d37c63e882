<?php
/**
 * Messaging System for Hozi Akadly
 * 
 * Handles communication between managers and agents
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Messaging_System {
    
    /**
     * Initialize the messaging system
     */
    public function __construct() {
        add_action('wp_ajax_hozi_send_message', array($this, 'ajax_send_message'));
        add_action('wp_ajax_hozi_get_messages', array($this, 'ajax_get_messages'));
        add_action('wp_ajax_hozi_mark_message_read', array($this, 'ajax_mark_message_read'));
        add_action('wp_ajax_hozi_get_unread_count', array($this, 'ajax_get_unread_count'));
        add_action('wp_ajax_hozi_delete_message', array($this, 'ajax_delete_message'));
        add_action('wp_ajax_hozi_get_message_thread', array($this, 'ajax_get_message_thread'));
        
        // Auto-messages hooks
        add_action('woocommerce_product_set_stock_status', array($this, 'auto_message_stock_status'), 10, 3);
        add_action('woocommerce_variation_set_stock_status', array($this, 'auto_message_stock_status'), 10, 3);
    }
    
    /**
     * Create messages table
     */
    public static function create_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'hozi_messages';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            sender_id int(11) NOT NULL,
            recipient_id int(11) DEFAULT NULL,
            recipient_type enum('agent', 'manager', 'all') DEFAULT 'agent',
            message_type enum('general', 'urgent', 'stock', 'order', 'product') DEFAULT 'general',
            subject varchar(255) NOT NULL,
            message text NOT NULL,
            priority enum('normal', 'high', 'urgent') DEFAULT 'normal',
            category varchar(50) DEFAULT 'general',
            related_order_id bigint(20) DEFAULT NULL,
            related_product_id bigint(20) DEFAULT NULL,
            attachments text DEFAULT NULL,
            is_read tinyint(1) DEFAULT 0,
            is_deleted tinyint(1) DEFAULT 0,
            requires_response tinyint(1) DEFAULT 0,
            parent_message_id int(11) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            read_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY sender_id (sender_id),
            KEY recipient_id (recipient_id),
            KEY message_type (message_type),
            KEY priority (priority),
            KEY is_read (is_read),
            KEY created_at (created_at),
            KEY parent_message_id (parent_message_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Send a message
     */
    public function send_message($data) {
        global $wpdb;
        
        $defaults = array(
            'sender_id' => get_current_user_id(),
            'recipient_id' => null,
            'recipient_type' => 'agent',
            'message_type' => 'general',
            'subject' => '',
            'message' => '',
            'priority' => 'normal',
            'category' => 'general',
            'related_order_id' => null,
            'related_product_id' => null,
            'attachments' => null,
            'requires_response' => 0,
            'parent_message_id' => null
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Validate required fields
        if (empty($data['message'])) {
            return new WP_Error('empty_message', 'الرسالة مطلوبة');
        }
        
        // Insert message
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_messages',
            array(
                'sender_id' => $data['sender_id'],
                'recipient_id' => $data['recipient_id'],
                'recipient_type' => $data['recipient_type'],
                'message_type' => $data['message_type'],
                'subject' => $data['subject'],
                'message' => $data['message'],
                'priority' => $data['priority'],
                'category' => $data['category'],
                'related_order_id' => $data['related_order_id'],
                'related_product_id' => $data['related_product_id'],
                'attachments' => $data['attachments'],
                'requires_response' => $data['requires_response'],
                'parent_message_id' => $data['parent_message_id'],
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%d', '%d', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', 'فشل في إرسال الرسالة');
        }
        
        $message_id = $wpdb->insert_id;
        
        // Send notifications
        $this->send_message_notification($message_id);
        
        return $message_id;
    }
    
    /**
     * Get messages for user
     */
    public function get_messages($user_id, $args = array()) {
        global $wpdb;
        
        $defaults = array(
            'limit' => 50,
            'offset' => 0,
            'unread_only' => false,
            'message_type' => null,
            'category' => null,
            'search' => null,
            'order_by' => 'created_at',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        // Build query
        $where_conditions = array();
        $where_values = array();
        
        // User can see messages sent to them or broadcast messages
        $where_conditions[] = "(recipient_id = %d OR recipient_type = 'all' OR sender_id = %d)";
        $where_values[] = $user_id;
        $where_values[] = $user_id;
        
        // Not deleted
        $where_conditions[] = "is_deleted = 0";
        
        // Unread only
        if ($args['unread_only']) {
            $where_conditions[] = "is_read = 0";
        }
        
        // Message type filter
        if ($args['message_type']) {
            $where_conditions[] = "message_type = %s";
            $where_values[] = $args['message_type'];
        }
        
        // Category filter
        if ($args['category']) {
            $where_conditions[] = "category = %s";
            $where_values[] = $args['category'];
        }
        
        // Search
        if ($args['search']) {
            $where_conditions[] = "(subject LIKE %s OR message LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $query = "SELECT m.*, 
                         u1.display_name as sender_name,
                         u2.display_name as recipient_name
                  FROM {$wpdb->prefix}hozi_messages m
                  LEFT JOIN {$wpdb->users} u1 ON m.sender_id = u1.ID
                  LEFT JOIN {$wpdb->users} u2 ON m.recipient_id = u2.ID
                  WHERE {$where_clause}
                  ORDER BY {$args['order_by']} {$args['order']}
                  LIMIT %d OFFSET %d";
        
        $where_values[] = $args['limit'];
        $where_values[] = $args['offset'];
        
        $prepared_query = $wpdb->prepare($query, $where_values);
        
        return $wpdb->get_results($prepared_query);
    }
    
    /**
     * Get unread message count
     */
    public function get_unread_count($user_id) {
        global $wpdb;
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_messages 
             WHERE (recipient_id = %d OR recipient_type = 'all') 
             AND is_read = 0 
             AND is_deleted = 0
             AND sender_id != %d",
            $user_id, $user_id
        ));
    }
    
    /**
     * Mark message as read
     */
    public function mark_as_read($message_id, $user_id) {
        global $wpdb;
        
        return $wpdb->update(
            $wpdb->prefix . 'hozi_messages',
            array(
                'is_read' => 1,
                'read_at' => current_time('mysql')
            ),
            array(
                'id' => $message_id,
                'recipient_id' => $user_id
            ),
            array('%d', '%s'),
            array('%d', '%d')
        );
    }
    
    /**
     * Delete message
     */
    public function delete_message($message_id, $user_id) {
        global $wpdb;
        
        return $wpdb->update(
            $wpdb->prefix . 'hozi_messages',
            array('is_deleted' => 1),
            array(
                'id' => $message_id,
                'recipient_id' => $user_id
            ),
            array('%d'),
            array('%d', '%d')
        );
    }
    
    /**
     * Send message notification
     */
    private function send_message_notification($message_id) {
        global $wpdb;
        
        $message = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_messages WHERE id = %d",
            $message_id
        ));
        
        if (!$message) {
            return;
        }
        
        // Send real-time notification via WebSocket or AJAX polling
        // This will be handled by the frontend JavaScript
        
        // For now, we'll use WordPress transients for real-time updates
        if ($message->recipient_type === 'all') {
            set_transient('hozi_new_broadcast_message', $message_id, 300); // 5 minutes
        } else {
            set_transient('hozi_new_message_' . $message->recipient_id, $message_id, 300);
        }
    }
    
    /**
     * Auto-send stock status messages
     */
    public function auto_message_stock_status($product_id, $stock_status, $product) {
        if ($stock_status === 'outofstock') {
            $product_name = $product->get_name();
            
            $this->send_message(array(
                'sender_id' => 1, // System user
                'recipient_type' => 'all',
                'message_type' => 'stock',
                'subject' => 'نفاد المخزون - ' . $product_name,
                'message' => "تنبيه: نفد مخزون المنتج '{$product_name}'. يرجى إبلاغ العملاء بعدم توفر هذا المنتج حالياً.",
                'priority' => 'high',
                'category' => 'stock',
                'related_product_id' => $product_id,
                'requires_response' => 0
            ));
        }
    }
    
    /**
     * AJAX: Send message
     */
    public function ajax_send_message() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');

        // Allow both admins and agents to send messages
        if (!current_user_can('edit_shop_orders') && !current_user_can('hozi_view_assigned_orders')) {
            wp_send_json_error('غير مصرح');
        }

        // If agent is sending message, default to manager
        $current_user_id = get_current_user_id();
        $is_agent = current_user_can('hozi_view_assigned_orders') && !current_user_can('manage_options');

        $recipient_id = intval($_POST['recipient_id'] ?? 0);
        $recipient_type = sanitize_text_field($_POST['recipient_type'] ?? 'agent');

        // If agent and no specific recipient, send to admin
        if ($is_agent && !$recipient_id) {
            $admins = get_users(array('role' => 'administrator', 'number' => 1));
            if (!empty($admins)) {
                $recipient_id = $admins[0]->ID;
                $recipient_type = 'manager';
            }
        }

        $data = array(
            'recipient_id' => $recipient_id,
            'recipient_type' => $recipient_type,
            'message_type' => sanitize_text_field($_POST['message_type'] ?? 'general'),
            'subject' => sanitize_text_field($_POST['subject'] ?? ''),
            'message' => sanitize_textarea_field($_POST['message'] ?? ''),
            'priority' => sanitize_text_field($_POST['priority'] ?? 'normal'),
            'category' => sanitize_text_field($_POST['category'] ?? 'general'),
            'related_order_id' => intval($_POST['related_order_id'] ?? 0) ?: null,
            'related_product_id' => intval($_POST['related_product_id'] ?? 0) ?: null,
            'requires_response' => intval($_POST['requires_response'] ?? 0),
            'parent_message_id' => intval($_POST['parent_message_id'] ?? 0) ?: null
        );

        $result = $this->send_message($data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success(array(
            'message_id' => $result,
            'message' => 'تم إرسال الرسالة بنجاح'
        ));
    }

    /**
     * AJAX: Get messages
     */
    public function ajax_get_messages() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');

        $user_id = get_current_user_id();
        $args = array(
            'limit' => intval($_POST['limit'] ?? 20),
            'offset' => intval($_POST['offset'] ?? 0),
            'unread_only' => !empty($_POST['unread_only']),
            'message_type' => sanitize_text_field($_POST['message_type'] ?? ''),
            'category' => sanitize_text_field($_POST['category'] ?? ''),
            'search' => sanitize_text_field($_POST['search'] ?? '')
        );

        $messages = $this->get_messages($user_id, $args);

        wp_send_json_success($messages);
    }

    /**
     * AJAX: Mark message as read
     */
    public function ajax_mark_message_read() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');

        $message_id = intval($_POST['message_id']);
        $user_id = get_current_user_id();

        $result = $this->mark_as_read($message_id, $user_id);

        if ($result === false) {
            wp_send_json_error('فشل في تحديث حالة القراءة');
        }

        wp_send_json_success('تم تحديث حالة القراءة');
    }

    /**
     * AJAX: Get unread count
     */
    public function ajax_get_unread_count() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');

        $user_id = get_current_user_id();
        $count = $this->get_unread_count($user_id);

        wp_send_json_success(array('count' => $count));
    }

    /**
     * AJAX: Delete message
     */
    public function ajax_delete_message() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');

        $message_id = intval($_POST['message_id']);
        $user_id = get_current_user_id();

        $result = $this->delete_message($message_id, $user_id);

        if ($result === false) {
            wp_send_json_error('فشل في حذف الرسالة');
        }

        wp_send_json_success('تم حذف الرسالة');
    }

    /**
     * AJAX: Get message thread
     */
    public function ajax_get_message_thread() {
        check_ajax_referer('hozi_messaging_nonce', 'nonce');

        $message_id = intval($_POST['message_id']);
        $user_id = get_current_user_id();

        global $wpdb;

        // Get the original message
        $original_message = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_messages WHERE id = %d",
            $message_id
        ));

        if (!$original_message) {
            wp_send_json_error('الرسالة غير موجودة');
        }

        // Get thread messages
        $thread_messages = $wpdb->get_results($wpdb->prepare(
            "SELECT m.*, u.display_name as sender_name
             FROM {$wpdb->prefix}hozi_messages m
             LEFT JOIN {$wpdb->users} u ON m.sender_id = u.ID
             WHERE (m.id = %d OR m.parent_message_id = %d)
             AND m.is_deleted = 0
             ORDER BY m.created_at ASC",
            $message_id, $message_id
        ));

        wp_send_json_success($thread_messages);
    }
}
