<?php
/**
 * License Manager for Hozi Akadly
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_License_Manager {

    private static $instance = null;
    private $item_name = 'akadly';
    private $item_id = false;
    private $item_shortname = 'akadly';
    private $version;
    private $author = 'Hostazi';
    private $api_url = 'https://hostazi.shop';
    private $license_key;
    private $license_status;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->version = HOZI_AKADLY_VERSION;

        // Try new format first, then fallback to old format for backward compatibility
        $this->license_key = trim(get_option($this->item_shortname . '_license_key', ''));
        if (empty($this->license_key)) {
            $this->license_key = trim(get_option('hozi_akadly_license_key', ''));
        }

        $this->license_status = get_option($this->item_shortname . '_license_status', false);
        if ($this->license_status === false) {
            $this->license_status = get_option('hozi_akadly_license_status', false);
        }

        // Migrate old license data to new format
        $this->migrate_license_data();

        // Admin hooks
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_notices', array($this, 'admin_notices'));
        add_action('admin_notices', array($this, 'persistent_license_notice')); // Always show license notice
        // License activation/deactivation hooks
        add_action('admin_init', array($this, 'activate_license'));
        add_action('admin_init', array($this, 'deactivate_license'));
        add_action('admin_init', array($this, 'check_license'));

        // Plugin updater
        add_action('admin_init', array($this, 'plugin_updater'), 0);

        // License check hooks
        add_action('init', array($this, 'schedule_license_check'));
        add_action('hozi_akadly_daily_license_check', array($this, 'daily_license_check'));

        // Check license on admin pages
        add_action('admin_init', array($this, 'maybe_check_license'));

        // Block functionality if not licensed
        add_action('plugins_loaded', array($this, 'check_license_status'), 1);

        // Plugin row actions
        add_filter('plugin_action_links_' . plugin_basename(HOZI_AKADLY_PLUGIN_FILE), array($this, 'plugin_action_links'));
        add_action('after_plugin_row_' . plugin_basename(HOZI_AKADLY_PLUGIN_FILE), array($this, 'plugin_row_notice'), 10, 2);
    }

    /**
     * Migrate old license data to new format
     */
    private function migrate_license_data() {
        // Check if migration is needed
        $migration_done = get_option('akadly_license_migration_done', false);
        if ($migration_done) {
            return;
        }

        // Get old data
        $old_license_key = get_option('hozi_akadly_license_key');
        $old_license_status = get_option('hozi_akadly_license_status');
        $old_license_expires = get_option('hozi_akadly_license_expires');

        // Migrate to new format if old data exists
        if (!empty($old_license_key)) {
            update_option($this->item_shortname . '_license_key', $old_license_key);
        }
        if ($old_license_status !== false) {
            update_option($this->item_shortname . '_license_status', $old_license_status);
        }
        if (!empty($old_license_expires)) {
            update_option($this->item_shortname . '_license_expires', $old_license_expires);
        }

        // Mark migration as done
        update_option('akadly_license_migration_done', true);
    }

    /**
     * Initialize admin
     */
    public function admin_init() {
        // Register settings
        register_setting($this->item_shortname . '_license', $this->item_shortname . '_license_key', array($this, 'sanitize_license'));
    }

    /**
     * Sanitize license key
     */
    public function sanitize_license($new) {
        $old = get_option($this->item_shortname . '_license_key');
        if ($old && $old != $new) {
            delete_option($this->item_shortname . '_license_status');
        }
        return $new;
    }

    /**
     * Activate license
     */
    public function activate_license() {
        // Check if this is a license activation request
        $has_license_key = isset($_POST[$this->item_shortname . '_license_key']) || isset($_POST['hozi_akadly_license_key']);
        $has_nonce = isset($_POST[$this->item_shortname . '_license_nonce']) || isset($_POST['hozi_akadly_license_nonce']);
        $has_activate_button = isset($_POST['edd_license_activate']);

        // If we have license key and nonce but no activate button, it's likely a form submission issue
        if (!$has_activate_button && !$has_license_key) {
            return;
        }

        // Check nonce with fallback
        $nonce_field = $this->item_shortname . '_license_nonce';
        if (!isset($_POST[$nonce_field]) || !wp_verify_nonce($_POST[$nonce_field], $nonce_field)) {
            // Try alternative nonce check
            if (!isset($_POST['hozi_akadly_license_nonce']) || !wp_verify_nonce($_POST['hozi_akadly_license_nonce'], 'hozi_akadly_license_nonce')) {
                wp_die(__('فشل التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'hozi-akadly'));
            }
        }

        $license_field1 = $this->item_shortname . '_license_key';
        $license_field2 = 'hozi_akadly_license_key'; // Keep backward compatibility
        $license = trim($_POST[$license_field1] ?? $_POST[$license_field2] ?? '');

        if (empty($license)) {
            $message = __('يرجى إدخال مفتاح الترخيص.', 'hozi-akadly');
            $base_url = admin_url('admin.php?page=hozi-akadly-license');
            $redirect = add_query_arg(array('sl_activation' => 'false', 'message' => urlencode($message)), $base_url);
            wp_redirect($redirect);
            exit();
        }



        $api_params = array(
            'edd_action' => 'activate_license',
            'license' => $license,
            'item_name' => urlencode($this->item_name),
            'url' => home_url()
        );

        $response = wp_remote_post($this->api_url . '/edd-sl-api/', array(
            'timeout' => 15,
            'sslverify' => false,
            'body' => $api_params
        ));

        if (is_wp_error($response) || 200 !== wp_remote_retrieve_response_code($response)) {
            $error_message = '';
            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
            } else {
                $error_message = 'HTTP ' . wp_remote_retrieve_response_code($response);
            }
            $message = sprintf(__('خطأ في الاتصال بسيرفر التفعيل: %s', 'hozi-akadly'), $error_message);
        } else {
            $response_body = wp_remote_retrieve_body($response);
            $license_data = json_decode($response_body);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $message = __('خطأ في تحليل استجابة السيرفر.', 'hozi-akadly');
            } elseif (!isset($license_data->success)) {
                $message = __('استجابة غير صحيحة من السيرفر.', 'hozi-akadly');
            } elseif (false === $license_data->success || $license_data->success === 'false') {
                switch ($license_data->error) {
                    case 'expired':
                        $message = sprintf(
                            __('انتهت صلاحية الترخيص في %s.', 'hozi-akadly'),
                            date_i18n(get_option('date_format'), strtotime($license_data->expires, current_time('timestamp')))
                        );
                        break;

                    case 'disabled':
                    case 'revoked':
                        $message = __('تم إلغاء الترخيص.', 'hozi-akadly');
                        break;

                    case 'missing':
                        $message = __('مفتاح الترخيص غير موجود أو غير صحيح. تأكد من إدخال المفتاح الصحيح.', 'hozi-akadly');
                        break;

                    case 'invalid':
                    case 'site_inactive':
                        $message = __('الترخيص غير نشط لهذا الموقع.', 'hozi-akadly');
                        break;

                    case 'item_name_mismatch':
                        $message = sprintf(__('هذا الترخيص ليس لـ %s.', 'hozi-akadly'), $this->item_name);
                        break;

                    case 'no_activations_left':
                        $message = __('تم استنفاد عدد مرات التفعيل المسموحة لهذا الترخيص.', 'hozi-akadly');
                        break;

                    default:
                        $message = __('حدث خطأ، يرجى المحاولة مرة أخرى.', 'hozi-akadly');
                        break;
                }
            } else {
                // Success case
                $message = ''; // No error message
            }
        }

        if (!empty($message)) {
            $base_url = admin_url('admin.php?page=hozi-akadly-license');
            $redirect = add_query_arg(array('sl_activation' => 'false', 'message' => urlencode($message)), $base_url);
            wp_redirect($redirect);
            exit();
        }

        // Check if we have valid license data before saving
        if (isset($license_data->license)) {
            update_option($this->item_shortname . '_license_key', $license);
            update_option($this->item_shortname . '_license_status', $license_data->license);

            if (isset($license_data->expires)) {
                update_option($this->item_shortname . '_license_expires', $license_data->expires);
            }
        } else {
            return;
        }

        // Update the instance variables
        $this->license_key = $license;
        $this->license_status = $license_data->license;

        // Force update the filter immediately
        remove_filter('hozi_akadly_license_valid', '__return_false');
        add_filter('hozi_akadly_license_valid', '__return_true');

        // Set a flag for successful activation
        set_transient('hozi_akadly_license_activated', true, 30);

        // Clear license check cache
        delete_transient('hozi_akadly_last_license_check');

        // Force update global filters
        global $wp_filter;
        if (isset($wp_filter['hozi_akadly_license_valid'])) {
            unset($wp_filter['hozi_akadly_license_valid']);
        }
        add_filter('hozi_akadly_license_valid', '__return_true', 10);

        // Don't redirect here - let the admin class handle it
        return;
    }

    /**
     * Deactivate license
     */
    public function deactivate_license() {
        if (!isset($_POST['edd_license_deactivate'])) {
            return;
        }

        if (!check_admin_referer($this->item_shortname . '_license_nonce', $this->item_shortname . '_license_nonce')) {
            return;
        }

        $license = trim($_POST[$this->item_shortname . '_license_key']);

        $api_params = array(
            'edd_action' => 'deactivate_license',
            'license' => $license,
            'item_name' => urlencode($this->item_name),
            'url' => home_url()
        );

        $response = wp_remote_post($this->api_url . '/edd-sl-api/', array(
            'timeout' => 15,
            'sslverify' => false,
            'body' => $api_params
        ));

        if (is_wp_error($response) || 200 !== wp_remote_retrieve_response_code($response)) {
            wp_die(__('حدث خطأ، يرجى المحاولة مرة أخرى.', 'hozi-akadly'), __('خطأ', 'hozi-akadly'), array('response' => 403));
        }

        $license_data = json_decode(wp_remote_retrieve_body($response));

        if ($license_data->license == 'deactivated') {
            delete_option($this->item_shortname . '_license_status');
            delete_option($this->item_shortname . '_license_expires');
        }

        wp_redirect(admin_url('admin.php?page=hozi-akadly-license&sl_deactivation=true'));
        exit();
    }

    /**
     * Check license
     */
    public function check_license() {
        if (!$this->license_key) {
            return false;
        }

        $api_params = array(
            'edd_action' => 'check_license',
            'license' => $this->license_key,
            'item_name' => urlencode($this->item_name),
            'url' => home_url()
        );

        $response = wp_remote_post($this->api_url . '/edd-sl-api/', array(
            'timeout' => 15,
            'sslverify' => false,
            'body' => $api_params
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if (200 !== $response_code) {
            return false;
        }

        $response_body = wp_remote_retrieve_body($response);
        $license_data = json_decode($response_body);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        if (!isset($license_data->license)) {
            return false;
        }

        // Update license status
        update_option($this->item_shortname . '_license_status', $license_data->license);

        if (isset($license_data->expires)) {
            update_option($this->item_shortname . '_license_expires', $license_data->expires);
        }

        // Update instance variables
        $this->license_status = $license_data->license;

        return $license_data->license;
    }

    /**
     * Plugin updater
     */
    public function plugin_updater() {
        if (!class_exists('EDD_Plugin_Updater')) {
            include(dirname(__FILE__) . '/EDD_Plugin_Updater.php');
        }

        new EDD_Plugin_Updater($this->api_url, HOZI_AKADLY_PLUGIN_FILE, array(
            'version' => $this->version,
            'license' => $this->license_key,
            'item_name' => $this->item_name,
            'author' => $this->author,
            'beta' => false
        ));
    }

    /**
     * Schedule license check
     */
    public function schedule_license_check() {
        if (!wp_next_scheduled('hozi_akadly_daily_license_check')) {
            wp_schedule_event(time(), 'daily', 'hozi_akadly_daily_license_check');
        }
    }

    /**
     * Daily license check
     */
    public function daily_license_check() {
        $this->check_license();
    }

    /**
     * Maybe check license on admin pages
     */
    public function maybe_check_license() {
        // Only check for admin users
        if (!is_admin() || !current_user_can('manage_options')) {
            return;
        }

        // Only check if we have a license key but no valid status
        if ($this->license_key && !$this->is_license_valid()) {
            // Check if we haven't checked recently (avoid too many API calls)
            $last_check = get_transient('hozi_akadly_last_license_check');
            if (!$last_check) {
                $this->check_license();
                // Set transient to avoid checking again for 1 hour
                set_transient('hozi_akadly_last_license_check', time(), HOUR_IN_SECONDS);
            }
        }
    }

    /**
     * Check license status and block functionality
     */
    public function check_license_status() {
        // Only check license for admin users
        if (!is_admin() && !current_user_can('manage_options')) {
            return;
        }

        // Always check current license status from database - try new format first, then old format
        $this->license_key = get_option($this->item_shortname . '_license_key');
        if (empty($this->license_key)) {
            $this->license_key = get_option('hozi_akadly_license_key');
        }

        $this->license_status = get_option($this->item_shortname . '_license_status');
        if ($this->license_status === false) {
            $this->license_status = get_option('hozi_akadly_license_status');
        }

        if (!$this->is_license_valid()) {
            // Block main plugin functionality by preventing class initialization
            add_filter('hozi_akadly_license_valid', '__return_false');
        } else {
            add_filter('hozi_akadly_license_valid', '__return_true');
        }
    }

    /**
     * Check if license is valid
     */
    public function is_license_valid() {
        // TEMPORARY: Allow testing without license for debugging
        if (defined('HOZI_AKADLY_DEBUG') && HOZI_AKADLY_DEBUG) {
            return true;
        }

        // Always get fresh data from database - try new format first, then old format
        $this->license_key = get_option($this->item_shortname . '_license_key');
        if (empty($this->license_key)) {
            $this->license_key = get_option('hozi_akadly_license_key');
        }

        $this->license_status = get_option($this->item_shortname . '_license_status');
        if ($this->license_status === false) {
            $this->license_status = get_option('hozi_akadly_license_status');
        }

        return ($this->license_status !== false && $this->license_status == 'valid');
    }

    /**
     * Add license menu only
     */
    public function add_license_menu_only() {
        add_menu_page(
            __('Hozi Akadly - الترخيص', 'hozi-akadly'),
            __('Hozi Akadly', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly-license',
            array($this, 'license_page'),
            'dashicons-admin-network',
            30
        );
    }

    /**
     * Persistent license notice - shown when not licensed
     */
    public function persistent_license_notice() {
        // Only show to admins with manage_options capability
        if (!current_user_can('manage_options')) {
            return;
        }

        // Prevent multiple calls
        static $notice_shown = false;
        if ($notice_shown) {
            return;
        }

        if ($this->is_license_valid()) {
            return; // Don't show if license is valid
        }

        $notice_shown = true; // Mark as shown

        // Only show on plugin admin pages, not customer dashboard
        $current_screen = get_current_screen();
        if (!$current_screen || strpos($current_screen->id, 'hozi-akadly') === false) {
            return;
        }

        // Show on plugin admin pages only
        ?>
        <div class="notice notice-warning hozi-license-notice" style="border-right: 4px solid #ff6b35; background: linear-gradient(135deg, #fff8e1 0%, #ffeaa7 100%); box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin: 15px 0;">
            <div style="display: flex; align-items: center; padding: 15px;">
                <div style="font-size: 32px; margin-left: 20px; animation: pulse 2s infinite;">🔒</div>
                <div style="flex: 1;">
                    <p style="margin: 0; font-size: 16px; font-weight: bold; color: #d63638;">
                        <strong>⚠️ أكدلي - Akadly - تفعيل الترخيص مطلوب</strong>
                    </p>
                    <p style="margin: 8px 0 0 0; font-size: 14px; color: #646970; line-height: 1.5;">
                        هذه الإضافة تتطلب ترخيص صالح للعمل والاستفادة من جميع المميزات.
                        <strong style="color: #d63638;">بدون ترخيص صالح، لن تعمل أي من وظائف الإضافة.</strong>
                    </p>
                </div>
                <div style="margin-right: 20px; display: flex; gap: 10px;">
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-license'); ?>"
                       class="button button-primary"
                       style="background: #d63638; border-color: #d63638; font-weight: bold; padding: 8px 16px; font-size: 14px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                        🔑 تفعيل الترخيص الآن
                    </a>
                    <a href="https://hostazi.shop/downloads/akadly/"
                       target="_blank"
                       class="button button-secondary"
                       style="font-weight: bold; padding: 8px 16px; font-size: 14px;">
                        🛒 شراء ترخيص
                    </a>
                </div>
            </div>
        </div>

        <style>
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .hozi-license-notice:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-1px);
            transition: all 0.3s ease;
        }

        @media (max-width: 768px) {
            .hozi-license-notice > div {
                flex-direction: column;
                text-align: center;
            }

            .hozi-license-notice > div > div:last-child {
                margin-right: 0;
                margin-top: 15px;
                flex-direction: column;
                gap: 8px;
            }

            .hozi-license-notice > div > div:first-child {
                margin-left: 0;
                margin-bottom: 10px;
            }
        }
        </style>
        <?php
    }



    /**
     * Admin notices
     */
    public function admin_notices() {
        if (isset($_GET['sl_activation']) && !empty($_GET['message'])) {
            switch ($_GET['sl_activation']) {
                case 'false':
                    $message = urldecode($_GET['message']);
                    ?>
                    <div class="error">
                        <p><?php echo $message; ?></p>
                    </div>
                    <?php
                    break;

                case 'true':
                default:
                    ?>
                    <div class="updated">
                        <p><?php _e('تم تفعيل الترخيص بنجاح.', 'hozi-akadly'); ?></p>
                    </div>
                    <?php
                    break;
            }
        }

        if (isset($_GET['sl_deactivation']) && $_GET['sl_deactivation'] == 'true') {
            ?>
            <div class="updated">
                <p><?php _e('تم إلغاء تفعيل الترخيص.', 'hozi-akadly'); ?></p>
            </div>
            <?php
        }

        if (isset($_GET['api_test']) && $_GET['api_test'] == 'true' && !empty($_GET['message'])) {
            ?>
            <div class="notice notice-info">
                <p><?php echo urldecode($_GET['message']); ?></p>
            </div>
            <?php
        }
    }

    /**
     * License page
     */
    public function license_page() {
        // Try new format first, then fallback to old format for backward compatibility
        $license = get_option($this->item_shortname . '_license_key');
        if (empty($license)) {
            $license = get_option('hozi_akadly_license_key');
        }

        $status = get_option($this->item_shortname . '_license_status');
        if ($status === false) {
            $status = get_option('hozi_akadly_license_status');
        }

        $expires = get_option($this->item_shortname . '_license_expires');
        if (empty($expires)) {
            $expires = get_option('hozi_akadly_license_expires');
        }


        ?>
        <div class="wrap">
            <h1><?php _e('Hozi Akadly - إدارة الترخيص', 'hozi-akadly'); ?></h1>

            <?php if ($status !== false && $status == 'valid') : ?>
                <!-- Success Message and Quick Navigation -->
                <div class="hozi-license-success-banner">
                    <div class="hozi-success-content">
                        <div class="hozi-success-icon">
                            <i class="dashicons dashicons-yes-alt"></i>
                        </div>
                        <div class="hozi-success-text">
                            <h2>🎉 تم تفعيل الترخيص بنجاح!</h2>
                            <p>يمكنك الآن الاستفادة من جميع ميزات أكدلي - Akadly. ابدأ بإعداد النظام واستكشاف الميزات الجديدة.</p>
                        </div>
                        <div class="hozi-success-actions">
                            <a href="<?php echo admin_url('admin.php?page=hozi-akadly'); ?>" class="button button-primary button-hero">
                                🏠 الانتقال إلى لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Navigation Cards -->
                <div class="hozi-quick-nav-section">
                    <h2>🚀 الوصول السريع للميزات</h2>
                    <div class="hozi-nav-grid">
                        <a href="<?php echo admin_url('admin.php?page=hozi-akadly'); ?>" class="hozi-nav-card hozi-nav-dashboard">
                            <div class="hozi-nav-icon">
                                <i class="dashicons dashicons-dashboard"></i>
                            </div>
                            <div class="hozi-nav-content">
                                <h3>لوحة التحكم</h3>
                                <p>الإحصائيات والنظرة العامة</p>
                            </div>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-agents'); ?>" class="hozi-nav-card hozi-nav-agents">
                            <div class="hozi-nav-icon">
                                <i class="dashicons dashicons-admin-users"></i>
                            </div>
                            <div class="hozi-nav-content">
                                <h3>إدارة الوكلاء</h3>
                                <p>إضافة وإدارة وكلاء التأكيد</p>
                            </div>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-distribution'); ?>" class="hozi-nav-card hozi-nav-distribution">
                            <div class="hozi-nav-icon">
                                <i class="dashicons dashicons-randomize"></i>
                            </div>
                            <div class="hozi-nav-content">
                                <h3>توزيع الطلبات</h3>
                                <p>تخصيص الطلبات للوكلاء</p>
                            </div>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-tracking'); ?>" class="hozi-nav-card hozi-nav-tracking">
                            <div class="hozi-nav-icon">
                                <i class="dashicons dashicons-location"></i>
                            </div>
                            <div class="hozi-nav-content">
                                <h3>تتبع الطلبات</h3>
                                <p>متابعة حالة التوصيل</p>
                            </div>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-analytics'); ?>" class="hozi-nav-card hozi-nav-analytics">
                            <div class="hozi-nav-icon">
                                <i class="dashicons dashicons-chart-bar"></i>
                            </div>
                            <div class="hozi-nav-content">
                                <h3>التحليلات</h3>
                                <p>تقارير الأداء والإحصائيات</p>
                            </div>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-user-guide'); ?>" class="hozi-nav-card hozi-nav-guide">
                            <div class="hozi-nav-icon">
                                <i class="dashicons dashicons-book"></i>
                            </div>
                            <div class="hozi-nav-content">
                                <h3>دليل الاستخدام</h3>
                                <p>شرح شامل لجميع الميزات</p>
                            </div>
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <div class="hozi-license-container">
                <div class="hozi-license-info">
                    <h2><?php _e('معلومات المنتج', 'hozi-akadly'); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th><?php _e('اسم المنتج:', 'hozi-akadly'); ?></th>
                            <td>أكدلي - Akadly</td>
                        </tr>
                        <tr>
                            <th><?php _e('الإصدار:', 'hozi-akadly'); ?></th>
                            <td><?php echo $this->version; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('المطور:', 'hozi-akadly'); ?></th>
                            <td><a href="https://hostazi.shop" target="_blank"><?php echo $this->author; ?></a></td>
                        </tr>
                        <tr>
                            <th><?php _e('حالة الترخيص:', 'hozi-akadly'); ?></th>
                            <td>
                                <?php if ($status !== false && $status == 'valid') : ?>
                                    <span style="color: green;">✅ <?php _e('نشط', 'hozi-akadly'); ?></span>
                                    <?php if ($expires && $expires !== 'lifetime') : ?>
                                        <br><small><?php printf(__('ينتهي في: %s', 'hozi-akadly'), date_i18n(get_option('date_format'), strtotime($expires))); ?></small>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <span style="color: red;">❌ <?php _e('غير نشط', 'hozi-akadly'); ?></span>
                                    <?php if ($status) : ?>
                                        <br><small style="color: #666;"><?php printf(__('الحالة: %s', 'hozi-akadly'), $status); ?></small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                        </tr>

                    </table>
                </div>

                <div class="hozi-license-form">
                    <form method="post" action="">
                        <?php wp_nonce_field($this->item_shortname . '_license_nonce', $this->item_shortname . '_license_nonce'); ?>

                        <h2><?php _e('مفتاح الترخيص', 'hozi-akadly'); ?></h2>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('مفتاح الترخيص:', 'hozi-akadly'); ?></th>
                                <td>
                                    <input id="<?php echo $this->item_shortname; ?>_license_key"
                                           name="<?php echo $this->item_shortname; ?>_license_key"
                                           type="text"
                                           class="regular-text"
                                           value="<?php esc_attr_e($license); ?>"
                                           placeholder="أدخل مفتاح الترخيص هنا" />
                                    <p class="description">
                                        <?php _e('أدخل مفتاح الترخيص الذي حصلت عليه من', 'hozi-akadly'); ?>
                                        <a href="https://hostazi.shop" target="_blank">Hostazi</a>
                                    </p>
                                </td>
                            </tr>
                        </table>

                        <?php if ($status !== false && $status == 'valid') : ?>
                            <p class="submit">
                                <input type="submit" class="button-secondary" name="edd_license_deactivate" value="<?php _e('إلغاء تفعيل الترخيص', 'hozi-akadly'); ?>"/>
                                <input type="submit" class="button" name="edd_license_check" value="<?php _e('فحص الترخيص', 'hozi-akadly'); ?>" style="margin-right: 10px;"/>
                            </p>
                        <?php else : ?>
                            <p class="submit">
                                <input type="submit" class="button-primary" name="edd_license_activate" value="<?php _e('تفعيل الترخيص', 'hozi-akadly'); ?>" onclick="this.form.submit(); return true;"/>
                                <?php if ($license) : ?>
                                    <input type="submit" class="button" name="edd_license_check" value="<?php _e('فحص الترخيص', 'hozi-akadly'); ?>" style="margin-right: 10px;"/>
                                <?php endif; ?>
                            </p>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>

        <style>
        /* ===== SUCCESS BANNER ===== */
        .hozi-license-success-banner {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 15px;
            margin-bottom: 30px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
        }

        .hozi-success-content {
            display: flex;
            align-items: center;
            padding: 30px;
            color: white;
        }

        .hozi-success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 25px;
            flex-shrink: 0;
        }

        .hozi-success-icon .dashicons {
            font-size: 40px;
            color: white;
        }

        .hozi-success-text {
            flex: 1;
        }

        .hozi-success-text h2 {
            margin: 0 0 10px 0;
            font-size: 2em;
            font-weight: 700;
        }

        .hozi-success-text p {
            margin: 0;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .hozi-success-actions {
            margin-right: 20px;
        }

        .hozi-success-actions .button-hero {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .hozi-success-actions .button-hero:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        /* ===== QUICK NAVIGATION ===== */
        .hozi-quick-nav-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .hozi-quick-nav-section h2 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.8em;
            color: #333;
        }

        .hozi-nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .hozi-nav-card {
            background: white;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .hozi-nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .hozi-nav-card:hover::before {
            left: 100%;
        }

        .hozi-nav-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #333;
        }

        .hozi-nav-dashboard:hover { border-color: #667eea; }
        .hozi-nav-agents:hover { border-color: #4CAF50; }
        .hozi-nav-distribution:hover { border-color: #FF9800; }
        .hozi-nav-tracking:hover { border-color: #2196F3; }
        .hozi-nav-analytics:hover { border-color: #9C27B0; }
        .hozi-nav-guide:hover { border-color: #FF5722; }

        .hozi-nav-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            flex-shrink: 0;
        }

        .hozi-nav-dashboard .hozi-nav-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
        .hozi-nav-agents .hozi-nav-icon { background: linear-gradient(135deg, #4CAF50, #45a049); }
        .hozi-nav-distribution .hozi-nav-icon { background: linear-gradient(135deg, #FF9800, #f57c00); }
        .hozi-nav-tracking .hozi-nav-icon { background: linear-gradient(135deg, #2196F3, #1976d2); }
        .hozi-nav-analytics .hozi-nav-icon { background: linear-gradient(135deg, #9C27B0, #7b1fa2); }
        .hozi-nav-guide .hozi-nav-icon { background: linear-gradient(135deg, #FF5722, #d84315); }

        .hozi-nav-icon .dashicons {
            font-size: 24px;
            color: white;
        }

        .hozi-nav-content h3 {
            margin: 0 0 5px 0;
            font-size: 1.2em;
            font-weight: 600;
        }

        .hozi-nav-content p {
            margin: 0;
            font-size: 0.9em;
            color: #666;
        }

        /* ===== LICENSE CONTAINER ===== */
        .hozi-license-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .hozi-license-info,
        .hozi-license-form {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }

        .hozi-license-info h2,
        .hozi-license-form h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
        }

        @media (max-width: 768px) {
            .hozi-license-container {
                grid-template-columns: 1fr;
            }

            .hozi-success-content {
                flex-direction: column;
                text-align: center;
            }

            .hozi-success-icon {
                margin: 0 0 20px 0;
            }

            .hozi-success-actions {
                margin: 20px 0 0 0;
            }

            .hozi-nav-grid {
                grid-template-columns: 1fr;
            }
        }
        </style>
        <?php
    }

    /**
     * Get license key
     */
    public function get_license_key() {
        return $this->license_key;
    }

    /**
     * Get license status
     */
    public function get_license_status() {
        return $this->license_status;
    }



    /**
     * Plugin action links
     */
    public function plugin_action_links($links) {
        if (!$this->is_license_valid()) {
            $license_link = '<a href="' . admin_url('admin.php?page=hozi-akadly-license') . '" style="color: #d63638; font-weight: bold;">تفعيل الترخيص</a>';
            array_unshift($links, $license_link);
        } else {
            $dashboard_link = '<a href="' . admin_url('admin.php?page=hozi-akadly') . '">لوحة التحكم</a>';
            array_unshift($links, $dashboard_link);
        }
        return $links;
    }

    /**
     * Plugin row notice - disabled to avoid duplication
     */
    public function plugin_row_notice($plugin_file, $plugin_data) {
        // Disabled - persistent_license_notice handles all notices
        return;
    }
}
