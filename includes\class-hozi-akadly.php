<?php
/**
 * Main plugin class
 */

if (!defined('ABSPATH')) {
    exit;
}

// Include new messaging system classes
require_once plugin_dir_path(__FILE__) . 'class-messaging-system-v2.php';
require_once plugin_dir_path(__FILE__) . 'class-admin-stats-tracker.php';
require_once plugin_dir_path(__FILE__) . 'class-admin-as-agent-v2.php';

class Hozi_Akadly {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Agent Manager instance
     */
    public $agent_manager;

    /**
     * Order Distributor instance
     */
    public $order_distributor;

    /**
     * Order Tracker instance
     */
    public $order_tracker;

    /**
     * Admin instance
     */
    public $admin;

    /**
     * Public instance
     */
    public $public;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->init_classes();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('woocommerce_new_order', array($this, 'handle_new_order'));
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 4);
        add_action('hozi_akadly_delayed_tracking_transition', array($this, 'handle_delayed_tracking_transition'), 10, 3);
        add_action('hozi_akadly_verify_tracking_entry', array($this, 'verify_tracking_entry'), 10, 2);
    }

    /**
     * Initialize classes
     */
    private function init_classes() {
        // Always initialize admin class for license management
        if (is_admin()) {
            $this->admin = new Hozi_Akadly_Admin();
        }

        // Always initialize main functionality - license check will be done in each method
        $this->agent_manager = new Hozi_Akadly_Agent_Manager();
        $this->order_distributor = new Hozi_Akadly_Order_Distributor();
        $this->order_tracker = new Hozi_Akadly_Order_Tracker();

        // Initialize admin menu manager for agents
        if (is_admin()) {
            new Hozi_Akadly_Admin_Menu_Manager();
        }

        // Initialize messaging system V2 (improved and bug-free)
        new Hozi_Akadly_Messaging_System_V2();
        
        // Initialize admin stats tracker
        new Hozi_Akadly_Admin_Stats_Tracker();
        
        // Initialize admin as agent V2
        new Hozi_Akadly_Admin_As_Agent_V2();

        if (!is_admin()) {
            $this->public = new Hozi_Akadly_Public();
        }
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Add custom order statuses
        $this->add_custom_order_statuses();
    }

    /**
     * Handle new order
     */
    public function handle_new_order($order_id) {
        // Check license before processing - only for admin functionality
        if (is_admin()) {
            $license_manager = Hozi_Akadly_License_Manager::get_instance();
            if (!$license_manager->is_license_valid()) {
                return;
            }
        }

        if (get_option('hozi_akadly_auto_assign_new_orders') === 'yes') {
            $order = wc_get_order($order_id);
            if ($order && $this->should_assign_order($order)) {
                $this->order_distributor->assign_order($order_id);
            }
        }
    }

    /**
     * Handle order status change
     */
    public function handle_order_status_change($order_id, $old_status, $new_status, $order) {
        // Check license before processing - only for admin functionality
        if (is_admin()) {
            $license_manager = Hozi_Akadly_License_Manager::get_instance();
            if (!$license_manager->is_license_valid()) {
                return;
            }
        }

        // Handle transition to completed - move to tracking (SIMPLIFIED)
        if ($new_status === 'completed') {
            // Direct call to order tracker for immediate processing
            if ($this->order_tracker) {
                $this->order_tracker->handle_woocommerce_status_change($order_id, $old_status, $new_status, $order);
            }

            // Also schedule delayed processing as backup
            wp_schedule_single_event(time() + 5, 'hozi_akadly_delayed_tracking_transition', array($order_id, $old_status, $new_status));
        }

        // Log status changes for assigned orders
        global $wpdb;

        try {
            $assignment = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
                $order_id
            ));

            if ($assignment) {
                $wpdb->insert(
                    $wpdb->prefix . 'hozi_confirmation_logs',
                    array(
                        'order_id' => $order_id,
                        'agent_id' => $assignment->agent_id,
                        'action' => 'status_change',
                        'old_status' => $old_status,
                        'new_status' => $new_status,
                        'created_at' => current_time('mysql')
                    )
                );
            }
        } catch (Exception $e) {
            // Silently handle database errors
        }
    }

    /**
     * Handle delayed tracking transition (called by scheduled event)
     */
    public function handle_delayed_tracking_transition($order_id, $old_status, $new_status) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }

        // Double check the order is still completed
        if ($order->get_status() !== 'completed') {
            return;
        }

        $this->handle_completed_transition($order_id, $old_status, $new_status, $order);
    }

    /**
     * Handle completed order transition - move to tracking system
     */
    private function handle_completed_transition($order_id, $old_status, $new_status, $order) {
        global $wpdb;

        try {
            // Check if order has confirmed assignment
            $assignment = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d AND confirmation_status = 'confirmed'",
                $order_id
            ));

            if ($assignment) {
                // Check if already in tracking system (with retry mechanism)
                $existing_tracking = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                    $order_id
                ));

                if (!$existing_tracking) {
                    // Add initial tracking entry with out_for_delivery status
                    $result = $wpdb->insert(
                        $wpdb->prefix . 'hozi_order_tracking',
                        array(
                            'order_id' => $order_id,
                            'agent_id' => $assignment->agent_id,
                            'status' => 'out_for_delivery', // Initial status for tracking
                            'previous_status' => null,
                            'reason_category' => '',
                            'reason_details' => '',
                            'notes' => 'تم نقل الطلب لمرحلة التتبع النهائي بعد اكتمال المراجعة (تأخير 5 ثواني لتجنب تداخل إضافات التوصيل)',
                            'updated_by' => get_current_user_id() ?: 1, // Fallback to admin if no user
                            'updated_at' => current_time('mysql'),
                            'created_at' => current_time('mysql')
                        ),
                        array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
                    );

                    if ($result) {
                        // Add order note
                        $agent_name = $this->get_agent_name($assignment->agent_id);
                        $order->add_order_note(
                            sprintf(
                                '🎯 تم نقل الطلب لمرحلة التتبع النهائي - الوكيل: %s سيتابع التسليم (مؤجل لتجنب تداخل إضافات التوصيل)',
                                $agent_name
                            ),
                            0 // Private note
                        );

                        // Log the transition
                        Hozi_Akadly_Database::log_action(
                            $order_id,
                            $assignment->agent_id,
                            'moved_to_tracking',
                            $old_status,
                            $new_status,
                            'تم نقل الطلب لمرحلة التتبع النهائي (مؤجل)'
                        );

                        // Schedule multiple verification checks to ensure persistence
                        wp_schedule_single_event(time() + 10, 'hozi_akadly_verify_tracking_entry', array($order_id, $assignment->agent_id));
                        wp_schedule_single_event(time() + 30, 'hozi_akadly_verify_tracking_entry', array($order_id, $assignment->agent_id));
                        wp_schedule_single_event(time() + 60, 'hozi_akadly_verify_tracking_entry', array($order_id, $assignment->agent_id));
                    } else {
                        // Log insertion failure
                        error_log("Hozi Akadly: Failed to insert tracking entry for order $order_id");
                    }
                } else {
                    // Already exists, just add a note
                    $order->add_order_note(
                        '✅ الطلب موجود بالفعل في نظام التتبع',
                        0 // Private note
                    );
                }
            } else {
                // Log if no confirmed assignment found
                error_log("Hozi Akadly: No confirmed assignment found for order $order_id when trying to move to tracking");
            }
        } catch (Exception $e) {
            // Log error but don't break the order status change
            error_log('Hozi Akadly Completed Transition Error: ' . $e->getMessage());
        }
    }

    /**
     * Verify tracking entry exists (called by scheduled event)
     */
    public function verify_tracking_entry($order_id, $agent_id) {
        global $wpdb;

        try {
            // Check if tracking entry still exists
            $tracking = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $order_id
            ));

            if (!$tracking) {
                // Entry was deleted, recreate it
                $result = $wpdb->insert(
                    $wpdb->prefix . 'hozi_order_tracking',
                    array(
                        'order_id' => $order_id,
                        'agent_id' => $agent_id,
                        'status' => 'out_for_delivery',
                        'previous_status' => null,
                        'reason_category' => '',
                        'reason_details' => '',
                        'notes' => 'تم إعادة إنشاء إدخال التتبع بعد حذفه من قبل إضافة أخرى',
                        'updated_by' => 1, // Admin user
                        'updated_at' => current_time('mysql'),
                        'created_at' => current_time('mysql')
                    ),
                    array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
                );

                if ($result) {
                    $order = wc_get_order($order_id);
                    if ($order) {
                        $agent_name = $this->get_agent_name($agent_id);
                        $order->add_order_note(
                            sprintf(
                                '🔄 تم إعادة إنشاء إدخال التتبع للوكيل: %s (تم حذفه من قبل إضافة أخرى)',
                                $agent_name
                            ),
                            0 // Private note
                        );
                    }

                    error_log("Hozi Akadly: Recreated tracking entry for order $order_id (was deleted by another plugin)");
                }
            }
        } catch (Exception $e) {
            error_log('Hozi Akadly Verify Tracking Error: ' . $e->getMessage());
        }
    }

    /**
     * Get agent name by ID
     */
    private function get_agent_name($agent_id) {
        global $wpdb;

        $agent_name = $wpdb->get_var($wpdb->prepare(
            "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
            $agent_id
        ));

        return $agent_name ?: 'غير محدد';
    }

    /**
     * Check if order should be assigned
     */
    private function should_assign_order($order) {
        $statuses_to_assign = get_option('hozi_akadly_order_statuses_to_assign', array('pending', 'processing'));
        return in_array($order->get_status(), $statuses_to_assign);
    }

    /**
     * Add custom order statuses
     */
    private function add_custom_order_statuses() {
        add_action('init', array($this, 'register_custom_order_statuses'));
        add_filter('wc_order_statuses', array($this, 'add_custom_order_statuses_to_list'));
    }

    /**
     * Register custom order statuses
     */
    public function register_custom_order_statuses() {
        register_post_status('wc-pending-confirm', array(
            'label' => __('في انتظار التأكيد', 'hozi-akadly'),
            'public' => true,
            'exclude_from_search' => false,
            'show_in_admin_all_list' => true,
            'show_in_admin_status_list' => true,
            'label_count' => _n_noop('في انتظار التأكيد <span class="count">(%s)</span>', 'في انتظار التأكيد <span class="count">(%s)</span>', 'hozi-akadly')
        ));

        register_post_status('wc-confirmed', array(
            'label' => __('تم التأكيد', 'hozi-akadly'),
            'public' => true,
            'exclude_from_search' => false,
            'show_in_admin_all_list' => true,
            'show_in_admin_status_list' => true,
            'label_count' => _n_noop('تم التأكيد <span class="count">(%s)</span>', 'تم التأكيد <span class="count">(%s)</span>', 'hozi-akadly')
        ));

        register_post_status('wc-no-answer', array(
            'label' => __('العميل لم يرد', 'hozi-akadly'),
            'public' => true,
            'exclude_from_search' => false,
            'show_in_admin_all_list' => true,
            'show_in_admin_status_list' => true,
            'label_count' => _n_noop('العميل لم يرد <span class="count">(%s)</span>', 'العميل لم يرد <span class="count">(%s)</span>', 'hozi-akadly')
        ));

        register_post_status('wc-callback-later', array(
            'label' => __('إعادة الاتصال لاحقًا', 'hozi-akadly'),
            'public' => true,
            'exclude_from_search' => false,
            'show_in_admin_all_list' => true,
            'show_in_admin_status_list' => true,
            'label_count' => _n_noop('إعادة الاتصال لاحقًا <span class="count">(%s)</span>', 'إعادة الاتصال لاحقًا <span class="count">(%s)</span>', 'hozi-akadly')
        ));
    }

    /**
     * Add custom order statuses to WooCommerce list
     */
    public function add_custom_order_statuses_to_list($order_statuses) {
        $new_statuses = array();

        foreach ($order_statuses as $key => $status) {
            $new_statuses[$key] = $status;

            if ('wc-processing' === $key) {
                $new_statuses['wc-pending-confirm'] = __('في انتظار التأكيد', 'hozi-akadly');
                $new_statuses['wc-confirmed'] = __('تم التأكيد', 'hozi-akadly');
                $new_statuses['wc-no-answer'] = __('العميل لم يرد', 'hozi-akadly');
                $new_statuses['wc-callback-later'] = __('إعادة الاتصال لاحقًا', 'hozi-akadly');
            }
        }

        return $new_statuses;
    }

    /**
     * Get plugin version
     */
    public function get_version() {
        return HOZI_AKADLY_VERSION;
    }
}
