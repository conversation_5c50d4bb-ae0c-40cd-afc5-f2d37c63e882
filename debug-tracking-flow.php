<?php
/**
 * Debug Tracking Flow
 * 
 * This file debugs the complete flow from order confirmation to delivery tracking.
 * Place this file in your WordPress root directory and access via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🔍 تشخيص مسار متابعة التوصيل - Akadly</h1>";

// Check if we're running the debug
$run_debug = isset($_GET['run_debug']) && $_GET['run_debug'] == '1';
$test_agent_id = isset($_GET['test_agent_id']) ? intval($_GET['test_agent_id']) : 0;

if ($run_debug && $test_agent_id) {
    echo "<h2>🔍 تشخيص مفصل للوكيل ID: {$test_agent_id}</h2>";
    
    try {
        global $wpdb;
        
        // Step 1: Get agent info
        echo "<h3>1️⃣ معلومات الوكيل:</h3>";
        $agent = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
            $test_agent_id
        ));
        
        if (!$agent) {
            echo "<p style='color: red;'>❌ الوكيل غير موجود!</p>";
            return;
        }
        
        echo "<p>✅ الوكيل: {$agent->name} (ID: {$agent->id}, User ID: {$agent->user_id})</p>";
        
        // Step 2: Check confirmed orders in assignments table
        echo "<h3>2️⃣ الطلبات المؤكدة في جدول التخصيصات:</h3>";
        $confirmed_assignments = $wpdb->get_results($wpdb->prepare(
            "SELECT oa.*, p.post_status 
             FROM {$wpdb->prefix}hozi_order_assignments oa
             LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
             WHERE oa.agent_id = %d 
             AND oa.confirmation_status = 'confirmed'
             AND (oa.notes IS NULL OR oa.notes NOT LIKE 'ARCHIVED:%')",
            $test_agent_id
        ));
        
        echo "<p>عدد الطلبات المؤكدة غير المؤرشفة: " . count($confirmed_assignments) . "</p>";
        
        if (!empty($confirmed_assignments)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>حالة WC</th><th>الملاحظات</th></tr>";
            foreach ($confirmed_assignments as $assignment) {
                $notes_preview = substr($assignment->notes ?? '', 0, 30);
                echo "<tr>";
                echo "<td>{$assignment->order_id}</td>";
                echo "<td>{$assignment->confirmed_at}</td>";
                echo "<td>{$assignment->post_status}</td>";
                echo "<td>{$notes_preview}...</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Step 3: Check tracking table
        echo "<h3>3️⃣ الطلبات في جدول التتبع:</h3>";
        $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
        
        if (!$table_exists) {
            echo "<p style='color: red;'>❌ جدول التتبع غير موجود!</p>";
        } else {
            $tracking_orders = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE agent_id = %d ORDER BY created_at DESC",
                $test_agent_id
            ));
            
            echo "<p>عدد الطلبات في جدول التتبع: " . count($tracking_orders) . "</p>";
            
            if (!empty($tracking_orders)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr><th>رقم الطلب</th><th>الحالة</th><th>تاريخ الإنشاء</th><th>الملاحظات</th></tr>";
                foreach ($tracking_orders as $track) {
                    $notes_preview = substr($track->notes ?? '', 0, 40);
                    echo "<tr>";
                    echo "<td>{$track->order_id}</td>";
                    echo "<td>{$track->status}</td>";
                    echo "<td>{$track->created_at}</td>";
                    echo "<td>{$notes_preview}...</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        
        // Step 4: Test get_trackable_orders_for_user function
        echo "<h3>4️⃣ اختبار دالة get_trackable_orders_for_user:</h3>";
        
        if ($agent->user_id) {
            $trackable_orders = Hozi_Akadly_Delivery_Tracking_Permissions::get_trackable_orders_for_user($agent->user_id);
            echo "<p>عدد الطلبات القابلة للتتبع: " . count($trackable_orders) . "</p>";
            
            if (!empty($trackable_orders)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr><th>رقم الطلب</th></tr>";
                foreach ($trackable_orders as $order) {
                    echo "<tr><td>{$order->order_id}</td></tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ الوكيل غير مرتبط بمستخدم WordPress</p>";
        }
        
        // Step 5: Check what delivery-tracking.php would show
        echo "<h3>5️⃣ محاكاة ما ستعرضه صفحة متابعة التوصيل:</h3>";
        
        // Simulate the delivery tracking page logic
        if (!empty($trackable_orders)) {
            $order_ids = array_column($trackable_orders, 'order_id');
            $order_ids_str = implode(',', array_map('intval', $order_ids));
            
            if ($order_ids_str) {
                $confirmed_assignments_for_tracking = $wpdb->get_results("
                    SELECT * FROM {$wpdb->prefix}hozi_order_assignments
                    WHERE order_id IN ({$order_ids_str})
                    AND confirmation_status = 'confirmed'
                ");
                
                echo "<p>الطلبات التي ستظهر في متابعة التوصيل: " . count($confirmed_assignments_for_tracking) . "</p>";
                
                // Filter out archived orders
                $non_archived = array_filter($confirmed_assignments_for_tracking, function($assignment) {
                    return !(strpos($assignment->notes ?? '', 'ARCHIVED:') === 0);
                });
                
                echo "<p>الطلبات غير المؤرشفة: " . count($non_archived) . "</p>";
                
                if (!empty($non_archived)) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                    echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>مؤرشف؟</th></tr>";
                    foreach ($non_archived as $assignment) {
                        $archived = (strpos($assignment->notes ?? '', 'ARCHIVED:') === 0) ? 'نعم' : 'لا';
                        echo "<tr>";
                        echo "<td>{$assignment->order_id}</td>";
                        echo "<td>{$assignment->confirmed_at}</td>";
                        echo "<td>{$archived}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        }
        
        // Step 6: Check missing orders
        echo "<h3>6️⃣ الطلبات المؤكدة المفقودة من التتبع:</h3>";
        
        if ($table_exists) {
            $missing_orders = $wpdb->get_results($wpdb->prepare("
                SELECT DISTINCT oa.order_id, oa.confirmed_at
                FROM {$wpdb->prefix}hozi_order_assignments oa
                LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
                WHERE oa.agent_id = %d
                AND oa.confirmation_status = 'confirmed'
                AND (oa.notes IS NULL OR oa.notes NOT LIKE 'ARCHIVED:%')
                AND ot.id IS NULL
            ", $test_agent_id));
            
            echo "<p>الطلبات المؤكدة المفقودة من جدول التتبع: " . count($missing_orders) . "</p>";
            
            if (!empty($missing_orders)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th></tr>";
                foreach ($missing_orders as $order) {
                    echo "<tr>";
                    echo "<td>{$order->order_id}</td>";
                    echo "<td>{$order->confirmed_at}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                echo "<p><strong>🔧 هذه الطلبات يجب نقلها إلى جدول التتبع!</strong></p>";
            }
        }
        
        // Step 7: Test auto-transfer
        echo "<h3>7️⃣ اختبار النقل التلقائي:</h3>";
        $transferred_count = Hozi_Akadly_Delivery_Tracking_Permissions::ensure_confirmed_orders_in_tracking($test_agent_id);
        
        if ($transferred_count > 0) {
            echo "<p style='color: green;'>✅ تم نقل {$transferred_count} طلب إلى جدول التتبع</p>";
        } else {
            echo "<p>✅ جميع الطلبات موجودة في جدول التتبع</p>";
        }
        
        echo "<h3>🎯 التشخيص النهائي:</h3>";
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #0073aa;'>";
        
        if (empty($confirmed_assignments)) {
            echo "<p style='color: orange;'>⚠️ لا توجد طلبات مؤكدة للوكيل</p>";
        } elseif (empty($trackable_orders)) {
            echo "<p style='color: red;'>❌ دالة get_trackable_orders_for_user لا تعيد أي طلبات</p>";
        } elseif (empty($non_archived)) {
            echo "<p style='color: orange;'>⚠️ جميع الطلبات مؤرشفة</p>";
        } else {
            echo "<p style='color: green;'>✅ يجب أن تظهر " . count($non_archived) . " طلب في متابعة التوصيل</p>";
        }
        
        echo "</div>";
        
        echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-delivery-tracking') . "' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🚀 انتقل إلى متابعة التوصيل</a></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ حدث خطأ أثناء التشخيص: " . $e->getMessage() . "</p>";
    }
} else {
    // Display debug form
    echo "<h2>🛠️ وصف التشخيص</h2>";
    echo "<p>هذا التشخيص يتتبع المسار الكامل من تأكيد الطلب إلى ظهوره في متابعة التوصيل.</p>";
    
    echo "<h2>🔧 ما سيقوم به التشخيص:</h2>";
    echo "<ol>";
    echo "<li>فحص معلومات الوكيل</li>";
    echo "<li>عرض الطلبات المؤكدة في جدول التخصيصات</li>";
    echo "<li>عرض الطلبات في جدول التتبع</li>";
    echo "<li>اختبار دالة get_trackable_orders_for_user</li>";
    echo "<li>محاكاة منطق صفحة متابعة التوصيل</li>";
    echo "<li>البحث عن الطلبات المفقودة</li>";
    echo "<li>اختبار النقل التلقائي</li>";
    echo "</ol>";
    
    // Get available agents
    global $wpdb;
    $agents = $wpdb->get_results("SELECT id, name FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 ORDER BY name");
    
    if (empty($agents)) {
        echo "<p style='color: red;'>❌ لا توجد وكلاء نشطين في النظام!</p>";
    } else {
        echo "<h2>👨‍💻 اختيار الوكيل للتشخيص:</h2>";
        echo "<form method='GET'>";
        echo "<input type='hidden' name='run_debug' value='1'>";
        echo "<p><label>اختر الوكيل:</label></p>";
        echo "<select name='test_agent_id' required style='padding: 8px; margin: 10px 0;'>";
        echo "<option value=''>-- اختر وكيل --</option>";
        foreach ($agents as $agent) {
            echo "<option value='{$agent->id}'>{$agent->name} (ID: {$agent->id})</option>";
        }
        echo "</select><br>";
        echo "<button type='submit' style='background: #0073aa; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer;'>🔍 تشغيل التشخيص</button>";
        echo "</form>";
    }
}

echo "<hr>";
echo "<p>تم إنشاء هذا التشخيص بواسطة فريق Akadly - " . date('Y-m-d H:i:s') . "</p>";
?>
