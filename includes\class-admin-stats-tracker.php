<?php
/**
 * Admin Stats Tracker - Simple and Accurate
 * Tracks admin actions separately from agent actions
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Admin_Stats_Tracker {
    
    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'hozi_admin_actions';
        
        // Create table
        $this->create_table();
        
        // Hook into admin actions
        add_action('hozi_admin_confirmed_order', array($this, 'track_admin_confirmation'), 10, 3);
        add_action('hozi_admin_rejected_order', array($this, 'track_admin_rejection'), 10, 3);
    }
    
    /**
     * Create admin actions table
     */
    public function create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            admin_user_id bigint(20) NOT NULL,
            order_id bigint(20) NOT NULL,
            action_type enum('confirmed', 'rejected', 'no_answer', 'callback') DEFAULT 'confirmed',
            acting_as_agent_id bigint(20) DEFAULT NULL,
            notes text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY admin_user_id (admin_user_id),
            KEY order_id (order_id),
            KEY action_type (action_type),
            KEY acting_as_agent_id (acting_as_agent_id),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Track admin confirmation
     */
    public function track_admin_confirmation($admin_id, $order_id, $agent_id = null) {
        $this->track_action($admin_id, $order_id, 'confirmed', $agent_id);
    }
    
    /**
     * Track admin rejection
     */
    public function track_admin_rejection($admin_id, $order_id, $agent_id = null) {
        $this->track_action($admin_id, $order_id, 'rejected', $agent_id);
    }
    
    /**
     * Track any admin action
     */
    private function track_action($admin_id, $order_id, $action_type, $agent_id = null, $notes = '') {
        global $wpdb;
        
        $wpdb->insert(
            $this->table_name,
            array(
                'admin_user_id' => $admin_id,
                'order_id' => $order_id,
                'action_type' => $action_type,
                'acting_as_agent_id' => $agent_id,
                'notes' => $notes,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%d', '%s', '%s')
        );
    }
    
    /**
     * Get admin stats
     */
    public function get_admin_stats($admin_id, $date_from = null, $date_to = null) {
        global $wpdb;
        
        $where_conditions = array("admin_user_id = %d");
        $where_values = array($admin_id);
        
        if ($date_from) {
            $where_conditions[] = "created_at >= %s";
            $where_values[] = $date_from;
        }
        
        if ($date_to) {
            $where_conditions[] = "created_at <= %s";
            $where_values[] = $date_to;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $stats = $wpdb->get_row($wpdb->prepare("
            SELECT 
                COUNT(*) as total_actions,
                SUM(CASE WHEN action_type = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                SUM(CASE WHEN action_type = 'rejected' THEN 1 ELSE 0 END) as rejected,
                SUM(CASE WHEN action_type = 'no_answer' THEN 1 ELSE 0 END) as no_answer,
                SUM(CASE WHEN acting_as_agent_id IS NOT NULL THEN 1 ELSE 0 END) as acting_as_agent
            FROM {$this->table_name}
            WHERE {$where_clause}
        ", $where_values));
        
        return $stats;
    }
    
    /**
     * Get recent admin actions
     */
    public function get_recent_actions($admin_id, $limit = 10) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare("
            SELECT aa.*, 
                   agent.name as agent_name,
                   p.post_title as order_title
            FROM {$this->table_name} aa
            LEFT JOIN {$wpdb->prefix}hozi_agents agent ON aa.acting_as_agent_id = agent.id
            LEFT JOIN {$wpdb->posts} p ON aa.order_id = p.ID
            WHERE aa.admin_user_id = %d
            ORDER BY aa.created_at DESC
            LIMIT %d
        ", $admin_id, $limit));
    }
}