<?php
/**
 * Agent dashboard view
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <!-- Simple Header -->
    <div class="hozi-simple-header">
        <div class="hozi-header-content">
            <h1><?php _e('طلباتي - وكيل التأكيد', 'hozi-akadly'); ?></h1>
            <button type="button" class="hozi-system-guide-btn" onclick="openSystemGuide()">
                <span class="dashicons dashicons-info"></span>
                <span class="hozi-guide-text"><?php _e('شرح النظام', 'hozi-akadly'); ?></span>
            </button>
        </div>
    </div>

    <!-- System Guide Modal -->
    <div id="hozi-system-guide-modal" class="hozi-modal-overlay" style="display: none;">
        <div class="hozi-modal-content">
            <div class="hozi-modal-header">
                <h2>📚 شرح نظام أكدلي - دليل الوكيل</h2>
                <button type="button" class="hozi-modal-close" onclick="closeSystemGuide()">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>

            <div class="hozi-modal-body">
                <div class="hozi-guide-intro">
                    <p><strong>مرحباً بك في نظام أكدلي!</strong> 🎯</p>
                    <p>نظام أكدلي يعمل بـ <strong>3 خطوات بسيطة</strong> لضمان تأكيد وتتبع جميع الطلبات بكفاءة عالية.</p>
                </div>

                <div class="hozi-steps-container">
                    <!-- Step 1 -->
                    <div class="hozi-step-card hozi-step-active">
                        <div class="hozi-step-header">
                            <div class="hozi-step-number">1</div>
                            <div class="hozi-step-title">
                                <h3>📞 تأكيد الطلبات</h3>
                                <span class="hozi-step-subtitle">الخطوة الحالية</span>
                            </div>
                        </div>
                        <div class="hozi-step-content">
                            <p><strong>ما تفعله هنا:</strong></p>
                            <ul>
                                <li>🔍 <strong>مراجعة الطلبات المخصصة لك</strong> - كل طلب يحتوي على معلومات العميل والمنتجات</li>
                                <li>📞 <strong>الاتصال بالعميل</strong> - اضغط على زر "بدء المكالمة" للاتصال المباشر</li>
                                <li>✅ <strong>تأكيد الطلب</strong> - بعد موافقة العميل، اضغط "تم التأكيد"</li>
                                <li>📝 <strong>إضافة ملاحظات</strong> - سجل أي تفاصيل مهمة من المكالمة</li>
                                <li>🎁 <strong>البيع الإضافي</strong> - اقترح منتجات إضافية باستخدام النصوص المقترحة</li>
                            </ul>

                            <div class="hozi-step-actions">
                                <p><strong>الإجراءات المتاحة:</strong></p>
                                <div class="hozi-action-examples">
                                    <span class="hozi-action-btn hozi-action-confirm">✅ تم التأكيد</span>
                                    <span class="hozi-action-btn hozi-action-no-answer">📞 لم يرد</span>
                                    <span class="hozi-action-btn hozi-action-callback">🔄 إعادة الاتصال</span>
                                    <span class="hozi-action-btn hozi-action-reject">❌ تم الرفض</span>
                                </div>
                            </div>

                            <div class="hozi-step-tip">
                                <strong>💡 نصيحة:</strong> عند تأكيد الطلب، سينتقل تلقائياً إلى الخطوة التالية (متابعة التوصيل).
                            </div>
                        </div>
                    </div>

                    <!-- Step 2 -->
                    <div class="hozi-step-card">
                        <div class="hozi-step-header">
                            <div class="hozi-step-number">2</div>
                            <div class="hozi-step-title">
                                <h3>🚚 متابعة التوصيل</h3>
                                <span class="hozi-step-subtitle">بعد التأكيد</span>
                            </div>
                        </div>
                        <div class="hozi-step-content">
                            <p><strong>ما يحدث هنا:</strong></p>
                            <ul>
                                <li>📦 <strong>الطلبات المؤكدة تظهر هنا</strong> - جميع الطلبات التي أكدتها في الخطوة الأولى</li>
                                <li>🚛 <strong>تتبع حالة التوصيل</strong> - تحديث حالة الطلب أثناء عملية التوصيل</li>
                                <li>📱 <strong>التواصل مع العميل</strong> - إعلام العميل بحالة طلبه</li>
                                <li>📊 <strong>تحديث الحالة</strong> - تم التوصيل، مؤجل، استبدال، أو مرفوض</li>
                            </ul>

                            <div class="hozi-step-actions">
                                <p><strong>حالات التوصيل:</strong></p>
                                <div class="hozi-delivery-statuses">
                                    <span class="hozi-status-badge hozi-status-delivered">✅ تم التوصيل</span>
                                    <span class="hozi-status-badge hozi-status-postponed">⏰ مؤجل</span>
                                    <span class="hozi-status-badge hozi-status-exchange">🔄 استبدال</span>
                                    <span class="hozi-status-badge hozi-status-rejected">❌ مرفوض</span>
                                </div>
                            </div>

                            <div class="hozi-step-tip">
                                <strong>💡 نصيحة:</strong> تحديث حالة التوصيل يساعد في تتبع الأداء وإرسال تقارير دقيقة للإدارة.
                            </div>
                        </div>
                    </div>

                    <!-- Step 3 -->
                    <div class="hozi-step-card">
                        <div class="hozi-step-header">
                            <div class="hozi-step-number">3</div>
                            <div class="hozi-step-title">
                                <h3>📁 الطلبات المؤرشفة</h3>
                                <span class="hozi-step-subtitle">المكتملة</span>
                            </div>
                        </div>
                        <div class="hozi-step-content">
                            <p><strong>ما تجده هنا:</strong></p>
                            <ul>
                                <li>📋 <strong>سجل كامل</strong> - جميع الطلبات التي تم التعامل معها</li>
                                <li>📊 <strong>إحصائيات الأداء</strong> - معدل النجاح والتأكيد</li>
                                <li>🔍 <strong>البحث والفلترة</strong> - العثور على طلبات محددة بسهولة</li>
                                <li>📈 <strong>تقارير شخصية</strong> - تتبع إنجازاتك وتطوير أدائك</li>
                            </ul>

                            <div class="hozi-step-tip">
                                <strong>💡 فائدة:</strong> مراجعة الطلبات المؤرشفة تساعدك في تحسين أسلوب التأكيد وزيادة معدل النجاح.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="hozi-guide-footer">
                    <div class="hozi-workflow-summary">
                        <h4>🔄 ملخص سير العمل:</h4>
                        <div class="hozi-workflow-steps">
                            <div class="hozi-workflow-step">
                                <span class="hozi-workflow-number">1</span>
                                <span class="hozi-workflow-text">تأكيد الطلب</span>
                            </div>
                            <div class="hozi-workflow-arrow">→</div>
                            <div class="hozi-workflow-step">
                                <span class="hozi-workflow-number">2</span>
                                <span class="hozi-workflow-text">متابعة التوصيل</span>
                            </div>
                            <div class="hozi-workflow-arrow">→</div>
                            <div class="hozi-workflow-step">
                                <span class="hozi-workflow-number">3</span>
                                <span class="hozi-workflow-text">الأرشفة</span>
                            </div>
                        </div>
                    </div>

                    <div class="hozi-quick-tips">
                        <h4>⚡ نصائح سريعة:</h4>
                        <ul>
                            <li><strong>استخدم مفتاح Space</strong> للاتصال السريع</li>
                            <li><strong>اقرأ ملاحظات العميل</strong> قبل الاتصال</li>
                            <li><strong>استخدم النصوص المقترحة</strong> للبيع الإضافي</li>
                            <li><strong>أضف ملاحظات مفصلة</strong> لكل مكالمة</li>
                            <li><strong>تابع الإشعارات الصوتية</strong> للطلبات الجديدة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="hozi-modal-footer">
                <button type="button" class="button button-primary" onclick="closeSystemGuide()">
                    فهمت، دعني أبدأ العمل! 🚀
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Controls - Mobile Friendly -->
    <div class="hozi-notification-controls-mobile">
        <div class="hozi-notification-row">
            <span class="hozi-notification-label">🔔 الإشعارات الصوتية</span>
            <label class="hozi-notification-toggle">
                <input type="checkbox" id="notification-toggle" checked>
                <span class="hozi-toggle-slider"></span>
            </label>
        </div>

        <div class="hozi-notification-status-row">
            <div id="notification-status" class="hozi-status-item">
                ✅ مفعلة
            </div>
            <div id="background-status" class="hozi-status-item">
                🔄 جاري التحقق من الدعم...
            </div>
            <button type="button" id="request-permission-btn" class="hozi-test-btn" style="display: none;">
                🔔 طلب الإذن
            </button>
            <button type="button" id="test-notification-btn" class="hozi-test-btn">
                🔊 اختبار
            </button>
        </div>
    </div>

    <!-- Navigation Tabs - 3 Steps Only -->
    <div class="hozi-nav-tabs">
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-orders'); ?>" class="hozi-nav-tab hozi-nav-tab-active">
            <span class="dashicons dashicons-cart"></span>
            <span class="hozi-step-number">1</span>
            <?php _e('الطلبات المخصصة لي', 'hozi-akadly'); ?>
        </a>
        <?php if (Hozi_Akadly_Delivery_Tracking_Permissions::should_show_delivery_tracking_menu()): ?>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-delivery-tracking'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-truck"></span>
            <span class="hozi-step-number">2</span>
            <?php _e('متابعة التوصيل', 'hozi-akadly'); ?>
        </a>
        <?php else: ?>
        <span class="hozi-nav-tab hozi-nav-tab-disabled" title="<?php echo esc_attr(Hozi_Akadly_Delivery_Tracking_Permissions::get_access_denied_message()); ?>">
            <span class="dashicons dashicons-truck"></span>
            <span class="hozi-step-number">2</span>
            <?php _e('متابعة التوصيل', 'hozi-akadly'); ?>
            <span class="hozi-disabled-badge">معطل</span>
        </span>
        <?php endif; ?>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-archived'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-archive"></span>
            <span class="hozi-step-number">3</span>
            <?php _e('الطلبات المؤرشفة', 'hozi-akadly'); ?>
        </a>
    </div>

    <!-- Agent Stats -->
    <div class="hozi-agent-stats">
        <div class="hozi-stats-grid">
            <div class="hozi-stat-card">
                <h3><?php echo esc_html($agent_stats->pending ?? 0); ?></h3>
                <p><?php _e('في انتظار التأكيد', 'hozi-akadly'); ?></p>
            </div>
            <div class="hozi-stat-card confirmed">
                <h3><?php echo esc_html($agent_stats->confirmed ?? 0); ?></h3>
                <p><?php _e('تم التأكيد', 'hozi-akadly'); ?></p>
            </div>
            <div class="hozi-stat-card rejected">
                <h3><?php echo esc_html($agent_stats->rejected ?? 0); ?></h3>
                <p><?php _e('تم الرفض', 'hozi-akadly'); ?></p>
            </div>
            <div class="hozi-stat-card no-answer">
                <h3><?php echo esc_html($agent_stats->no_answer ?? 0); ?></h3>
                <p><?php _e('لم يرد', 'hozi-akadly'); ?></p>
            </div>
        </div>
    </div>

    <!-- Pending Orders -->
    <div class="hozi-orders-section">
        <div class="hozi-orders-header">
            <h2><?php _e('الطلبات المخصصة لي', 'hozi-akadly'); ?></h2>
            <?php if ($pagination_info && $pagination_info['total_orders'] > 0) : ?>
                <div class="hozi-pagination-info">
                    <?php printf(
                        __('عرض %d-%d من %d طلب', 'hozi-akadly'),
                        $pagination_info['showing_from'],
                        $pagination_info['showing_to'],
                        $pagination_info['total_orders']
                    ); ?>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!empty($pending_orders)) : ?>
            <div class="hozi-orders-grid">
                <?php foreach ($pending_orders as $assignment) :
                    $order = wc_get_order($assignment->order_id);
                    if (!$order) continue;

                    $billing_address = $order->get_formatted_billing_address();
                    $items = $order->get_items();
                ?>
                    <div class="hozi-order-card" data-order-id="<?php echo esc_attr($assignment->order_id); ?>">
                        <div class="hozi-order-header">
                            <h3><?php _e('طلب رقم:', 'hozi-akadly'); ?> #<?php echo esc_html($assignment->order_id); ?></h3>
                            <span class="hozi-order-date">
                                <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($assignment->order_date))); ?>
                            </span>
                        </div>

                        <!-- Phone Call Section -->
                        <div class="hozi-phone-call-section">
                            <div class="hozi-call-header">
                                <span class="dashicons dashicons-phone"></span>
                                <?php _e('جاهز للاتصال', 'hozi-akadly'); ?>
                            </div>
                            <div class="hozi-phone-display">
                                <?php echo esc_html($order->get_billing_phone()); ?>
                            </div>
                            <button type="button" class="hozi-call-button" onclick="window.location.href='tel:<?php echo esc_attr($order->get_billing_phone()); ?>'">
                                <span class="dashicons dashicons-phone"></span>
                                <?php _e('بدء المكالمة', 'hozi-akadly'); ?>
                            </button>
                            <div class="hozi-call-shortcut">
                                <?php _e('اضغط Space للاتصال السريع', 'hozi-akadly'); ?>
                            </div>
                        </div>

                        <div class="hozi-customer-info">
                            <h4><?php _e('معلومات العميل', 'hozi-akadly'); ?></h4>
                            <div class="hozi-info-grid">
                                <div class="hozi-info-item">
                                    <label><?php _e('الاسم:', 'hozi-akadly'); ?></label>
                                    <span><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></span>
                                </div>
                                <div class="hozi-info-item">
                                    <label><?php _e('البريد الإلكتروني:', 'hozi-akadly'); ?></label>
                                    <span><?php echo esc_html($order->get_billing_email()); ?></span>
                                </div>
                                <div class="hozi-info-item full-width">
                                    <label><?php _e('العنوان:', 'hozi-akadly'); ?></label>
                                    <span><?php echo wp_kses_post($billing_address); ?></span>
                                </div>
                            </div>
                        </div>

                        <?php
                        // Get shipping method information
                        $shipping_methods = $order->get_shipping_methods();
                        $shipping_method_name = '';
                        $shipping_cost = 0;

                        if (!empty($shipping_methods)) {
                            foreach ($shipping_methods as $shipping_method) {
                                $shipping_method_name = $shipping_method->get_method_title();
                                $shipping_cost = $shipping_method->get_total();
                                break;
                            }
                        }

                        if ($shipping_method_name) : ?>
                            <!-- Enhanced Shipping Info Section -->
                            <div class="hozi-shipping-highlight">
                                <div class="hozi-shipping-main">
                                    <div class="hozi-shipping-icon">
                                        <i class="dashicons dashicons-car"></i>
                                    </div>
                                    <div class="hozi-shipping-content">
                                        <div class="hozi-shipping-method-large">
                                            <?php echo esc_html($shipping_method_name); ?>
                                        </div>
                                        <?php if ($shipping_cost > 0) : ?>
                                            <div class="hozi-shipping-cost-large">
                                                <?php echo wc_price($shipping_cost); ?>
                                            </div>
                                        <?php else : ?>
                                            <div class="hozi-shipping-cost-large hozi-free">
                                                <?php _e('مجاني', 'hozi-akadly'); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="hozi-order-items">
                            <h4><?php _e('المنتجات المطلوبة', 'hozi-akadly'); ?></h4>
                            <div class="hozi-items-list">
                                <?php foreach ($items as $item) :
                                    $product = $item->get_product();
                                    $quantity = $item->get_quantity();
                                    $total = $item->get_total();
                                ?>
                                    <div class="hozi-item">
                                        <div class="hozi-item-details">
                                            <strong><?php echo esc_html($item->get_name()); ?></strong>
                                            <?php if ($product && $product->get_sku()) : ?>
                                                <span class="hozi-sku">(<?php echo esc_html($product->get_sku()); ?>)</span>
                                            <?php endif; ?>

                                            <?php
                                            // Display product variations/attributes
                                            $item_meta = $item->get_formatted_meta_data();
                                            if ($item_meta) {
                                                echo '<div class="hozi-item-meta">';
                                                foreach ($item_meta as $meta) {
                                                    echo '<span>' . wp_kses_post($meta->display_key . ': ' . $meta->display_value) . '</span>';
                                                }
                                                echo '</div>';
                                            }
                                            ?>
                                        </div>
                                        <div class="hozi-item-quantity">
                                            <?php _e('الكمية:', 'hozi-akadly'); ?> <?php echo esc_html($quantity); ?>
                                        </div>
                                        <div class="hozi-item-total">
                                            <?php echo wc_price($total); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="hozi-order-total">
                            <h4><?php _e('إجمالي الطلب:', 'hozi-akadly'); ?> <?php echo wc_price($order->get_total()); ?></h4>
                        </div>

                        <!-- Upsell Section -->
                        <div class="hozi-upsell-section">
                            <div class="hozi-upsell-header">
                                <span class="dashicons dashicons-chart-line"></span>
                                <?php _e('البيع الإضافي', 'hozi-akadly'); ?>
                            </div>

                            <div class="hozi-upsell-scripts">
                                <h5><?php _e('نصوص مقترحة:', 'hozi-akadly'); ?></h5>
                                <?php
                                $scripts = array(
                                    'هل تريد إضافة منتج مكمل بخصم خاص؟',
                                    'لدينا عرض خاص على المنتجات المشابهة',
                                    'يمكنك الحصول على شحن مجاني عند إضافة منتج آخر',
                                    'هل تحتاج أي إكسسوارات إضافية؟'
                                );
                                foreach ($scripts as $script) : ?>
                                    <div class="hozi-script-item" onclick="navigator.clipboard.writeText('<?php echo esc_js($script); ?>')">
                                        <?php echo esc_html($script); ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="hozi-upsell-result">
                                <button type="button" class="hozi-upsell-btn" data-result="success">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php _e('نجح البيع الإضافي', 'hozi-akadly'); ?>
                                </button>
                                <button type="button" class="hozi-upsell-btn" data-result="failed">
                                    <span class="dashicons dashicons-no"></span>
                                    <?php _e('لم ينجح البيع الإضافي', 'hozi-akadly'); ?>
                                </button>
                            </div>
                        </div>

                        <?php if ($order->get_customer_note()) : ?>
                            <div class="hozi-order-notes">
                                <h4><?php _e('ملاحظات العميل:', 'hozi-akadly'); ?></h4>
                                <p><?php echo esc_html($order->get_customer_note()); ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Navigation Arrows -->
                        <div class="hozi-order-navigation">
                            <button type="button" class="hozi-nav-arrow hozi-nav-prev" title="<?php _e('الطلب السابق', 'hozi-akadly'); ?>">
                                <span class="dashicons dashicons-arrow-up-alt2"></span>
                            </button>
                            <button type="button" class="hozi-nav-arrow hozi-nav-next" title="<?php _e('الطلب التالي', 'hozi-akadly'); ?>">
                                <span class="dashicons dashicons-arrow-down-alt2"></span>
                            </button>
                        </div>

                        <div class="hozi-confirmation-actions">
                            <form method="post" class="hozi-confirmation-form">
                                <input type="hidden" name="hozi_akadly_nonce" value="<?php echo wp_create_nonce('hozi_akadly_nonce'); ?>">
                                <input type="hidden" name="order_id" value="<?php echo esc_attr($assignment->order_id); ?>">
                                <input type="hidden" name="update_confirmation" value="1">

                                <div class="hozi-action-buttons">
                                    <button type="submit" name="status" value="confirmed" class="button button-primary hozi-btn-confirm">
                                        <span class="dashicons dashicons-yes"></span>
                                        <?php _e('تم التأكيد', 'hozi-akadly'); ?>
                                    </button>

                                    <button type="submit" name="status" value="no_answer" class="button hozi-btn-no-answer">
                                        <span class="dashicons dashicons-phone"></span>
                                        <?php _e('لم يرد', 'hozi-akadly'); ?>
                                    </button>

                                    <button type="submit" name="status" value="callback_later" class="button hozi-btn-callback">
                                        <span class="dashicons dashicons-clock"></span>
                                        <?php _e('إعادة الاتصال', 'hozi-akadly'); ?>
                                    </button>

                                    <button type="submit" name="status" value="rejected" class="button hozi-btn-reject">
                                        <span class="dashicons dashicons-no"></span>
                                        <?php _e('تم الرفض', 'hozi-akadly'); ?>
                                    </button>
                                </div>

                                <div class="hozi-notes-section">
                                    <label for="notes_<?php echo esc_attr($assignment->order_id); ?>">
                                        <?php _e('ملاحظات إضافية:', 'hozi-akadly'); ?>
                                    </label>
                                    <textarea
                                        name="notes"
                                        id="notes_<?php echo esc_attr($assignment->order_id); ?>"
                                        rows="3"
                                        placeholder="<?php _e('أضف أي ملاحظات حول المكالمة...', 'hozi-akadly'); ?>"
                                    ></textarea>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else : ?>
            <div class="hozi-no-orders">
                <div class="hozi-no-orders-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <h3><?php _e('لا توجد طلبات مخصصة لك حالياً', 'hozi-akadly'); ?></h3>
                <p><?php _e('ستظهر الطلبات الجديدة هنا عند تخصيصها لك', 'hozi-akadly'); ?></p>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if ($pagination_info && $pagination_info['total_pages'] > 1) : ?>
            <div class="hozi-pagination">
                <?php
                $current_page = $pagination_info['current_page'];
                $total_pages = $pagination_info['total_pages'];
                $base_url = admin_url('admin.php?page=hozi-akadly-my-orders');

                // Previous page
                if ($current_page > 1) {
                    $prev_url = add_query_arg('paged', $current_page - 1, $base_url);
                    echo '<a href="' . esc_url($prev_url) . '" class="hozi-pagination-btn hozi-pagination-prev">';
                    echo '<span class="dashicons dashicons-arrow-right-alt2"></span> ' . __('السابق', 'hozi-akadly');
                    echo '</a>';
                }

                // Page numbers
                echo '<div class="hozi-pagination-numbers">';
                for ($i = 1; $i <= $total_pages; $i++) {
                    $page_url = add_query_arg('paged', $i, $base_url);
                    $active_class = ($i == $current_page) ? ' hozi-pagination-active' : '';
                    echo '<a href="' . esc_url($page_url) . '" class="hozi-pagination-number' . $active_class . '">' . $i . '</a>';
                }
                echo '</div>';

                // Next page
                if ($current_page < $total_pages) {
                    $next_url = add_query_arg('paged', $current_page + 1, $base_url);
                    echo '<a href="' . esc_url($next_url) . '" class="hozi-pagination-btn hozi-pagination-next">';
                    echo __('التالي', 'hozi-akadly') . ' <span class="dashicons dashicons-arrow-left-alt2"></span>';
                    echo '</a>';
                }
                ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Notification Toggle Styles */
.hozi-notification-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.hozi-notification-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.hozi-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.hozi-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.hozi-notification-toggle input:checked + .hozi-toggle-slider {
    background-color: #4f46e5;
}

.hozi-notification-toggle input:checked + .hozi-toggle-slider:before {
    transform: translateX(26px);
}

/* Simple Header */
.hozi-simple-header {
    margin-bottom: 20px;
    padding: 15px 0;
    border-bottom: 2px solid #e1e5e9;
}

.hozi-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hozi-simple-header h1 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.hozi-system-guide-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.hozi-system-guide-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

.hozi-system-guide-btn .dashicons {
    font-size: 16px;
}

.hozi-guide-text {
    font-family: inherit;
}

/* Modal Styles */
.hozi-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.hozi-modal-content {
    background: white;
    border-radius: 15px;
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.hozi-modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px 30px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hozi-modal-header h2 {
    margin: 0;
    font-size: 1.5em;
    font-weight: 700;
}

.hozi-modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.hozi-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.hozi-modal-body {
    padding: 30px;
}

.hozi-guide-intro {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
    border-radius: 12px;
    border: 2px solid #e1e8ff;
}

.hozi-guide-intro p {
    margin: 10px 0;
    font-size: 1.1em;
    line-height: 1.6;
}

/* Step Cards */
.hozi-steps-container {
    display: grid;
    gap: 25px;
    margin-bottom: 30px;
}

.hozi-step-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hozi-step-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hozi-step-active {
    border-color: #667eea;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.hozi-step-header {
    background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
    padding: 20px 25px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.hozi-step-active .hozi-step-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.hozi-step-number {
    width: 50px;
    height: 50px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: 700;
    flex-shrink: 0;
}

.hozi-step-active .hozi-step-number {
    background: white;
    color: #667eea;
}

.hozi-step-title h3 {
    margin: 0 0 5px 0;
    font-size: 1.3em;
    font-weight: 700;
}

.hozi-step-subtitle {
    font-size: 0.9em;
    opacity: 0.8;
    font-weight: 500;
}

.hozi-step-content {
    padding: 25px;
}

.hozi-step-content ul {
    margin: 15px 0;
    padding-right: 20px;
}

.hozi-step-content li {
    margin: 10px 0;
    line-height: 1.6;
}

/* Action Examples */
.hozi-step-actions {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.hozi-action-examples {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.hozi-action-btn {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
    border: 2px solid transparent;
}

.hozi-action-confirm {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.hozi-action-no-answer {
    background: #e2e3e5;
    color: #383d41;
    border-color: #d6d8db;
}

.hozi-action-callback {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.hozi-action-reject {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

/* Delivery Statuses */
.hozi-delivery-statuses {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.hozi-status-badge {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 600;
}

.hozi-status-delivered {
    background: #d4edda;
    color: #155724;
}

.hozi-status-postponed {
    background: #fff3cd;
    color: #856404;
}

.hozi-status-exchange {
    background: #cce7ff;
    color: #004085;
}

.hozi-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

/* Step Tips */
.hozi-step-tip {
    margin-top: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #fff9e6, #fff3cd);
    border-radius: 10px;
    border-right: 4px solid #ffc107;
}

/* Guide Footer */
.hozi-guide-footer {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 2px solid #e9ecef;
}

.hozi-workflow-summary {
    margin-bottom: 25px;
    text-align: center;
}

.hozi-workflow-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.hozi-workflow-step {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 25px;
    border: 2px solid #e9ecef;
}

.hozi-workflow-number {
    width: 25px;
    height: 25px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    font-weight: 700;
}

.hozi-workflow-text {
    font-weight: 600;
    color: #333;
}

.hozi-workflow-arrow {
    font-size: 1.2em;
    color: #667eea;
    font-weight: 700;
}

.hozi-quick-tips {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 12px;
    border: 2px solid #e1e8ff;
}

.hozi-quick-tips h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.hozi-quick-tips ul {
    margin: 0;
    padding-right: 20px;
}

.hozi-quick-tips li {
    margin: 8px 0;
    line-height: 1.5;
}

.hozi-modal-footer {
    padding: 20px 30px;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
    text-align: center;
}

.hozi-modal-footer .button {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.hozi-modal-footer .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
    .hozi-header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .hozi-modal-content {
        margin: 10px;
        max-height: 95vh;
    }

    .hozi-modal-header,
    .hozi-modal-body,
    .hozi-modal-footer {
        padding: 20px;
    }

    .hozi-workflow-steps {
        flex-direction: column;
        gap: 10px;
    }

    .hozi-workflow-arrow {
        transform: rotate(90deg);
    }

    .hozi-action-examples,
    .hozi-delivery-statuses {
        flex-direction: column;
    }
}

/* Mobile-Friendly Notification Controls */
.hozi-notification-controls-mobile {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.hozi-notification-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.hozi-notification-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.hozi-notification-status-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: center;
}

.hozi-status-item {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    font-weight: 500;
}

.hozi-test-btn {
    font-size: 11px !important;
    padding: 4px 12px !important;
    border-radius: 15px !important;
    background: #4f46e5 !important;
    color: white !important;
    border: none !important;
    cursor: pointer;
}

.hozi-test-btn:hover {
    background: #3730a3 !important;
}

/* Navigation Tabs */
.hozi-nav-tabs {
    display: flex;
    gap: 0;
    margin: 20px 0;
    border-bottom: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    text-decoration: none;
    color: #666;
    background: #f8f9fa;
    border: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    flex-direction: column;
    flex: 1;
    justify-content: center;
}

.hozi-step-number {
    background: #0073aa;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}

.hozi-nav-tab-active .hozi-step-number {
    background: white;
    color: #4f46e5;
}

.hozi-nav-tab:hover {
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
}

.hozi-nav-tab-active {
    background: white !important;
    color: #4f46e5 !important;
    border-bottom-color: #4f46e5 !important;
    font-weight: 600;
}

.hozi-nav-tab-active::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
}

.hozi-nav-tab .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Order Navigation Arrows - Mobile Optimized */
.hozi-order-navigation {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: row;
    gap: 8px;
}

.hozi-nav-arrow {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
    opacity: 0.8;
}

.hozi-nav-arrow:hover {
    opacity: 1;
    transform: scale(1.05);
    box-shadow: 0 3px 12px rgba(79, 70, 229, 0.5);
}

.hozi-nav-arrow:active {
    transform: scale(0.95);
}

.hozi-nav-arrow .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.hozi-nav-arrow:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.5;
}

.hozi-nav-arrow:disabled:hover {
    transform: none;
    opacity: 0.5;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .hozi-order-navigation {
        bottom: 15px;
        right: 15px;
        gap: 6px;
    }

    .hozi-nav-arrow {
        width: 30px;
        height: 30px;
    }

    .hozi-nav-arrow .dashicons {
        font-size: 14px;
        width: 14px;
        height: 14px;
    }

    .hozi-simple-header h1 {
        font-size: 20px;
    }

    .hozi-notification-controls-mobile {
        padding: 12px;
    }

    .hozi-notification-status-row {
        justify-content: space-between;
    }

    .hozi-nav-tabs {
        flex-direction: column;
        gap: 0;
    }

    .hozi-nav-tab {
        padding: 12px 16px;
        text-align: center;
    }

    /* Hide navigation arrows on very small screens */
    @media (max-width: 480px) {
        .hozi-order-navigation {
            bottom: 10px;
            right: 10px;
        }

        .hozi-nav-arrow {
            width: 28px;
            height: 28px;
        }

        .hozi-nav-arrow .dashicons {
            font-size: 12px;
            width: 12px;
            height: 12px;
        }
    }

    /* Improve order cards on mobile */
    @media (max-width: 768px) {
        .hozi-order-card {
            margin-bottom: 15px;
            padding: 15px;
        }

        .hozi-phone-call-section {
            padding: 20px;
            margin-bottom: 20px;
        }

        .hozi-phone-display {
            font-size: 28px;
            margin: 12px 0;
        }

        .hozi-call-button {
            padding: 12px 24px !important;
            font-size: 14px !important;
        }

        .hozi-action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .hozi-action-buttons button {
            padding: 10px 8px !important;
            font-size: 12px !important;
        }
    }
}

/* Current Order Highlight */
.hozi-order-card.hozi-current-order {
    border: 3px solid #4f46e5;
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.2);
    transform: scale(1.02);
    position: relative;
}

.hozi-order-card.hozi-current-order::before {
    content: '👁️ الطلب الحالي';
    position: absolute;
    top: -15px;
    right: 20px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.hozi-agent-stats {
    margin-bottom: 30px;
}

/* Phone Call Section - Beautiful Design */
.hozi-phone-call-section {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 25px;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hozi-phone-call-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.hozi-call-header {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.hozi-phone-display {
    font-size: 32px;
    font-weight: bold;
    margin: 16px 0;
    direction: ltr;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1;
}

.hozi-call-button {
    background: #10b981 !important;
    border: none !important;
    border-radius: 50px !important;
    padding: 16px 32px !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 12px !important;
    margin-top: 16px !important;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4) !important;
    position: relative;
    z-index: 1;
}

.hozi-call-button:hover {
    background: #059669 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6) !important;
}

.hozi-call-button:active {
    transform: translateY(0) !important;
}

.hozi-call-button .dashicons {
    animation: ring 2s ease-in-out infinite;
}

@keyframes ring {
    0%, 100% { transform: rotate(0deg); }
    10%, 30% { transform: rotate(-10deg); }
    20% { transform: rotate(10deg); }
}

.hozi-call-shortcut {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 8px;
    position: relative;
    z-index: 1;
}

/* ===== ENHANCED SHIPPING HIGHLIGHT SECTION ===== */
.hozi-shipping-highlight {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 2px solid #4CAF50;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.hozi-shipping-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #4CAF50, #45a049, #4CAF50);
    background-size: 200% 100%;
    animation: gradientMove 3s ease-in-out infinite;
}

@keyframes gradientMove {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.hozi-shipping-main {
    display: flex;
    align-items: center;
    gap: 15px;
}

.hozi-shipping-icon {
    background: #4CAF50;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.hozi-shipping-icon i {
    font-size: 24px;
}

.hozi-shipping-content {
    flex: 1;
    text-align: center;
}

.hozi-shipping-method-large {
    font-size: 20px;
    font-weight: 700;
    color: #2e7d32;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.hozi-shipping-cost-large {
    font-size: 28px;
    font-weight: 800;
    color: #1b5e20;
    direction: ltr;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background: rgba(255,255,255,0.8);
    padding: 8px 16px;
    border-radius: 25px;
    display: inline-block;
    border: 2px solid rgba(76, 175, 80, 0.3);
}

.hozi-shipping-cost-large.hozi-free {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    border-color: rgba(255, 152, 0, 0.3);
}

/* Upsell Section */
.hozi-upsell-section {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    color: white;
}

.hozi-upsell-header {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.hozi-upsell-scripts h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    opacity: 0.9;
}

.hozi-upsell-scripts {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.hozi-script-item {
    background: rgba(255,255,255,0.2);
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.hozi-script-item:hover {
    background: rgba(255,255,255,0.3);
    transform: translateX(5px);
}

.hozi-script-item:last-child {
    margin-bottom: 0;
}

.hozi-upsell-result {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.hozi-upsell-btn {
    padding: 10px 15px !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    border-radius: 6px !important;
    background: transparent !important;
    color: white !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    font-size: 14px !important;
}

.hozi-upsell-btn:hover {
    background: rgba(255,255,255,0.2) !important;
}

.hozi-upsell-btn.success {
    background: #10b981 !important;
    border-color: #10b981 !important;
}

.hozi-upsell-btn.failed {
    background: #ef4444 !important;
    border-color: #ef4444 !important;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.hozi-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-stat-card.confirmed { border-left: 4px solid #4CAF50; }
.hozi-stat-card.rejected { border-left: 4px solid #f44336; }
.hozi-stat-card.no-answer { border-left: 4px solid #FF9800; }

.hozi-stat-card h3 {
    font-size: 2em;
    margin: 0 0 10px 0;
    color: #333;
}

.hozi-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.hozi-order-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.hozi-order-header h3 {
    margin: 0;
    color: #0073aa;
}

.hozi-order-date {
    color: #666;
    font-size: 14px;
}

.hozi-customer-info, .hozi-order-items, .hozi-order-notes {
    margin-bottom: 20px;
}

/* ===== CUSTOMER INFO SIMPLE BOX ===== */
.hozi-customer-info {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.hozi-customer-info h4 {
    margin: 0 0 12px 0;
    color: #555;
    font-size: 15px;
    font-weight: 600;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.hozi-order-items h4, .hozi-order-notes h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.hozi-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.hozi-info-item {
    display: flex;
    flex-direction: column;
    background: white;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
}

.hozi-info-item.full-width {
    grid-column: 1 / -1;
}

.hozi-info-item label {
    font-weight: 500;
    color: #777;
    font-size: 11px;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hozi-info-item span {
    color: #333;
    font-size: 14px;
    font-weight: 400;
}

.hozi-phone a {
    color: #0073aa;
    text-decoration: none;
    font-weight: bold;
}

.hozi-phone a:hover {
    text-decoration: underline;
}

.hozi-items-list {
    border: 1px solid #eee;
    border-radius: 4px;
}

.hozi-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 15px;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.hozi-item:last-child {
    border-bottom: none;
}

.hozi-item-meta {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.hozi-item-meta span {
    display: block;
}

.hozi-order-total {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 20px;
}

.hozi-order-total h4 {
    margin: 0;
    color: #0073aa;
    font-size: 18px;
}

.hozi-action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.hozi-action-buttons button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
}

.hozi-action-buttons button .dashicons {
    margin-left: 5px;
}

.hozi-btn-confirm {
    background: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
}

.hozi-btn-no-answer {
    background: #FF9800 !important;
    border-color: #FF9800 !important;
    color: white !important;
}

.hozi-btn-callback {
    background: #2196F3 !important;
    border-color: #2196F3 !important;
    color: white !important;
}

.hozi-btn-reject {
    background: #f44336 !important;
    border-color: #f44336 !important;
    color: white !important;
}

.hozi-notes-section {
    margin-top: 15px;
}

.hozi-notes-section label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.hozi-notes-section textarea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    resize: vertical;
}

.hozi-no-orders {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.hozi-no-orders-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.hozi-no-orders h3 {
    color: #666;
    margin-bottom: 10px;
}

.hozi-no-orders p {
    color: #999;
}

/* Orders Header */
.hozi-orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.hozi-orders-header h2 {
    margin: 0;
    color: #333;
    font-size: 20px;
}

.hozi-pagination-info {
    background: #f8f9fa;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    border: 1px solid #e9ecef;
}

/* Pagination Styles */
.hozi-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #e9ecef;
}

.hozi-pagination-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-pagination-btn:hover {
    background: #f8f9fa;
    border-color: #007cba;
    color: #007cba;
    text-decoration: none;
}

.hozi-pagination-numbers {
    display: flex;
    gap: 5px;
}

.hozi-pagination-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-pagination-number:hover {
    background: #f8f9fa;
    border-color: #007cba;
    color: #007cba;
    text-decoration: none;
}

.hozi-pagination-number.hozi-pagination-active {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
}

.hozi-pagination-number.hozi-pagination-active:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
}

@media (max-width: 768px) {
    .hozi-orders-grid {
        grid-template-columns: 1fr;
    }

    .hozi-info-grid {
        grid-template-columns: 1fr;
    }

    .hozi-action-buttons {
        grid-template-columns: 1fr 1fr;
    }

    .hozi-shipping-main {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .hozi-shipping-method-large {
        font-size: 18px;
    }

    .hozi-shipping-cost-large {
        font-size: 24px;
        padding: 6px 12px;
    }

    .hozi-info-grid {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }

    .hozi-info-item {
        padding: 8px !important;
    }

    .hozi-customer-info {
        padding: 12px !important;
        margin: 10px 0 !important;
    }

    .hozi-orders-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .hozi-pagination {
        flex-wrap: wrap;
        gap: 8px;
    }

    .hozi-pagination-btn {
        padding: 8px 12px;
        font-size: 14px;
    }

    .hozi-pagination-number {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

/* Statistics update animation */
.hozi-stat-updated {
    animation: statUpdate 0.6s ease-in-out;
    color: #00a32a !important;
}

@keyframes statUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Archive Animation */
.hozi-archived-order {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef) !important;
    border: 2px dashed #28a745 !important;
    opacity: 0.8;
    transform: scale(0.98);
    transition: all 0.8s ease;
    position: relative;
}

.hozi-archived-order::before {
    content: "🗂️ تم الأرشفة";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Ensure ajaxurl is available
    if (typeof ajaxurl === 'undefined') {
        var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
    }
    // Add confirmation for status updates with AJAX
    $('.hozi-confirmation-form button[type="submit"]').on('click', function(e) {
        e.preventDefault(); // Always prevent default form submission

        var $button = $(this);
        var $form = $button.closest('form');
        var status = $button.val();
        var orderId = $form.find('input[name="order_id"]').val();
        var notes = $form.find('textarea[name="notes"]').val();
        var confirmMessage = '';

        // Check if order is already confirmed for archive warning
        var currentStatus = $form.closest('.hozi-order-card').find('.hozi-status-badge').text().trim();
        var isAlreadyConfirmed = currentStatus.includes('تم التأكيد') || currentStatus.includes('confirmed');

        switch(status) {
            case 'confirmed':
                if (isAlreadyConfirmed) {
                    confirmMessage = '⚠️ تحذير: هذا الطلب مؤكد مسبقاً!\n\n' +
                                   '🗂️ التأكيد مرة أخرى سيؤدي إلى أرشفة الطلب تلقائياً وتحويله إلى "أكدلي - مكتمل"\n\n' +
                                   '✅ هل تريد المتابعة وأرشفة الطلب؟';
                } else {
                    confirmMessage = 'هل أنت متأكد من تأكيد هذا الطلب؟';
                }
                break;
            case 'rejected':
                confirmMessage = 'هل أنت متأكد من رفض هذا الطلب؟';
                break;
            case 'no_answer':
                confirmMessage = 'هل تأكدت من عدم رد العميل؟';
                break;
            case 'callback_later':
                confirmMessage = 'هل تريد تأجيل هذا الطلب لإعادة الاتصال لاحقاً؟';
                break;
        }

        if (confirmMessage && !confirm(confirmMessage)) {
            return false;
        }

        // Show loading state
        $button.prop('disabled', true).text('جاري التحديث...');

        // Get nonce from form
        var nonce = $form.find('input[name="hozi_akadly_nonce"]').val();

        // Validate nonce exists
        if (!nonce) {
            alert('خطأ في الأمان: لم يتم العثور على nonce');
            $button.prop('disabled', false);
            return;
        }

        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_update_confirmation',
                order_id: orderId,
                status: status,
                notes: notes,
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update statistics if provided
                    if (response.data && response.data.stats) {
                        updateAgentStatistics(response.data.stats);
                    }

                    // Check if this was an archive action
                    var message = response.data ? response.data.message : 'تم تحديث حالة الطلب بنجاح';
                    var isArchiveAction = message.includes('أرشفة') || message.includes('مكتمل');

                    if (isArchiveAction) {
                        // Special handling for archive action
                        alert('🗂️ تم أرشفة الطلب بنجاح!\n\nالطلب أصبح "أكدلي - مكتمل" وتم إزالته من قائمة التتبع.');

                        // Add archive animation
                        $form.closest('.hozi-order-card').addClass('hozi-archived-order');

                        // Wait for animation then reload
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        // Normal success message
                        alert(message);

                        // Reload page to show updated status
                        window.location.reload();
                    }
                } else {
                    alert('فشل في تحديث حالة الطلب: ' + (response.data ? response.data.message : 'خطأ غير معروف'));
                    // Re-enable button
                    $button.prop('disabled', false).text($button.data('original-text') || $button.text());
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', xhr.responseText);
                console.log('Status:', status);
                console.log('Error:', error);

                var errorMessage = 'حدث خطأ أثناء تحديث الحالة';
                if (xhr.responseText) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.data && response.data.message) {
                            errorMessage += ': ' + response.data.message;
                        }
                    } catch (e) {
                        errorMessage += ': ' + xhr.responseText.substring(0, 100);
                    }
                }

                alert(errorMessage);
                // Re-enable button
                $button.prop('disabled', false).text($button.data('original-text') || $button.text());
            }
        });
    });

    // Function to update agent statistics
    function updateAgentStatistics(stats) {
        if (!stats) return;

        // Update stat cards
        $('.hozi-stat-card').each(function() {
            var $card = $(this);
            var $number = $card.find('h3');

            if ($card.hasClass('confirmed') && stats.confirmed !== undefined) {
                $number.text(stats.confirmed);
            } else if ($card.hasClass('rejected') && stats.rejected !== undefined) {
                $number.text(stats.rejected);
            } else if ($card.hasClass('no-answer') && stats.no_answer !== undefined) {
                $number.text(stats.no_answer);
            } else if (!$card.hasClass('confirmed') && !$card.hasClass('rejected') && !$card.hasClass('no-answer') && stats.pending !== undefined) {
                // This is the pending card
                $number.text(stats.pending);
            }
        });

        // Add animation effect
        $('.hozi-stat-card h3').addClass('hozi-stat-updated');
        setTimeout(function() {
            $('.hozi-stat-card h3').removeClass('hozi-stat-updated');
        }, 1000);
    }

    // Upsell buttons functionality
    $('.hozi-upsell-btn').on('click', function() {
        var result = $(this).data('result');
        var orderId = $(this).closest('.hozi-order-card').data('order-id');

        // Remove active class from all buttons in this order
        $(this).closest('.hozi-upsell-result').find('.hozi-upsell-btn').removeClass('success failed');

        // Add appropriate class
        if (result === 'success') {
            $(this).addClass('success');
            saveUpsellResult(orderId, true);
        } else {
            $(this).addClass('failed');
            saveUpsellResult(orderId, false);
        }
    });

    // Save upsell result via AJAX
    function saveUpsellResult(orderId, successful) {
        // Get nonce from any form on the page
        var nonce = $('.hozi-confirmation-form input[name="hozi_akadly_nonce"]').first().val();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_save_upsell',
                order_id: orderId,
                successful: successful ? 1 : 0,
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Upsell result saved successfully');
                } else {
                    console.error('Failed to save upsell result');
                }
            },
            error: function() {
                console.error('AJAX error while saving upsell result');
            }
        });
    }

    // Copy script to clipboard
    $('.hozi-script-item').on('click', function() {
        var text = $(this).text();
        navigator.clipboard.writeText(text).then(function() {
            // Show temporary feedback
            var $item = $(this);
            var originalBg = $item.css('background');
            $item.css('background', 'rgba(16, 185, 129, 0.3)');
            setTimeout(function() {
                $item.css('background', originalBg);
            }, 500);
        }.bind(this));
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Space key for quick call (only if not in input/textarea)
        if (e.which === 32 && !$(e.target).is('input, textarea')) {
            e.preventDefault();
            var $firstCallButton = $('.hozi-call-button').first();
            if ($firstCallButton.length) {
                $firstCallButton.click();
            }
        }

        // Number keys for quick status update
        if (e.which >= 49 && e.which <= 52 && !$(e.target).is('input, textarea')) { // 1-4 keys
            e.preventDefault();
            var buttonIndex = e.which - 49; // 0-3
            var $buttons = $('.hozi-action-buttons button');
            if ($buttons.eq(buttonIndex).length) {
                $buttons.eq(buttonIndex).click();
            }
        }
    });

    // Add keyboard shortcuts help - Mobile optimized
    if ($('.hozi-order-card').length > 0) {
        $('body').append(`
            <div id="keyboard-shortcuts" style="position: fixed; bottom: 80px; left: 15px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 6px; font-size: 11px; z-index: 999; max-width: 200px; display: none;">
                <strong>🎮 اختصارات:</strong><br>
                <div style="margin-top: 6px; line-height: 1.3;">
                    <div>Space: اتصال 📞</div>
                    <div>↑↓: تنقل</div>
                    <div>1-4: أزرار التأكيد</div>
                </div>
            </div>
        `);

        // Show/hide shortcuts on long press or double tap
        let pressTimer;
        $(document).on('touchstart mousedown', function(e) {
            pressTimer = setTimeout(function() {
                $('#keyboard-shortcuts').fadeIn();
                setTimeout(function() {
                    $('#keyboard-shortcuts').fadeOut();
                }, 3000);
            }, 1000);
        }).on('touchend mouseup', function() {
            clearTimeout(pressTimer);
        });
    }

    // ===== AUDIO NOTIFICATIONS SYSTEM =====
    let lastOrderCount = $('.hozi-order-card').length;
    let notificationSound = null;
    let isPageVisible = true;
    let notificationsEnabled = true;
    let checkInterval = null;

    // Create audio notification
    function createNotificationSound() {
        // Create audio context for notification sound
        if (typeof(AudioContext) !== "undefined" || typeof(webkitAudioContext) !== "undefined") {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            const audioContext = new AudioContext();

            return function playNotification() {
                // Create a pleasant notification sound
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // Pleasant notification tone
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
            };
        }
        return null;
    }

    // Initialize notification sound
    notificationSound = createNotificationSound();

    // Track page visibility
    document.addEventListener('visibilitychange', function() {
        isPageVisible = !document.hidden;
    });

    // Check for new orders every 30 seconds
    function checkForNewOrders() {
        if (!notificationsEnabled) return;

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'hozi_check_new_orders',
                nonce: '<?php echo wp_create_nonce('hozi_check_new_orders'); ?>'
            },
            success: function(response) {
                if (response.success && response.data.count > lastOrderCount) {
                    // New order detected!
                    showNewOrderNotification(response.data.count - lastOrderCount);
                    lastOrderCount = response.data.count;

                    // Auto-refresh page after 3 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                }
            }
        });
    }

    // Show new order notification
    function showNewOrderNotification(newOrdersCount) {
        if (!notificationsEnabled) return;

        // Play sound
        if (notificationSound) {
            notificationSound();
        }

        // Show visual notification
        const notification = $(`
            <div class="hozi-new-order-notification" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                color: white;
                padding: 20px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
                z-index: 10000;
                animation: slideInRight 0.5s ease-out;
                min-width: 300px;
                text-align: center;
            ">
                <div style="font-size: 24px; margin-bottom: 10px;">🔔</div>
                <div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">
                    طلب جديد مخصص لك!
                </div>
                <div style="font-size: 14px; opacity: 0.9;">
                    ${newOrdersCount} طلب${newOrdersCount > 1 ? 'ات' : ''} جديد${newOrdersCount > 1 ? 'ة' : ''}
                </div>
                <div style="font-size: 12px; margin-top: 8px; opacity: 0.8;">
                    سيتم تحديث الصفحة خلال 3 ثوان...
                </div>
            </div>
        `);

        $('body').append(notification);

        // Remove notification after 5 seconds
        setTimeout(function() {
            notification.fadeOut(500, function() {
                $(this).remove();
            });
        }, 5000);

        // Browser notification if supported and page not visible
        if (!isPageVisible && 'Notification' in window) {
            if (Notification.permission === 'granted') {
                new Notification('أكدلي - طلب جديد', {
                    body: `لديك ${newOrdersCount} طلب${newOrdersCount > 1 ? 'ات' : ''} جديد${newOrdersCount > 1 ? 'ة' : ''} للتأكيد`,
                    icon: '/wp-admin/images/wordpress-logo.svg',
                    tag: 'hozi-new-order'
                });
            }
        }
    }

    // Request notification permission and setup service worker
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(function(permission) {
            if (permission === 'granted') {
                console.log('✅ تم منح إذن الإشعارات');
                setupBackgroundNotifications();
            }
        });
    } else if (Notification.permission === 'granted') {
        setupBackgroundNotifications();
    }

    // Setup background notifications
    function setupBackgroundNotifications() {
        // Register service worker for background notifications
        if ('serviceWorker' in navigator) {
            // Create inline service worker
            const swCode = `
                self.addEventListener('message', function(event) {
                    if (event.data.type === 'CHECK_ORDERS') {
                        // Perform background check
                        fetch(event.data.ajaxUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams({
                                action: 'hozi_check_new_orders',
                                nonce: event.data.nonce
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.data.count > event.data.lastCount) {
                                // Show notification
                                self.registration.showNotification('أكدلي - طلب جديد!', {
                                    body: 'لديك ' + (data.data.count - event.data.lastCount) + ' طلب جديد للتأكيد',
                                    icon: '/wp-admin/images/wordpress-logo.svg',
                                    badge: '/wp-admin/images/wordpress-logo.svg',
                                    tag: 'hozi-new-order',
                                    requireInteraction: true,
                                    actions: [
                                        {
                                            action: 'view',
                                            title: 'عرض الطلبات'
                                        }
                                    ]
                                });

                                // Send message back to main thread
                                event.ports[0].postMessage({
                                    type: 'NEW_ORDERS',
                                    count: data.data.count
                                });
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في فحص الطلبات:', error);
                        });
                    }
                });

                self.addEventListener('notificationclick', function(event) {
                    event.notification.close();

                    if (event.action === 'view') {
                        // Open the agent dashboard
                        event.waitUntil(
                            clients.openWindow('/wp-admin/admin.php?page=hozi-akadly-my-orders')
                        );
                    }
                });
            `;

            const blob = new Blob([swCode], { type: 'application/javascript' });
            const swUrl = URL.createObjectURL(blob);

            navigator.serviceWorker.register(swUrl)
                .then(function(registration) {
                    console.log('✅ تم تسجيل Service Worker للإشعارات الخلفية');
                    $('#background-status').html('🌐 الإشعارات الخلفية مفعلة').css('color', '#28a745');

                    // Setup background checking
                    if (registration.active) {
                        setupBackgroundChecking(registration);
                    } else {
                        registration.addEventListener('updatefound', function() {
                            registration.installing.addEventListener('statechange', function() {
                                if (this.state === 'activated') {
                                    setupBackgroundChecking(registration);
                                }
                            });
                        });
                    }
                })
                .catch(function(error) {
                    console.error('فشل في تسجيل Service Worker:', error);
                    $('#background-status').html('⚠️ الإشعارات الخلفية غير متاحة').css('color', '#ffc107');
                    // 🚀 FALLBACK: Use regular checking if service worker fails
                    startNotificationChecking();
                });
        }
    }

    // Setup background checking with service worker
    function setupBackgroundChecking(registration) {
        setInterval(function() {
            if (!document.hidden && notificationsEnabled) {
                // Regular foreground check
                checkForNewOrders();
            } else if (notificationsEnabled) {
                // Background check via service worker
                const channel = new MessageChannel();
                channel.port1.onmessage = function(event) {
                    if (event.data.type === 'NEW_ORDERS') {
                        lastOrderCount = event.data.count;
                    }
                };

                registration.active.postMessage({
                    type: 'CHECK_ORDERS',
                    ajaxUrl: ajaxurl,
                    nonce: '<?php echo wp_create_nonce('hozi_check_new_orders'); ?>',
                    lastCount: lastOrderCount
                }, [channel.port2]);
            }
        }, 30000); // Check every 30 seconds
    }

    // Legacy checking system (fallback)
    function startNotificationChecking() {
        if (checkInterval) clearInterval(checkInterval);
        checkInterval = setInterval(checkForNewOrders, 30000);
    }

    function stopNotificationChecking() {
        if (checkInterval) {
            clearInterval(checkInterval);
            checkInterval = null;
        }
    }

    // 🚀 FIXED: Initialize notification system with proper status updates
    function initializeNotificationSystem() {
        console.log('🔔 Initializing notification system...');

        // Update status immediately
        $('#background-status').html('🔄 جاري فحص دعم الإشعارات...').css('color', '#007cba');

        // Check browser support
        if (!('Notification' in window)) {
            $('#background-status').html('❌ المتصفح لا يدعم الإشعارات').css('color', '#dc3545');
            console.log('❌ Browser does not support notifications');
            return;
        }

        console.log('✅ Browser supports notifications');
        console.log('📋 Current permission:', Notification.permission);

        // Check notification permission
        if (Notification.permission === 'denied') {
            $('#background-status').html('❌ تم رفض إذن الإشعارات').css('color', '#dc3545');
            console.log('❌ Notification permission denied');
            return;
        }

        if (Notification.permission === 'default') {
            $('#background-status').html('🔒 انقر "طلب الإذن" لتفعيل الإشعارات').css('color', '#ff9800');
            $('#request-permission-btn').show();
            console.log('⚠️ Notification permission not requested yet');
            return;
        }

        // If permission is granted
        if (Notification.permission === 'granted') {
            $('#background-status').html('✅ الإشعارات مفعلة ومدعومة').css('color', '#28a745');
            console.log('✅ Notifications are enabled and supported');

            // Try to setup service worker for background notifications
            if ('serviceWorker' in navigator) {
                setupBackgroundNotifications();
            } else {
                console.log('⚠️ Service Worker not supported, using regular notifications only');
            }
        }
    }

    // Initialize the notification system
    setTimeout(initializeNotificationSystem, 500);

    // Handle notification toggle
    $('#notification-toggle').on('change', function() {
        notificationsEnabled = $(this).is(':checked');
        const $status = $('#notification-status');

        if (notificationsEnabled) {
            $status.html('✅ مفعلة').css('color', '#28a745');
            startNotificationChecking();

            // Save preference
            localStorage.setItem('hozi_notifications_enabled', 'true');
        } else {
            $status.html('❌ معطلة').css('color', '#dc3545');
            stopNotificationChecking();

            // Save preference
            localStorage.setItem('hozi_notifications_enabled', 'false');
        }
    });

    // Load saved preference
    const savedPreference = localStorage.getItem('hozi_notifications_enabled');
    if (savedPreference === 'false') {
        $('#notification-toggle').prop('checked', false).trigger('change');
    }

    // 🚀 FIXED: Test notification button with working sound
    $('#test-notification-btn').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.text();

        console.log('🔊 Testing notifications...');
        console.log('📋 Notifications enabled:', notificationsEnabled);
        console.log('📋 Notification permission:', Notification.permission);

        $btn.text('🔊 جاري الاختبار...').prop('disabled', true);

        // Test sound first
        try {
            // Create audio element for notification sound
            const audio = new Audio();
            audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
            audio.volume = 0.5;
            audio.play().then(() => {
                console.log('✅ Sound played successfully');
            }).catch(error => {
                console.log('⚠️ Sound failed, trying alternative:', error);
                // Alternative: use Web Audio API
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);

                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.3);

                    console.log('✅ Web Audio API sound played');
                } catch (webAudioError) {
                    console.log('❌ All sound methods failed:', webAudioError);
                }
            });
        } catch (error) {
            console.log('❌ Audio creation failed:', error);
        }

        // Test visual notification
        showNewOrderNotification(1);

        // Test desktop notification if permission granted
        if (Notification.permission === 'granted') {
            new Notification('🧪 اختبار الإشعارات - أكدلي', {
                body: 'هذا اختبار للتأكد من عمل الإشعارات بشكل صحيح',
                icon: '<?php echo HOZI_AKADLY_PLUGIN_URL; ?>assets/images/icon-128x128.png'
            });
            console.log('✅ Desktop notification sent');
        } else {
            console.log('⚠️ Desktop notification permission not granted');
        }

        // Reset button after 2 seconds
        setTimeout(function() {
            $btn.text(originalText).prop('disabled', false);
            console.log('🔊 Test completed');
        }, 2000);
    });

    // 🚀 FIXED: Request permission button
    $('#request-permission-btn').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.text();

        console.log('🔔 User clicked request permission');

        $btn.text('🔄 جاري طلب الإذن...').prop('disabled', true);

        Notification.requestPermission().then(function(permission) {
            console.log('📋 Permission result:', permission);

            if (permission === 'granted') {
                $('#background-status').html('✅ الإشعارات مفعلة ومدعومة').css('color', '#28a745');
                $btn.hide();

                // Test notification
                setTimeout(() => {
                    new Notification('🎉 تم تفعيل الإشعارات!', {
                        body: 'ستتلقى إشعارات عند وصول طلبات جديدة',
                        icon: '<?php echo HOZI_AKADLY_PLUGIN_URL; ?>assets/images/icon-128x128.png'
                    });
                }, 500);

                // Setup background notifications
                if ('serviceWorker' in navigator) {
                    setupBackgroundNotifications();
                }
            } else {
                $('#background-status').html('❌ تم رفض الإشعارات').css('color', '#dc3545');
                $btn.text(originalText).prop('disabled', false);
            }
        }).catch(function(error) {
            console.error('❌ Error requesting permission:', error);
            $btn.text(originalText).prop('disabled', false);
        });
    });

    // ===== ORDER NAVIGATION SYSTEM =====
    let currentOrderIndex = 0;
    let $orderCards = $('.hozi-order-card');

    function updateNavigationButtons() {
        const $prevBtn = $('.hozi-nav-prev');
        const $nextBtn = $('.hozi-nav-next');

        // Disable/enable buttons based on position
        $prevBtn.prop('disabled', currentOrderIndex === 0);
        $nextBtn.prop('disabled', currentOrderIndex >= $orderCards.length - 1);

        // Update button text with current position
        $prevBtn.attr('title', `الطلب السابق (${currentOrderIndex}/${$orderCards.length})`);
        $nextBtn.attr('title', `الطلب التالي (${currentOrderIndex + 2}/${$orderCards.length})`);
    }

    function scrollToOrder(index) {
        if (index >= 0 && index < $orderCards.length) {
            currentOrderIndex = index;
            const $targetCard = $orderCards.eq(index);

            // Smooth scroll to the order
            $('html, body').animate({
                scrollTop: $targetCard.offset().top - 100
            }, 500);

            // Highlight the current order
            $orderCards.removeClass('hozi-current-order');
            $targetCard.addClass('hozi-current-order');

            updateNavigationButtons();
        }
    }

    // Navigation button events
    $('.hozi-nav-prev').on('click', function() {
        if (currentOrderIndex > 0) {
            scrollToOrder(currentOrderIndex - 1);
        }
    });

    $('.hozi-nav-next').on('click', function() {
        if (currentOrderIndex < $orderCards.length - 1) {
            scrollToOrder(currentOrderIndex + 1);
        }
    });

    // Keyboard navigation
    $(document).on('keydown', function(e) {
        // Only if not in input/textarea
        if (!$(e.target).is('input, textarea')) {
            if (e.which === 38) { // Up arrow
                e.preventDefault();
                $('.hozi-nav-prev').click();
            } else if (e.which === 40) { // Down arrow
                e.preventDefault();
                $('.hozi-nav-next').click();
            }
        }
    });

    // Initialize navigation if there are orders
    if ($orderCards.length > 0) {
        updateNavigationButtons();

        // Set first order as current
        $orderCards.first().addClass('hozi-current-order');

        // Show navigation only if there are multiple orders
        if ($orderCards.length > 1) {
            $('.hozi-order-navigation').show();
        } else {
            $('.hozi-order-navigation').hide();
        }
    } else {
        $('.hozi-order-navigation').hide();
    }

    // Add notification animation CSS
    $('<style>').text(`
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .hozi-new-order-notification {
            animation: slideInRight 0.5s ease-out;
        }
    `).appendTo('head');
});

// System Guide Modal Functions
function openSystemGuide() {
    document.getElementById('hozi-system-guide-modal').style.display = 'flex';
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeSystemGuide() {
    document.getElementById('hozi-system-guide-modal').style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scrolling
}

// Close modal when clicking outside or pressing Escape
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('hozi-system-guide-modal');
    if (modal) {
        // Close when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeSystemGuide();
            }
        });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSystemGuide();
        }
    });
});

/* Disabled Tab Styles */
.hozi-nav-tab-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    position: relative;
    background: #f5f5f5 !important;
    color: #999 !important;
    border-color: #ddd !important;
}

.hozi-nav-tab-disabled:hover {
    background: #f5f5f5 !important;
    color: #999 !important;
    transform: none !important;
}

.hozi-disabled-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    font-size: 9px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

@media (max-width: 768px) {
    .hozi-disabled-badge {
        font-size: 8px;
        padding: 1px 3px;
    }
}
</script>

<?php
// Include messaging widget
include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/messaging-widget.php';
?>
