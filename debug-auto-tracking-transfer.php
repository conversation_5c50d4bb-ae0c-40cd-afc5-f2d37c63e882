<?php
/**
 * Debug Auto Tracking Transfer
 * 
 * This script diagnoses why confirmed orders aren't being transferred to the tracking system.
 * Place this file in your WordPress root directory and access via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🔍 تشخيص نقل الطلبات لمتابعة التوصيل - Akadly</h1>";

global $wpdb;

// Check environment
echo "<h2>1️⃣ فحص البيئة:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>WordPress Version: " . get_bloginfo('version') . "</p>";
if (class_exists('WooCommerce')) {
    echo "<p>WooCommerce Version: " . WC()->version . "</p>";
}
echo "<p>Database Prefix: " . $wpdb->prefix . "</p>";

// Check necessary classes
echo "<h2>2️⃣ فحص الكلاسات الضرورية:</h2>";
$required_classes = array(
    'Hozi_Akadly_Order_Distributor',
    'Hozi_Akadly_Order_Tracker',
    'Hozi_Akadly_Delivery_Tracking_Permissions',
    'Hozi_Akadly_Database'
);

foreach ($required_classes as $class) {
    echo "<p>" . $class . ": " . (class_exists($class) ? '✅ موجود' : '❌ غير موجود') . "</p>";
}

// Check database tables
echo "<h2>3️⃣ فحص جداول قاعدة البيانات:</h2>";
$required_tables = array(
    'hozi_order_assignments',
    'hozi_order_tracking',
    'hozi_agents'
);

foreach ($required_tables as $table) {
    $table_name = $wpdb->prefix . $table;
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    echo "<p>" . $table_name . ": " . ($table_exists ? '✅ موجود' : '❌ غير موجود') . "</p>";
    
    if ($table_exists && $table == 'hozi_order_tracking') {
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        echo "<p>هيكل جدول التتبع:</p>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column->Field} ({$column->Type})</li>";
        }
        echo "</ul>";
    }
}

// Check delivery tracking settings
echo "<h2>4️⃣ فحص إعدادات متابعة التوصيل:</h2>";
$tracking_enabled = get_option('hozi_akadly_enable_delivery_tracking', 'yes');
$access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

echo "<p>تفعيل نظام متابعة التوصيل: " . ($tracking_enabled === 'yes' ? '✅ مفعل' : '❌ معطل') . "</p>";
echo "<p>صلاحيات الوصول: " . $access_control . "</p>";

// Find problematic orders
echo "<h2>5️⃣ فحص الطلبات المؤكدة بدون تتبع:</h2>";
$problematic_orders = $wpdb->get_results("
    SELECT oa.order_id, oa.agent_id, oa.confirmation_status, oa.confirmed_at, 
           a.name as agent_name, u.display_name as user_name
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    LEFT JOIN {$wpdb->prefix}users u ON a.user_id = u.ID
    LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
    WHERE oa.confirmation_status = 'confirmed'
    AND ot.id IS NULL
    ORDER BY oa.confirmed_at DESC
    LIMIT 10
");

if ($problematic_orders) {
    echo "<p>وجدنا " . count($problematic_orders) . " طلبات مؤكدة لم تنتقل إلى نظام التتبع:</p>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>الطلب</th>";
    echo "<th style='padding: 8px;'>الوكيل</th>";
    echo "<th style='padding: 8px;'>المستخدم</th>";
    echo "<th style='padding: 8px;'>تاريخ التأكيد</th>";
    echo "<th style='padding: 8px;'>إجراءات</th>";
    echo "</tr>";
    
    foreach ($problematic_orders as $order) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$order->order_id}</td>";
        echo "<td style='padding: 8px;'>{$order->agent_name} (ID: {$order->agent_id})</td>";
        echo "<td style='padding: 8px;'>{$order->user_name}</td>";
        echo "<td style='padding: 8px;'>{$order->confirmed_at}</td>";
        echo "<td style='padding: 8px;'><a href='?test_transfer=1&order_id={$order->order_id}&agent_id={$order->agent_id}' style='background: #0073aa; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px;'>اختبار النقل</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لم نجد أي طلبات مؤكدة بدون تتبع - وهذا يعني أن المشكلة قد تكون في شيء آخر.</p>";
}

// Test auto transfer function
if (isset($_GET['test_transfer']) && $_GET['test_transfer'] == '1' && isset($_GET['order_id']) && isset($_GET['agent_id'])) {
    echo "<h2>6️⃣ اختبار دالة النقل التلقائي:</h2>";
    
    $test_order_id = intval($_GET['order_id']);
    $test_agent_id = intval($_GET['agent_id']);
    
    echo "<p>🎯 اختبار النقل التلقائي للطلب {$test_order_id} (الوكيل: {$test_agent_id})</p>";
    
    if (class_exists('Hozi_Akadly_Order_Distributor')) {
        // Enable detailed error logging
        if (!defined('WP_DEBUG')) define('WP_DEBUG', true);
        if (!defined('WP_DEBUG_LOG')) define('WP_DEBUG_LOG', true);
        if (!defined('WP_DEBUG_DISPLAY')) define('WP_DEBUG_DISPLAY', true);
        
        echo "<p>📝 تفعيل سجل الأخطاء التفصيلي...</p>";
        
        try {
            $distributor = new Hozi_Akadly_Order_Distributor();
            
            // Use reflection to access private method
            $reflection = new ReflectionClass($distributor);
            $method = $reflection->getMethod('auto_transfer_to_tracking');
            $method->setAccessible(true);
            
            echo "<p>🔄 تشغيل auto_transfer_to_tracking للطلب {$test_order_id}...</p>";
            
            ob_start();
            $method->invoke($distributor, $test_order_id, $test_agent_id);
            $output = ob_get_clean();
            
            echo "<p>🔍 مخرجات الدالة:</p>";
            echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;'>";
            echo $output ? htmlspecialchars($output) : 'لا توجد مخرجات مباشرة';
            echo "</pre>";
            
            // Check if tracking record was created
            $tracking_record = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $test_order_id
            ));
            
            if ($tracking_record) {
                echo "<p style='color: green;'>✅ تم إنشاء سجل التتبع بنجاح!</p>";
                echo "<p>معرف السجل: {$tracking_record->id}</p>";
                echo "<p>الحالة: {$tracking_record->status}</p>";
                echo "<p>تاريخ الإنشاء: {$tracking_record->created_at}</p>";
            } else {
                echo "<p style='color: red;'>❌ لم يتم إنشاء سجل التتبع</p>";
            }
            
            // Check error log
            $log_file = WP_CONTENT_DIR . '/debug.log';
            if (file_exists($log_file)) {
                $log_content = file_get_contents($log_file);
                $lines = explode("\n", $log_content);
                $recent_logs = array_slice($lines, -20);
                
                echo "<p>📜 آخر سجلات الأخطاء:</p>";
                echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;'>";
                foreach ($recent_logs as $line) {
                    if (strpos($line, 'Hozi Akadly') !== false) {
                        echo "<p style='margin: 5px 0; font-family: monospace; font-size: 12px;'>" . esc_html($line) . "</p>";
                    }
                }
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
            echo "<p>الملف: " . $e->getFile() . " السطر: " . $e->getLine() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    } else {
        echo "<p style='color: red;'>❌ كلاس Hozi_Akadly_Order_Distributor غير موجود</p>";
    }
}

// Check database-related functions
echo "<h2>7️⃣ فحص دوال قاعدة البيانات:</h2>";
if (class_exists('Hozi_Akadly_Database')) {
    echo "<p>✅ كلاس Hozi_Akadly_Database موجود</p>";
    
    // Test if the necessary methods exist
    $methods = array(
        'log_action',
        'update_agent_stats',
        'ensure_tables_exist'
    );
    
    foreach ($methods as $method) {
        echo "<p>دالة {$method}: " . (method_exists('Hozi_Akadly_Database', $method) ? '✅ موجودة' : '❌ غير موجودة') . "</p>";
    }
    
    // Check for the ensure_tables_exist method
    if (method_exists('Hozi_Akadly_Database', 'ensure_tables_exist')) {
        echo "<p>🔄 تشغيل ensure_tables_exist لضمان وجود جميع الجداول...</p>";
        try {
            Hozi_Akadly_Database::ensure_tables_exist();
            echo "<p style='color: green;'>✅ تم تشغيل الدالة بنجاح</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ كلاس Hozi_Akadly_Database غير موجود</p>";
}

// Check hooks and actions
echo "<h2>8️⃣ فحص الهوكات والإجراءات:</h2>";
echo "<p>هذه قائمة بالهوكات المهمة لنظام متابعة التوصيل:</p>";

$hooks = array(
    'hozi_akadly_order_confirmation_updated' => 'يتم تشغيله عند تحديث حالة تأكيد الطلب',
    'hozi_akadly_order_tracking_updated' => 'يتم تشغيله عند تحديث حالة تتبع الطلب',
    'woocommerce_order_status_changed' => 'يتم تشغيله عند تغيير حالة طلب WooCommerce'
);

echo "<ul>";
foreach ($hooks as $hook => $description) {
    echo "<li><strong>{$hook}</strong>: {$description}</li>";
}
echo "</ul>";

// Check permissions for current user and any test agents
echo "<h2>9️⃣ فحص صلاحيات المستخدمين:</h2>";

echo "<p>المستخدم الحالي: " . wp_get_current_user()->display_name . " (ID: " . get_current_user_id() . ")</p>";

// Current user permissions
if (class_exists('Hozi_Akadly_Delivery_Tracking_Permissions')) {
    $can_access = Hozi_Akadly_Delivery_Tracking_Permissions::can_access_delivery_tracking();
    echo "<p>هل يمكن للمستخدم الحالي الوصول لمتابعة التوصيل؟ " . ($can_access ? '✅ نعم' : '❌ لا') . "</p>";
}

// Test access for sample agents
$test_agents = $wpdb->get_results("
    SELECT a.id, a.name, a.user_id, u.display_name
    FROM {$wpdb->prefix}hozi_agents a
    LEFT JOIN {$wpdb->prefix}users u ON a.user_id = u.ID
    WHERE a.is_active = 1
    LIMIT 3
");

if ($test_agents) {
    echo "<p>فحص صلاحيات لعينة من الوكلاء:</p>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>الوكيل</th>";
    echo "<th style='padding: 8px;'>المستخدم</th>";
    echo "<th style='padding: 8px;'>يمكن الوصول للتتبع؟</th>";
    echo "</tr>";
    
    foreach ($test_agents as $agent) {
        $agent_can_access = Hozi_Akadly_Delivery_Tracking_Permissions::can_access_delivery_tracking($agent->user_id);
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$agent->name} (ID: {$agent->id})</td>";
        echo "<td style='padding: 8px;'>{$agent->display_name} (ID: {$agent->user_id})</td>";
        echo "<td style='padding: 8px;'>" . ($agent_can_access ? '✅ نعم' : '❌ لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Display a conclusion
echo "<h2>🔍 الاستنتاج والتوصيات:</h2>";

$issues = array();

if (!class_exists('Hozi_Akadly_Order_Distributor')) {
    $issues[] = "❌ كلاس Hozi_Akadly_Order_Distributor غير موجود";
}

if (!class_exists('Hozi_Akadly_Delivery_Tracking_Permissions')) {
    $issues[] = "❌ كلاس Hozi_Akadly_Delivery_Tracking_Permissions غير موجود";
}

$table_name = $wpdb->prefix . 'hozi_order_tracking';
$tracking_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
if (!$tracking_table_exists) {
    $issues[] = "❌ جدول hozi_order_tracking غير موجود";
}

if ($tracking_enabled !== 'yes') {
    $issues[] = "❌ نظام متابعة التوصيل معطل في الإعدادات";
}

if (empty($issues)) {
    echo "<p style='background: #e8f5e9; padding: 10px; border-radius: 4px;'>";
    echo "<strong>✅ لم نجد مشاكل واضحة في النظام.</strong> نوصي بما يلي:";
    echo "<ul>";
    echo "<li>استخدام زر اختبار النقل لطلب واحد على الأقل من الطلبات المؤكدة</li>";
    echo "<li>التحقق من سجلات الأخطاء بعد الاختبار</li>";
    echo "<li>إعادة تفعيل الإضافة في حالة استمرار المشكلة</li>";
    echo "</ul>";
    echo "</p>";
} else {
    echo "<p style='background: #ffebee; padding: 10px; border-radius: 4px;'>";
    echo "<strong>❌ وجدنا بعض المشاكل التي قد تسبب هذه المشكلة:</strong>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>{$issue}</li>";
    }
    echo "</ul>";
    echo "<strong>الحلول المقترحة:</strong>";
    echo "<ol>";
    if (!$tracking_table_exists) {
        echo "<li>قم بإعادة تفعيل الإضافة لإنشاء جدول التتبع</li>";
    }
    if ($tracking_enabled !== 'yes') {
        echo "<li>قم بتفعيل نظام متابعة التوصيل من الإعدادات</li>";
    }
    echo "<li>استخدم زر اختبار النقل لطلب واحد لمعرفة المزيد من التفاصيل</li>";
    echo "</ol>";
    echo "</p>";
}

echo "<hr>";
echo "<p><strong>تم الانتهاء من التشخيص</strong> - " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل</a></p>";
?>
