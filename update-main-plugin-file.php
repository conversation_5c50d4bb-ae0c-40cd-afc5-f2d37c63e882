<?php
/**
 * Update Main Plugin File
 * This updates the main plugin file to use the new messaging system
 */

echo "<h1>🔄 تحديث الملف الرئيسي للإضافة</h1>";

// Read the current main plugin file
$main_file_path = dirname(__FILE__) . '/includes/class-hozi-akadly.php';

if (!file_exists($main_file_path)) {
    echo "❌ لم يتم العثور على الملف الرئيسي<br>";
    exit;
}

$current_content = file_get_contents($main_file_path);

// Create the updated content
$updated_content = str_replace(
    '        // Initialize messaging system
        new Hozi_Akadly_Messaging_System();',
    '        // Initialize new messaging system V2
        if (class_exists(\'Hozi_Akadly_Messaging_System_V2\')) {
            new Hozi_Akadly_Messaging_System_V2();
        }
        
        // Initialize admin stats tracker
        if (class_exists(\'Hozi_Akadly_Admin_Stats_Tracker\')) {
            new Hozi_Akadly_Admin_Stats_Tracker();
        }
        
        // Initialize admin as agent V2
        if (class_exists(\'Hozi_Akadly_Admin_As_Agent_V2\')) {
            new Hozi_Akadly_Admin_As_Agent_V2();
        }',
    $current_content
);

// Add includes for new classes at the top
$includes_to_add = "
// Include new messaging system classes
require_once plugin_dir_path(__FILE__) . 'class-messaging-system-v2.php';
require_once plugin_dir_path(__FILE__) . 'class-admin-stats-tracker.php';
require_once plugin_dir_path(__FILE__) . 'class-admin-as-agent-v2.php';
";

// Find the position after the existing includes
$include_position = strpos($updated_content, 'class Hozi_Akadly {');
if ($include_position !== false) {
    $updated_content = substr_replace($updated_content, $includes_to_add . "\n", $include_position, 0);
}

// Write the updated content
if (file_put_contents($main_file_path, $updated_content)) {
    echo "✅ تم تحديث الملف الرئيسي بنجاح<br>";
} else {
    echo "❌ فشل في تحديث الملف الرئيسي<br>";
}

// Create backup of original file
$backup_path = $main_file_path . '.backup.' . date('Y-m-d-H-i-s');
if (copy($main_file_path, $backup_path)) {
    echo "✅ تم إنشاء نسخة احتياطية: " . basename($backup_path) . "<br>";
}

echo "<h2>📝 التحديثات المطبقة:</h2>";
echo "<ul>";
echo "<li>✅ إضافة تضمين الكلاسات الجديدة</li>";
echo "<li>✅ استبدال نظام المراسلة القديم بالجديد</li>";
echo "<li>✅ إضافة نظام تتبع إحصائيات المدير</li>";
echo "<li>✅ إضافة نظام العمل كوكيل المحسن</li>";
echo "<li>✅ إنشاء نسخة احتياطية من الملف الأصلي</li>";
echo "</ul>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 تم التحديث بنجاح!</h3>";
echo "<p>الآن يمكنك اختبار النظام الجديد. إذا واجهت أي مشاكل، يمكنك استعادة النسخة الأصلية من الملف الاحتياطي.</p>";
echo "</div>";
?>