<?php
/**
 * Test Confirmation Flow - Debug Tool
 * اختبار تدفق التأكيد - أداة التشخيص
 */

// WordPress environment
require_once('wp-config.php');
require_once(ABSPATH . 'wp-admin/includes/admin.php');

// Security check
if (!current_user_can('manage_options')) {
    die('غير مصرح لك بالوصول');
}

echo "<h1>🔍 اختبار تدفق التأكيد - أكدلي</h1>";

// Get a test order
global $wpdb;

$test_order = $wpdb->get_row("
    SELECT oa.order_id, oa.agent_id, oa.confirmation_status, p.post_status, a.name as agent_name
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    WHERE oa.confirmation_status = 'pending_confirmation'
    ORDER BY oa.assigned_at DESC
    LIMIT 1
");

if (!$test_order) {
    echo "<p style='color: red;'>❌ لا توجد طلبات في انتظار التأكيد للاختبار</p>";
    exit;
}

echo "<h2>📋 طلب الاختبار:</h2>";
echo "<p><strong>رقم الطلب:</strong> #{$test_order->order_id}</p>";
echo "<p><strong>الوكيل:</strong> {$test_order->agent_name}</p>";
echo "<p><strong>حالة أكدلي:</strong> {$test_order->confirmation_status}</p>";
echo "<p><strong>حالة WooCommerce:</strong> {$test_order->post_status}</p>";

// Test confirmation process
if (isset($_GET['action']) && $_GET['action'] === 'test_confirm') {
    echo "<h2>🧪 اختبار عملية التأكيد:</h2>";

    // Step 1: Before confirmation
    echo "<h3>1️⃣ قبل التأكيد:</h3>";
    $order_before = wc_get_order($test_order->order_id);
    echo "<p>حالة WooCommerce: <strong>{$order_before->get_status()}</strong></p>";

    // Step 2: Run confirmation
    echo "<h3>2️⃣ تشغيل عملية التأكيد:</h3>";

    $order_distributor = new Hozi_Akadly_Order_Distributor();
    $result = $order_distributor->update_confirmation_status($test_order->order_id, 'confirmed', 'اختبار تدفق التأكيد');

    if ($result) {
        echo "<p style='color: green;'>✅ تم تشغيل عملية التأكيد بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في تشغيل عملية التأكيد</p>";
    }

    // Step 3: After confirmation
    echo "<h3>3️⃣ بعد التأكيد:</h3>";
    $order_after = wc_get_order($test_order->order_id);
    echo "<p>حالة WooCommerce: <strong>{$order_after->get_status()}</strong></p>";

    // Check if in tracking
    $tracking_entry = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
        $test_order->order_id
    ));

    if ($tracking_entry) {
        echo "<p style='color: green;'>✅ الطلب موجود في نظام التتبع</p>";
        echo "<p><strong>حالة التتبع:</strong> {$tracking_entry->status}</p>";
        echo "<p><strong>تاريخ الإضافة:</strong> {$tracking_entry->created_at}</p>";
        echo "<p><strong>ملاحظات:</strong> {$tracking_entry->notes}</p>";
    } else {
        echo "<p style='color: red;'>❌ الطلب غير موجود في نظام التتبع</p>";

        // Debug: Check if auto_transfer_to_tracking was called
        echo "<h4>🔍 تشخيص مشكلة النقل:</h4>";

        // Check if the function exists
        if (method_exists('Hozi_Akadly_Order_Distributor', 'auto_transfer_to_tracking')) {
            echo "<p>✅ دالة auto_transfer_to_tracking موجودة</p>";
        } else {
            echo "<p style='color: red;'>❌ دالة auto_transfer_to_tracking غير موجودة</p>";
        }

        // Check database table
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}hozi_order_tracking'");
        if ($table_exists) {
            echo "<p>✅ جدول hozi_order_tracking موجود</p>";

            // Check total records in tracking table
            $total_tracking = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking");
            echo "<p><strong>إجمالي السجلات في جدول التتبع:</strong> {$total_tracking}</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول hozi_order_tracking غير موجود</p>";
        }
    }

    // Check assignment status
    $assignment_after = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
        $test_order->order_id
    ));

    echo "<p><strong>حالة التخصيص:</strong> {$assignment_after->confirmation_status}</p>";

    // Show recent order notes
    echo "<h3>4️⃣ ملاحظات الطلب الأخيرة:</h3>";
    $notes = $order_after->get_order_notes(array('limit' => 3));
    foreach ($notes as $note) {
        echo "<div style='background: #f9f9f9; padding: 10px; margin: 5px 0; border-left: 3px solid #007cba;'>";
        echo "<p><strong>التاريخ:</strong> " . $note->date_created->format('Y-m-d H:i:s') . "</p>";
        echo "<p><strong>المحتوى:</strong> " . $note->content . "</p>";
        echo "</div>";
    }

    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>📊 ملخص النتائج:</h3>";
    echo "<p><strong>الحالة المتوقعة:</strong> on-hold (قيد الانتظار)</p>";
    echo "<p><strong>الحالة الفعلية:</strong> {$order_after->get_status()}</p>";

    if ($order_after->get_status() === 'on-hold') {
        echo "<p style='color: green;'>✅ الحالة صحيحة!</p>";
    } else {
        echo "<p style='color: red;'>❌ الحالة غير صحيحة!</p>";
    }

    if ($tracking_entry) {
        echo "<p style='color: green;'>✅ تم النقل إلى التتبع بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ لم يتم النقل إلى التتبع</p>";
    }
    echo "</div>";

} else {
    echo "<h2>🚀 اختبار التأكيد:</h2>";
    echo "<p><a href='?action=test_confirm' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🧪 تشغيل اختبار التأكيد</a></p>";
    echo "<p><strong>تحذير:</strong> سيتم تأكيد الطلب فعلياً!</p>";
}

echo "<hr>";
echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 فتح متابعة التوصيل</a></p>";
echo "<p><a href='debug-delivery-new.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 أداة التشخيص الرئيسية</a></p>";
?>
