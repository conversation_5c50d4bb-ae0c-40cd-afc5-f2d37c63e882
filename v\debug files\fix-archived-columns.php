<?php
/**
 * Temporary fix for archived columns
 * Run this file once to add missing columns
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

global $wpdb;

echo "<h2>🔧 إصلاح أعمدة الأرشفة</h2>";

$assignments_table = $wpdb->prefix . 'hozi_order_assignments';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$assignments_table'") == $assignments_table;

if (!$table_exists) {
    echo "<p style='color: red;'>❌ الجدول غير موجود: $assignments_table</p>";
    exit;
}

echo "<p>✅ الجدول موجود: $assignments_table</p>";

// Get current columns
$columns = $wpdb->get_col("SHOW COLUMNS FROM $assignments_table");
echo "<p><strong>الأعمدة الحالية:</strong> " . implode(', ', $columns) . "</p>";

// Add archived column
if (!in_array('archived', $columns)) {
    $result1 = $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN archived tinyint(1) DEFAULT 0");
    if ($result1 !== false) {
        echo "<p style='color: green;'>✅ تم إضافة عمود archived بنجاح!</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة عمود archived: " . $wpdb->last_error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ عمود archived موجود بالفعل</p>";
}

// Add archived_at column
if (!in_array('archived_at', $columns)) {
    $result2 = $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN archived_at datetime DEFAULT NULL");
    if ($result2 !== false) {
        echo "<p style='color: green;'>✅ تم إضافة عمود archived_at بنجاح!</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة عمود archived_at: " . $wpdb->last_error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ عمود archived_at موجود بالفعل</p>";
}

// Verify columns were added
$new_columns = $wpdb->get_col("SHOW COLUMNS FROM $assignments_table");
echo "<p><strong>الأعمدة بعد الإضافة:</strong> " . implode(', ', $new_columns) . "</p>";

// Test query
$test_count = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM $assignments_table WHERE agent_id = %d AND confirmation_status = 'confirmed'",
    1
));

echo "<p><strong>اختبار الاستعلام:</strong> $test_count طلب مؤكد للوكيل</p>";

echo "<h3>🎯 الإصلاح مكتمل!</h3>";
echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-delivery-tracking') . "'>العودة لصفحة التتبع</a></p>";
?>
