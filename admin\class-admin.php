<?php
/**
 * Admin functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        // Always add admin menu and basic functionality
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Always register AJAX handlers - regardless of license status
        $this->register_ajax_handlers();

        // Always initialize licensed features for now (debug mode)
        $this->init_licensed_features();
    }

    /**
     * Register AJAX handlers - always available regardless of license
     */
    private function register_ajax_handlers() {
        // AJAX handlers for all users
        add_action('wp_ajax_hozi_assign_order', array($this, 'ajax_assign_order'));
        add_action('wp_ajax_hozi_update_confirmation', array($this, 'ajax_update_confirmation'));
        add_action('wp_ajax_hozi_create_agent', array($this, 'ajax_create_agent'));
        add_action('wp_ajax_hozi_toggle_agent_status', array($this, 'ajax_toggle_agent_status'));
        add_action('wp_ajax_hozi_save_upsell', array($this, 'ajax_save_upsell'));
        add_action('wp_ajax_hozi_quick_tracking_update', array($this, 'ajax_quick_tracking_update'));
        add_action('wp_ajax_hozi_get_agent_stats', array($this, 'ajax_get_agent_stats'));
        add_action('wp_ajax_hozi_update_tracking_status', array($this, 'ajax_update_tracking_status'));
        add_action('wp_ajax_hozi_bulk_update_tracking_status', array($this, 'ajax_bulk_update_tracking_status'));
        add_action('wp_ajax_hozi_reset_logs', array($this, 'ajax_reset_logs'));
        add_action('wp_ajax_hozi_reset_assignments', array($this, 'ajax_reset_assignments'));
        add_action('wp_ajax_hozi_check_new_orders', array($this, 'ajax_check_new_orders'));
        add_action('wp_ajax_hozi_clear_completed_orders', array($this, 'ajax_clear_completed_orders'));
    }

    /**
     * Initialize features that require valid license
     */
    private function init_licensed_features() {
        // Add custom columns to orders list
        add_filter('manage_shop_order_posts_columns', array($this, 'add_order_columns'));
        add_action('manage_shop_order_posts_custom_column', array($this, 'populate_order_columns'), 10, 2);

        // Add meta box to order edit page
        add_action('add_meta_boxes', array($this, 'add_order_meta_boxes'));

        // Redirect agents on login
        add_filter('login_redirect', array($this, 'redirect_agent_on_login'), 10, 3);

        // Prevent agents from accessing frontend
        add_action('template_redirect', array($this, 'prevent_agent_frontend_access'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Only show admin menus to users with proper capabilities
        if (!current_user_can('manage_options') && !current_user_can('hozi_view_assigned_orders')) {
            return;
        }

        // Check if license is valid - get fresh status from database
        $license_manager = Hozi_Akadly_License_Manager::get_instance();
        $is_licensed = $license_manager->is_license_valid();

        if (!$is_licensed && current_user_can('manage_options')) {
            // Show only license page when not licensed - only for admins
            add_menu_page(
                __('أكدلي - Akadly', 'hozi-akadly'),
                __('أكدلي - Akadly', 'hozi-akadly'),
                'manage_options',
                'hozi-akadly-license',
                array($this, 'license_page'),
                'dashicons-lock',
                56
            );
            return;
        }

        // Check if user is an agent first
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $is_agent = $agent_manager->is_agent() && $agent_manager->get_current_agent();

        // For agents, always show their dashboard regardless of license status
        if ($is_agent) {
            add_menu_page(
                __('أكدلي - Akadly', 'hozi-akadly'),
                __('أكدلي - Akadly', 'hozi-akadly'),
                'hozi_view_assigned_orders',
                'hozi-akadly-my-orders',
                array($this, 'agent_dashboard_page'),
                'dashicons-phone',
                56
            );

            // Add delivery tracking submenu for agents (NEW APPROACH) - Check permissions
            if (Hozi_Akadly_Delivery_Tracking_Permissions::should_show_delivery_tracking_menu()) {
                add_submenu_page(
                    'hozi-akadly-my-orders',
                    __('متابعة التوصيل', 'hozi-akadly'),
                    __('📦 متابعة التوصيل', 'hozi-akadly'),
                    'hozi_view_assigned_orders',
                    'hozi-akadly-delivery-tracking',
                    array($this, 'delivery_tracking_page')
                );
            }



            // Add archived orders submenu for agents
            add_submenu_page(
                'hozi-akadly-my-orders',
                __('الطلبات المؤرشفة', 'hozi-akadly'),
                __('الطلبات المؤرشفة', 'hozi-akadly'),
                'hozi_view_assigned_orders',
                'hozi-akadly-my-archived',
                array($this, 'agent_archived_page')
            );

            return;
        }

        // Check if user is a customer (not admin, not agent)
        if (is_user_logged_in() && !current_user_can('manage_options') && !$is_agent) {
            add_menu_page(
                __('أكدلي - Akadly', 'hozi-akadly'),
                __('أكدلي - Akadly', 'hozi-akadly'),
                'read',
                'hozi-akadly-customer-dashboard',
                array($this, 'customer_dashboard_page'),
                'dashicons-cart',
                56
            );

            return;
        }

        // For admins, check license status
        if (!$is_licensed) {
            return; // Already handled above
        }

        // Main menu (only when licensed and for admins)
        add_menu_page(
            __('أكدلي - Akadly', 'hozi-akadly'),
            __('أكدلي - Akadly', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly',
            array($this, 'dashboard_page'),
            'dashicons-phone',
            56
        );

        // Dashboard
        add_submenu_page(
            'hozi-akadly',
            __('Dashboard', 'hozi-akadly'),
            __('لوحة التحكم', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly',
            array($this, 'dashboard_page')
        );

        // Analytics
        add_submenu_page(
            'hozi-akadly',
            __('Analytics', 'hozi-akadly'),
            __('التحليلات', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly-analytics',
            array($this, 'analytics_page')
        );

        // Agents management
        add_submenu_page(
            'hozi-akadly',
            __('Agents', 'hozi-akadly'),
            __('الوكلاء', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly-agents',
            array($this, 'agents_page')
        );

        // Order assignments
        add_submenu_page(
            'hozi-akadly',
            __('Order Assignments', 'hozi-akadly'),
            __('توزيع الطلبات', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly-assignments',
            array($this, 'assignments_page')
        );

        // Order tracking
        add_submenu_page(
            'hozi-akadly',
            __('Order Tracking', 'hozi-akadly'),
            __('تتبع الطلبيات', 'hozi-akadly'),
            'edit_shop_orders',
            'hozi-akadly-tracking',
            array($this, 'tracking_page')
        );

        // Admin as Agent (NEW FEATURE)
        add_submenu_page(
            'hozi-akadly',
            __('العمل كوكيل', 'hozi-akadly'),
            __('🎭 العمل كوكيل', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly-admin-as-agent',
            array($this, 'admin_as_agent_page')
        );

        // Hide admin menu items for agents
        add_action('admin_menu', array($this, 'hide_menu_for_agents'), 999);

        // Show activation notice
        add_action('admin_notices', array($this, 'activation_notice'));

        // Add order metaboxes (unified approach)
        add_action('add_meta_boxes', array($this, 'add_order_meta_boxes'));
        add_action('save_post', array($this, 'save_order_assignment_metabox'));



        // Settings
        add_submenu_page(
            'hozi-akadly',
            __('Settings', 'hozi-akadly'),
            __('الإعدادات', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly-settings',
            array($this, 'settings_page')
        );

        // License - only for admins
        add_submenu_page(
            'hozi-akadly',
            __('الترخيص', 'hozi-akadly'),
            __('الترخيص', 'hozi-akadly'),
            'manage_options', // Only for admins
            'hozi-akadly-license',
            array($this, 'license_page')
        );

        // User Guide - only for admins
        add_submenu_page(
            'hozi-akadly',
            __('دليل الاستخدام', 'hozi-akadly'),
            __('📖 دليل الاستخدام', 'hozi-akadly'),
            'manage_options',
            'hozi-akadly-user-guide',
            array($this, 'user_guide_page')
        );
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'hozi-akadly') === false) {
            return;
        }

        wp_enqueue_script(
            'hozi-akadly-admin',
            HOZI_AKADLY_PLUGIN_URL . 'admin/assets/js/admin.js',
            array('jquery'),
            HOZI_AKADLY_VERSION,
            true
        );

        wp_enqueue_style(
            'hozi-akadly-admin',
            HOZI_AKADLY_PLUGIN_URL . 'admin/assets/css/admin.css',
            array(),
            HOZI_AKADLY_VERSION
        );

        wp_localize_script('hozi-akadly-admin', 'hoziAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('hozi_akadly_nonce'),
            'strings' => array(
                'confirm' => __('هل أنت متأكد؟', 'hozi-akadly'),
                'success' => __('تم بنجاح', 'hozi-akadly'),
                'error' => __('حدث خطأ', 'hozi-akadly'),
            )
        ));
    }

    /**
     * Dashboard page
     */
    public function dashboard_page() {
        // Check license before showing content - use direct check
        $license_manager = Hozi_Akadly_License_Manager::get_instance();
        if (!$license_manager->is_license_valid()) {
            wp_die(__('يجب تفعيل الترخيص للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        // Show license activation success message
        if (isset($_GET['license_activated']) && $_GET['license_activated'] == '1') {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible">';
                echo '<p><strong>🎉 تم تفعيل الترخيص بنجاح!</strong> يمكنك الآن الاستفادة من جميع مميزات أكدلي - Akadly.</p>';
                echo '</div>';
            });
        }

        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $order_distributor = new Hozi_Akadly_Order_Distributor();

        // Get statistics
        $total_agents = count($agent_manager->get_agents());
        $active_agents = count($agent_manager->get_agents(true));
        $unassigned_orders = count($order_distributor->get_unassigned_orders());

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/dashboard.php';
    }

    /**
     * Agents page
     */
    public function agents_page() {
        // Check license before showing content - use direct check
        $license_manager = Hozi_Akadly_License_Manager::get_instance();
        if (!$license_manager->is_license_valid()) {
            wp_die(__('يجب تفعيل الترخيص للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Handle form submissions
        if (isset($_POST['action'])) {
            $this->handle_agent_actions();
        }

        $agents = $agent_manager->get_agents();
        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/agents.php';
    }

    /**
     * Assignments page
     */
    public function assignments_page() {
        $order_distributor = new Hozi_Akadly_Order_Distributor();
        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Check if viewing specific agent
        $agent_id = isset($_GET['agent_id']) ? intval($_GET['agent_id']) : null;

        if ($agent_id) {
            // Show specific agent's orders
            $this->agent_orders_page($agent_id);
            return;
        }

        // Handle bulk assignment
        if (isset($_POST['bulk_assign'])) {
            $this->handle_bulk_assignment();
        }

        $unassigned_orders = $order_distributor->get_unassigned_orders();
        $agents = $agent_manager->get_agents(true);

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/assignments.php';
    }

    /**
     * Agent orders page
     */
    private function agent_orders_page($agent_id) {
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $order_distributor = new Hozi_Akadly_Order_Distributor();

        // Get agent info
        $agent = $agent_manager->get_agent($agent_id);
        if (!$agent) {
            wp_die(__('الوكيل غير موجود.', 'hozi-akadly'));
        }

        // Pagination settings
        $per_page = 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;

        // Get filter
        $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : 'all';

        // Get agent's orders
        $agent_orders = $order_distributor->get_agent_assignments($agent_id, $status_filter === 'all' ? null : $status_filter, $per_page, $offset);
        $total_orders = $order_distributor->get_agent_assignments_count($agent_id, $status_filter === 'all' ? null : $status_filter);
        $agent_stats = $agent_manager->get_agent_stats($agent_id);

        // Calculate pagination info
        $total_pages = ceil($total_orders / $per_page);
        $pagination_info = array(
            'current_page' => $current_page,
            'total_pages' => $total_pages,
            'per_page' => $per_page,
            'total_orders' => $total_orders,
            'showing_from' => $offset + 1,
            'showing_to' => min($offset + $per_page, $total_orders)
        );

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/agent-orders.php';
    }

    /**
     * Agent dashboard page
     */
    public function agent_dashboard_page() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $order_distributor = new Hozi_Akadly_Order_Distributor();

        // Check if user is actually an agent
        if (!$agent_manager->is_agent()) {
            wp_die(__('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        $current_agent = $agent_manager->get_current_agent();
        if (!$current_agent) {
            wp_die(__('لم يتم العثور على بيانات الوكيل.', 'hozi-akadly'));
        }

        // Handle confirmation updates
        if (isset($_POST['update_confirmation'])) {
            $this->handle_confirmation_update();
        }

        // Pagination settings
        $per_page = 8;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;

        try {
            $pending_orders = $order_distributor->get_pending_assignments($current_agent->id, $per_page, $offset);
            $total_pending = $order_distributor->get_pending_assignments_count($current_agent->id);
            $agent_stats = $agent_manager->get_agent_stats($current_agent->id);

            // Calculate pagination info
            $total_pages = ceil($total_pending / $per_page);
            $pagination_info = array(
                'current_page' => $current_page,
                'total_pages' => $total_pages,
                'per_page' => $per_page,
                'total_orders' => $total_pending,
                'showing_from' => $offset + 1,
                'showing_to' => min($offset + $per_page, $total_pending)
            );
        } catch (Exception $e) {
            $pending_orders = array();
            $agent_stats = array();
            $pagination_info = null;
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/agent-dashboard.php';
    }

    /**
     * Agent tracking page
     */
    public function agent_tracking_page() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $order_tracker = new Hozi_Akadly_Order_Tracker();

        // Check if user is actually an agent
        if (!$agent_manager->is_agent()) {
            wp_die(__('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        $current_agent = $agent_manager->get_current_agent();
        if (!$current_agent) {
            wp_die(__('لم يتم العثور على بيانات الوكيل.', 'hozi-akadly'));
        }

        // Handle tracking updates
        if (isset($_POST['update_tracking_status'])) {
            $this->handle_agent_tracking_update();
        }

        try {
            // Get confirmed orders for this agent that need tracking
            $confirmed_orders = $this->get_agent_confirmed_orders($current_agent->id);
            $tracking_stats = $this->get_agent_tracking_stats($current_agent->id);

            // Debug logging for commercial version
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Hozi Akadly - Agent Tracking Page - Agent ID: " . $current_agent->id);
                error_log("Hozi Akadly - Agent Tracking Page - Confirmed Orders Count: " . count($confirmed_orders));
                error_log("Hozi Akadly - Agent Tracking Page - Stats: " . print_r($tracking_stats, true));
                if (!empty($confirmed_orders)) {
                    error_log("Hozi Akadly - Agent Tracking Page - First Order: " . print_r($confirmed_orders[0], true));
                }
            }

            // Get filter parameters
            $status_filter = isset($_GET['status_filter']) ? sanitize_text_field($_GET['status_filter']) : '';
            $date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
            $date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';

            // Apply filters if needed
            if ($status_filter || $date_from || $date_to) {
                $confirmed_orders = $this->filter_agent_orders($confirmed_orders, $status_filter, $date_from, $date_to);
            }

        } catch (Exception $e) {
            error_log("Hozi Akadly - Agent Tracking Page Error: " . $e->getMessage());
            $confirmed_orders = array();
            $tracking_stats = (object) array(
                'total_confirmed' => 0,
                'total_tracked' => 0,
                'delivered' => 0,
                'rejected' => 0,
                'postponed' => 0,
                'exchange' => 0
            );
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/agent-tracking.php';
    }

    /**
     * Delivery tracking page (NEW APPROACH)
     */
    public function delivery_tracking_page() {
        // Check delivery tracking permissions first
        if (!Hozi_Akadly_Delivery_Tracking_Permissions::can_access_delivery_tracking()) {
            $error_message = Hozi_Akadly_Delivery_Tracking_Permissions::get_access_denied_message();
            wp_die($error_message);
        }

        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // For non-admin users, check if they are agents
        if (!current_user_can('manage_options')) {
            if (!$agent_manager->is_agent()) {
                wp_die(__('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'hozi-akadly'));
            }

            $current_agent = $agent_manager->get_current_agent();
            if (!$current_agent) {
                wp_die(__('لم يتم العثور على بيانات الوكيل.', 'hozi-akadly'));
            }
        } else {
            // For admins, set current_agent to null - they can see all orders
            $current_agent = null;
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/delivery-tracking.php';
    }

    /**
     * Agent archived orders page
     */
    public function agent_archived_page() {
        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/my-archived.php';
    }

    /**
     * Analytics page
     */
    public function analytics_page() {
        // Check license before showing content - use direct check
        $license_manager = Hozi_Akadly_License_Manager::get_instance();
        if (!$license_manager->is_license_valid()) {
            wp_die(__('يجب تفعيل الترخيص للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/analytics-dashboard.php';
    }

    /**
     * Customer dashboard page
     */
    public function customer_dashboard_page() {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_die(__('يجب تسجيل الدخول للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        // Check if user is not admin or agent
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        if (current_user_can('manage_options') || $agent_manager->is_agent()) {
            wp_die(__('هذه الصفحة مخصصة للعملاء فقط.', 'hozi-akadly'));
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/customer-dashboard.php';
    }

    /**
     * Order tracking page
     */
    public function tracking_page() {
        // Check license before showing content - use direct check
        $license_manager = Hozi_Akadly_License_Manager::get_instance();
        if (!$license_manager->is_license_valid()) {
            wp_die(__('يجب تفعيل الترخيص للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/order-tracking.php';
    }

    /**
     * Admin as Agent page (NEW FEATURE)
     */
    public function admin_as_agent_page() {
        // Check license before showing content
        $license_manager = Hozi_Akadly_License_Manager::get_instance();
        if (!$license_manager->is_license_valid()) {
            wp_die(__('يجب تفعيل الترخيص للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        // Only admins can access this page
        if (!current_user_can('manage_options')) {
            wp_die(__('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $order_distributor = new Hozi_Akadly_Order_Distributor();

        // Get selected agent from URL or session
        $selected_agent_id = isset($_GET['agent_id']) ? intval($_GET['agent_id']) :
                           (isset($_SESSION['hozi_admin_as_agent']) ? $_SESSION['hozi_admin_as_agent'] : 0);

        // Handle agent selection
        if (isset($_POST['select_agent'])) {
            $selected_agent_id = intval($_POST['agent_id']);
            $_SESSION['hozi_admin_as_agent'] = $selected_agent_id;

            // Redirect to avoid form resubmission
            wp_redirect(admin_url('admin.php?page=hozi-akadly-admin-as-agent&agent_id=' . $selected_agent_id));
            exit;
        }

        // Handle confirmation updates (admin acting as agent)
        if (isset($_POST['update_confirmation']) && $selected_agent_id) {
            $this->handle_admin_as_agent_confirmation($selected_agent_id);
        }

        // Get agents list
        $agents = $agent_manager->get_agents(true); // Only active agents

        // Get selected agent info and orders
        $selected_agent = null;
        $pending_orders = array();
        $agent_stats = null;
        $pagination_info = null;

        if ($selected_agent_id) {
            $selected_agent = $agent_manager->get_agent($selected_agent_id);

            if ($selected_agent) {
                // Pagination settings
                $per_page = 8;
                $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
                $offset = ($current_page - 1) * $per_page;

                try {
                    $pending_orders = $order_distributor->get_pending_assignments($selected_agent_id, $per_page, $offset);
                    $total_pending = $order_distributor->get_pending_assignments_count($selected_agent_id);
                    $agent_stats = $agent_manager->get_agent_stats($selected_agent_id);

                    // Calculate pagination info
                    $total_pages = ceil($total_pending / $per_page);
                    $pagination_info = array(
                        'current_page' => $current_page,
                        'total_pages' => $total_pages,
                        'per_page' => $per_page,
                        'total_orders' => $total_pending,
                        'showing_from' => $offset + 1,
                        'showing_to' => min($offset + $per_page, $total_pending)
                    );
                } catch (Exception $e) {
                    $pending_orders = array();
                    $agent_stats = array();
                    $pagination_info = null;
                }
            }
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/admin-as-agent.php';
    }

    /**
     * User guide page
     */
    public function user_guide_page() {
        ?>
        <div class="wrap">
            <h1>📖 دليل الاستخدام - أكدلي Akadly</h1>

            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin: 20px 0; text-align: center;">
                <h2 style="color: white; margin-top: 0;">🎯 مرحباً بك في نظام أكدلي لتأكيد الطلبات</h2>
                <p style="font-size: 18px; margin-bottom: 0;">نظام شامل لإدارة وتأكيد طلبات WooCommerce مع توزيع ذكي للوكلاء</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0;">

                <!-- للمشرفين -->
                <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 5px solid #e74c3c;">
                    <h3 style="color: #e74c3c; margin-top: 0;">👨‍💼 للمشرفين</h3>
                    <ul style="line-height: 1.8;">
                        <li><strong>لوحة التحكم:</strong> عرض الإحصائيات العامة والنشاط الأخير</li>
                        <li><strong>إدارة الوكلاء:</strong> إضافة/تعديل/تعطيل الوكلاء</li>
                        <li><strong>توزيع الطلبات:</strong> تخصيص الطلبات يدوياً أو تلقائياً</li>
                        <li><strong>التحليلات:</strong> متابعة أداء الوكلاء والإحصائيات</li>
                        <li><strong>طلبات العملاء:</strong> البحث في طلبات عميل محدد</li>
                    </ul>
                </div>

                <!-- للوكلاء -->
                <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 5px solid #3498db;">
                    <h3 style="color: #3498db; margin-top: 0;">📞 للوكلاء</h3>
                    <ul style="line-height: 1.8;">
                        <li><strong>طلباتي:</strong> عرض الطلبات المخصصة للتأكيد</li>
                        <li><strong>متابعة التوصيل:</strong> تحديث حالة الطلبات المؤكدة</li>
                        <li><strong>الطلبات المؤرشفة:</strong> عرض الطلبات المكتملة</li>
                        <li><strong>أزرار سريعة:</strong> تأكيد، رفض، لم يرد، إعادة اتصال</li>
                        <li><strong>إضافة ملاحظات:</strong> توثيق تفاصيل المكالمة</li>
                    </ul>
                </div>

                <!-- للعملاء -->
                <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 5px solid #27ae60;">
                    <h3 style="color: #27ae60; margin-top: 0;">🛒 للعملاء</h3>
                    <ul style="line-height: 1.8;">
                        <li><strong>لوحة تحكم العملاء:</strong> عرض طلباتهم الشخصية</li>
                        <li><strong>حالة التأكيد:</strong> متابعة حالة تأكيد الطلب</li>
                        <li><strong>تفاصيل الوكيل:</strong> معرفة الوكيل المخصص</li>
                        <li><strong>الملاحظات:</strong> رؤية ملاحظات الوكيل</li>
                        <li><strong>التصفية:</strong> فلترة الطلبات حسب الحالة</li>
                    </ul>
                </div>
            </div>

            <!-- خطوات البدء السريع -->
            <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin: 30px 0;">
                <h2 style="color: #2c3e50; margin-top: 0;">🚀 خطوات البدء السريع</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="text-align: center; padding: 20px;">
                        <div style="background: #3498db; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px; font-weight: bold;">1</div>
                        <h4>إضافة الوكلاء</h4>
                        <p>اذهب إلى <strong>الوكلاء</strong> وأضف وكلاء التأكيد</p>
                    </div>

                    <div style="text-align: center; padding: 20px;">
                        <div style="background: #e74c3c; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px; font-weight: bold;">2</div>
                        <h4>ضبط الإعدادات</h4>
                        <p>اختر طريقة التوزيع والإعدادات المناسبة</p>
                    </div>

                    <div style="text-align: center; padding: 20px;">
                        <div style="background: #27ae60; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px; font-weight: bold;">3</div>
                        <h4>بدء التأكيد</h4>
                        <p>الوكلاء يمكنهم الآن تأكيد الطلبات الجديدة</p>
                    </div>
                </div>
            </div>

            <!-- المميزات الرئيسية -->
            <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin: 30px 0;">
                <h2 style="color: #2c3e50; margin-top: 0;">✨ المميزات الرئيسية</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                    <div>
                        <h4 style="color: #8e44ad;">🔄 التوزيع الذكي</h4>
                        <p>توزيع تلقائي للطلبات على الوكلاء بنظام Round Robin أو التخصيص اليدوي</p>
                    </div>

                    <div>
                        <h4 style="color: #f39c12;">📊 التحليلات المتقدمة</h4>
                        <p>إحصائيات شاملة لأداء الوكلاء ومعدلات التأكيد والرفض</p>
                    </div>

                    <div>
                        <h4 style="color: #e67e22;">🔔 التنبيهات الفورية</h4>
                        <p>تنبيهات صوتية للوكلاء عند وصول طلبات جديدة</p>
                    </div>

                    <div>
                        <h4 style="color: #1abc9c;">📱 تصميم متجاوب</h4>
                        <p>واجهة محسنة للهواتف المحمولة لسهولة الاستخدام</p>
                    </div>

                    <div>
                        <h4 style="color: #34495e;">🔒 الأمان والصلاحيات</h4>
                        <p>نظام صلاحيات متقدم لحماية البيانات وتقييد الوصول</p>
                    </div>

                    <div>
                        <h4 style="color: #9b59b6;">📈 تتبع الطلبات</h4>
                        <p>نظام متابعة شامل لحالة الطلبات من التأكيد حتى التسليم</p>
                    </div>
                </div>
            </div>

            <!-- روابط مفيدة -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; border: 1px solid #dee2e6; margin: 30px 0;">
                <h3 style="color: #495057; margin-top: 0;">🔗 روابط مفيدة</h3>
                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly'); ?>" class="button button-primary">🏠 لوحة التحكم</a>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-agents'); ?>" class="button button-secondary">👥 إدارة الوكلاء</a>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-settings'); ?>" class="button button-secondary">⚙️ الإعدادات</a>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-analytics'); ?>" class="button button-secondary">📊 التحليلات</a>
                </div>
            </div>

            <!-- معلومات الدعم -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 30px 0; text-align: center;">
                <h3 style="color: white; margin-top: 0;">💬 هل تحتاج مساعدة؟</h3>
                <p style="margin-bottom: 20px;">فريق الدعم الفني جاهز لمساعدتك في أي وقت</p>
                <a href="https://hostazi.shop" target="_blank" style="background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; font-weight: bold; transition: all 0.3s;">
                    🌐 زيارة موقع الدعم
                </a>
            </div>
        </div>

        <style>
        .wrap h3 {
            font-size: 1.3em;
        }
        .wrap h4 {
            font-size: 1.1em;
            margin-bottom: 10px;
        }
        .wrap p, .wrap li {
            font-size: 1em;
        }
        .wrap ul {
            padding-left: 20px;
        }
        @media (max-width: 768px) {
            .wrap > div {
                padding: 15px !important;
            }
            .wrap h1 {
                font-size: 1.5em;
            }
            .wrap h2 {
                font-size: 1.3em;
            }
        }
        </style>
        <?php
    }

    /**
     * Show activation notice
     */
    public function activation_notice() {
        if (get_transient('hozi_akadly_activation_notice')) {
            delete_transient('hozi_akadly_activation_notice');
            ?>
            <div class="notice notice-success is-dismissible">
                <h3>🎉 مرحباً بك في Hozi Akadly!</h3>
                <p><strong>تم تفعيل الإضافة بنجاح!</strong> يمكنك الآن البدء في استخدام نظام تأكيد الطلبات.</p>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly'); ?>" class="button button-primary">
                        🚀 ابدأ الإعداد
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-agents'); ?>" class="button">
                        👥 إضافة وكيل
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-settings'); ?>" class="button">
                        ⚙️ الإعدادات
                    </a>
                </p>
                <p><em>💡 نصيحة: ابدأ بإضافة وكيل تأكيد جديد من قائمة "الوكلاء"</em></p>
            </div>
            <?php
        }
    }



    /**
     * Order assignment metabox callback
     */
    public function order_assignment_metabox_callback($post) {
        global $wpdb;

        // Check if tables exist
        $tables_exist = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}hozi_agents'");

        if (!$tables_exist) {
            ?>
            <div style="padding: 15px; background: #fff3cd; border-left: 4px solid #ffc107;">
                <h4 style="margin: 0 0 10px 0; color: #856404;">⚠️ جداول البيانات غير موجودة</h4>
                <p style="margin: 0 0 10px 0; color: #856404;">يجب إنشاء جداول البيانات أولاً لتتمكن من استخدام نظام التخصيص.</p>
                <form method="post" style="margin: 0;">
                    <?php wp_nonce_field('create_tables', 'create_tables_nonce'); ?>
                    <input type="hidden" name="action" value="create_tables">
                    <button type="submit" class="button button-primary" style="background: #ffc107; border-color: #ffc107; color: #212529;">🔧 إنشاء جداول البيانات الآن</button>
                </form>
            </div>
            <?php
            return;
        }

        // Handle table creation
        if (isset($_POST['action']) && $_POST['action'] === 'create_tables' &&
            wp_verify_nonce($_POST['create_tables_nonce'], 'create_tables')) {

            require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-database.php';
            Hozi_Akadly_Database::create_tables();

            // Create test agent
            $this->create_test_agent();

            echo '<div style="background: #d1edff; padding: 10px; border-radius: 4px; margin: 10px 0;">';
            echo '<p style="color: #0c5460; margin: 0;"><strong>✅ تم إنشاء الجداول بنجاح!</strong> يرجى تحديث الصفحة.</p>';
            echo '</div>';
            return;
        }

        // Get current assignment
        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
            $post->ID
        ));

        // Get all active agents
        $agents = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 ORDER BY name ASC"
        );

        wp_nonce_field('hozi_order_assignment', 'hozi_order_assignment_nonce');
        ?>
        <div class="hozi-order-assignment-metabox">
            <?php if ($assignment) : ?>
                <div class="hozi-current-assignment">
                    <h4>📋 الحالة الحالية:</h4>
                    <?php
                    $agent = $wpdb->get_row($wpdb->prepare(
                        "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                        $assignment->agent_id
                    ));
                    ?>
                    <p><strong>الوكيل:</strong> <?php echo esc_html($agent->name ?? 'غير محدد'); ?></p>
                    <p><strong>تاريخ التخصيص:</strong> <?php echo esc_html(date('Y/m/d H:i', strtotime($assignment->assigned_at))); ?></p>
                    <p><strong>حالة التأكيد:</strong>
                        <?php
                        $status_labels = array(
                            'pending_confirmation' => '⏳ في انتظار التأكيد',
                            'confirmed' => '✅ تم التأكيد',
                            'rejected' => '❌ تم الرفض',
                            'no_answer' => '📞 لم يرد',
                            'callback_later' => '🔄 إعادة اتصال'
                        );
                        echo $status_labels[$assignment->confirmation_status] ?? 'غير محدد';
                        ?>
                    </p>
                    <?php if ($assignment->notes) : ?>
                        <p><strong>ملاحظات:</strong> <?php echo esc_html($assignment->notes); ?></p>
                    <?php endif; ?>
                </div>
                <hr>
            <?php endif; ?>

            <div class="hozi-assignment-form">
                <h4>👥 تخصيص/تغيير الوكيل:</h4>
                <p>
                    <label for="hozi_agent_id"><strong>اختر الوكيل:</strong></label>
                    <select name="hozi_agent_id" id="hozi_agent_id" style="width: 100%;">
                        <option value="">-- اختر وكيل --</option>
                        <?php foreach ($agents as $agent) : ?>
                            <option value="<?php echo esc_attr($agent->id); ?>"
                                    <?php selected($assignment ? $assignment->agent_id : '', $agent->id); ?>>
                                <?php echo esc_html($agent->name); ?>
                                (<?php
                                $current_orders = $wpdb->get_var($wpdb->prepare(
                                    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments
                                     WHERE agent_id = %d AND confirmation_status = 'pending_confirmation'",
                                    $agent->id
                                ));
                                echo $current_orders; ?> طلب حالي)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </p>

                <p>
                    <label for="hozi_assignment_notes"><strong>ملاحظة التخصيص:</strong></label>
                    <textarea name="hozi_assignment_notes" id="hozi_assignment_notes"
                              rows="3" style="width: 100%;"
                              placeholder="ملاحظة اختيارية عن سبب التخصيص..."></textarea>
                </p>

                <p style="text-align: center; margin-top: 15px;">
                    <button type="button" id="hozi_assign_order_btn" class="button button-primary" style="width: 100%; padding: 8px; font-size: 14px; background: #0073aa; border-color: #0073aa;">
                        📋 تخصيص الطلب للوكيل المحدد
                    </button>
                </p>

                <?php if (!$assignment) : ?>
                    <div class="hozi-assignment-notice">
                        <p><em>💡 هذا الطلب غير مخصص لأي وكيل حالياً</em></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <style>
        .hozi-order-assignment-metabox {
            font-size: 13px;
        }

        .hozi-current-assignment {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-right: 4px solid #007cba;
        }

        .hozi-current-assignment h4 {
            margin-top: 0;
            color: #007cba;
        }

        .hozi-current-assignment p {
            margin: 5px 0;
        }

        .hozi-assignment-form h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .hozi-assignment-notice {
            background: #fff3cd;
            padding: 8px;
            border-radius: 4px;
            border-right: 4px solid #ffc107;
        }

        .hozi-assignment-notice p {
            margin: 0;
            color: #856404;
        }

        #hozi_assign_order_btn:hover {
            background: #005a87 !important;
            border-color: #005a87 !important;
        }
        </style>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#hozi_assign_order_btn').on('click', function() {
                var agentId = $('#hozi_agent_id').val();
                var notes = $('#hozi_assignment_notes').val();

                if (!agentId || agentId == '0') {
                    alert('يرجى اختيار وكيل أولاً');
                    return;
                }

                // Confirm assignment
                var agentName = $('#hozi_agent_id option:selected').text();
                if (confirm('هل أنت متأكد من تخصيص هذا الطلب للوكيل: ' + agentName + '؟')) {
                    // Show loading state
                    $(this).prop('disabled', true).text('⏳ جاري التخصيص...');

                    // Trigger form save
                    $('#post').submit();
                }
            });
        });
        </script>
        <?php
    }

    /**
     * Save order assignment metabox
     */
    public function save_order_assignment_metabox($post_id) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check if this is a shop_order
        if (get_post_type($post_id) !== 'shop_order') {
            return;
        }

        // Check nonce
        if (!isset($_POST['hozi_order_assignment_nonce']) ||
            !wp_verify_nonce($_POST['hozi_order_assignment_nonce'], 'hozi_order_assignment')) {
            return;
        }

        // Check permissions
        if (!current_user_can('edit_shop_orders')) {
            return;
        }

        // Get the agent ID
        $agent_id = isset($_POST['hozi_agent_id']) ? intval($_POST['hozi_agent_id']) : 0;
        $assignment_notes = isset($_POST['hozi_assignment_notes']) ? sanitize_textarea_field($_POST['hozi_assignment_notes']) : '';

        global $wpdb;

        // Get current assignment
        $current_assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
            $post_id
        ));

        if ($agent_id > 0) {
            // Get agent info
            $agent = $wpdb->get_row($wpdb->prepare(
                "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                $agent_id
            ));

            if ($agent) {
                if ($current_assignment) {
                    // Update existing assignment
                    $wpdb->update(
                        $wpdb->prefix . 'hozi_order_assignments',
                        array(
                            'agent_id' => $agent_id,
                            'assigned_at' => current_time('mysql')
                        ),
                        array('order_id' => $post_id),
                        array('%d', '%s'),
                        array('%d')
                    );

                    $action = 'تم تغيير تخصيص الطلب';
                } else {
                    // Create new assignment
                    $wpdb->insert(
                        $wpdb->prefix . 'hozi_order_assignments',
                        array(
                            'order_id' => $post_id,
                            'agent_id' => $agent_id,
                            'assigned_at' => current_time('mysql'),
                            'confirmation_status' => 'pending_confirmation'
                        ),
                        array('%d', '%d', '%s', '%s')
                    );

                    $action = 'تم تخصيص الطلب';
                }

                // Add order note
                $order = wc_get_order($post_id);
                if ($order) {
                    $note_text = sprintf(
                        '%s للوكيل: %s%s',
                        $action,
                        $agent->name,
                        $assignment_notes ? "\nملاحظة: " . $assignment_notes : ''
                    );

                    $order->add_order_note($note_text, 0); // 0 = private note
                }

                // Log the assignment
                $wpdb->insert(
                    $wpdb->prefix . 'hozi_confirmation_logs',
                    array(
                        'order_id' => $post_id,
                        'agent_id' => $agent_id,
                        'action' => 'assigned',
                        'notes' => $assignment_notes,
                        'created_at' => current_time('mysql')
                    ),
                    array('%d', '%d', '%s', '%s', '%s')
                );

                // Add success notice
                add_action('admin_notices', function() use ($agent, $action) {
                    echo '<div class="notice notice-success is-dismissible"><p><strong>✅ ' . $action . ' للوكيل: ' . esc_html($agent->name) . '</strong></p></div>';
                });
            }
        } elseif ($current_assignment) {
            // Remove assignment if agent_id is 0 and assignment exists
            $wpdb->delete(
                $wpdb->prefix . 'hozi_order_assignments',
                array('order_id' => $post_id),
                array('%d')
            );

            // Add order note
            $order = wc_get_order($post_id);
            if ($order) {
                $order->add_order_note('تم إلغاء تخصيص الطلب', 0);
            }

            // Add success notice
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p><strong>✅ تم إلغاء تخصيص الطلب بنجاح</strong></p></div>';
            });
        }
    }



    /**
     * Settings page
     */
    public function settings_page() {
        // Check license before showing content - use direct check
        $license_manager = Hozi_Akadly_License_Manager::get_instance();
        if (!$license_manager->is_license_valid()) {
            wp_die(__('يجب تفعيل الترخيص للوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        // Handle settings save
        if (isset($_POST['save_settings'])) {
            $this->save_settings();
        }

        include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/settings.php';
    }

    /**
     * License page
     */
    public function license_page() {
        // Only allow access for admins
        if (!current_user_can('manage_options')) {
            wp_die(__('غير مصرح لك بالوصول إلى هذه الصفحة.', 'hozi-akadly'));
        }

        // Handle license activation/deactivation
        $license_manager = Hozi_Akadly_License_Manager::get_instance();

        // Process license actions
        if (isset($_POST['edd_license_activate'])) {
            $license_manager->activate_license();

            // Check if activation was successful using both transient and direct check
            $transient_check = get_transient('hozi_akadly_license_activated');
            $license_check = $license_manager->is_license_valid();

            if ($transient_check || $license_check) {
                delete_transient('hozi_akadly_license_activated');
                // Force refresh after successful activation
                wp_redirect(admin_url('admin.php?page=hozi-akadly&license_activated=1'));
                exit();
            }
        } elseif (isset($_POST['edd_license_deactivate'])) {
            $license_manager->deactivate_license();
        } elseif (isset($_POST['edd_license_check'])) {
            // Force license check
            delete_transient('hozi_akadly_last_license_check');
            $result = $license_manager->check_license();

            if ($result === 'valid') {
                wp_redirect(admin_url('admin.php?page=hozi-akadly-license&sl_activation=true&message=' . urlencode(__('الترخيص صالح ونشط.', 'hozi-akadly'))));
            } else {
                $status_message = $result ? sprintf(__('حالة الترخيص: %s', 'hozi-akadly'), $result) : __('فشل في التحقق من الترخيص.', 'hozi-akadly');
                wp_redirect(admin_url('admin.php?page=hozi-akadly-license&sl_activation=false&message=' . urlencode($status_message)));
            }
            exit();
        }

        // Check if license is valid to determine which view to show - use direct check
        $is_licensed = $license_manager->is_license_valid();

        if ($is_licensed) {
            // Show regular license management page
            $license_manager->license_page();
        } else {
            // Show license-only page
            include HOZI_AKADLY_PLUGIN_DIR . 'admin/views/license-only.php';
        }
    }



    /**
     * Handle agent actions
     */
    private function handle_agent_actions() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'hozi_agent_action')) {
            return;
        }

        $agent_manager = new Hozi_Akadly_Agent_Manager();

        switch ($_POST['action']) {
            case 'create_agent':
                $user_id = intval($_POST['user_id']);
                $data = array(
                    'name' => sanitize_text_field($_POST['name']),
                    'phone' => sanitize_text_field($_POST['phone']),
                    'max_orders_per_day' => intval($_POST['max_orders_per_day'])
                );

                if ($agent_manager->create_agent($user_id, $data)) {
                    add_action('admin_notices', function() {
                        echo '<div class="notice notice-success"><p>' . __('تم إنشاء الوكيل بنجاح', 'hozi-akadly') . '</p></div>';
                    });
                }
                break;

            case 'toggle_status':
                $agent_id = intval($_POST['agent_id']);
                if ($agent_manager->toggle_agent_status($agent_id)) {
                    add_action('admin_notices', function() {
                        echo '<div class="notice notice-success"><p>' . __('تم تحديث حالة الوكيل', 'hozi-akadly') . '</p></div>';
                    });
                }
                break;
        }
    }

    /**
     * Handle bulk assignment
     */
    private function handle_bulk_assignment() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'hozi_bulk_assign')) {
            return;
        }

        $order_ids = array_map('intval', $_POST['order_ids'] ?? array());
        $agent_id = intval($_POST['agent_id']);

        if (!empty($order_ids) && $agent_id) {
            $order_distributor = new Hozi_Akadly_Order_Distributor();
            $results = $order_distributor->bulk_assign_orders($order_ids, $agent_id);

            $success_count = count(array_filter($results));
            add_action('admin_notices', function() use ($success_count) {
                echo '<div class="notice notice-success"><p>' . sprintf(__('تم تخصيص %d طلب بنجاح', 'hozi-akadly'), $success_count) . '</p></div>';
            });
        }
    }

    /**
     * Create test agent
     */
    private function create_test_agent() {
        global $wpdb;

        // Check if test agent already exists
        $existing_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE name = 'وكيل تجريبي' LIMIT 1");

        if ($existing_agent) {
            return; // Test agent already exists
        }

        // Create test user
        $username = 'test_agent_' . time();
        $email = 'test_agent_' . time() . '@example.com';
        $password = 'TestAgent123!';

        $user_id = wp_create_user($username, $password, $email);

        if (!is_wp_error($user_id)) {
            // Add agent role
            $user = get_user_by('id', $user_id);
            $user->set_role('confirmation_agent');

            // Create agent record
            $wpdb->insert(
                $wpdb->prefix . 'hozi_agents',
                array(
                    'user_id' => $user_id,
                    'name' => 'وكيل تجريبي',
                    'email' => $email,
                    'phone' => '0501234567',
                    'is_active' => 1,
                    'max_orders_per_day' => 0,
                    'created_at' => current_time('mysql')
                )
            );
        }
    }

    /**
     * Handle confirmation update
     */
    private function handle_confirmation_update() {
        if (!wp_verify_nonce($_POST['hozi_akadly_nonce'], 'hozi_akadly_nonce')) {
            return;
        }

        $order_id = intval($_POST['order_id']);
        $status = sanitize_text_field($_POST['status']);
        $notes = sanitize_textarea_field($_POST['notes']);

        $order_distributor = new Hozi_Akadly_Order_Distributor();
        if ($order_distributor->update_confirmation_status($order_id, $status, $notes)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('تم تحديث حالة الطلب بنجاح', 'hozi-akadly') . '</p></div>';
            });
        }
    }

    /**
     * Handle admin as agent confirmation update (NEW FEATURE)
     */
    private function handle_admin_as_agent_confirmation($agent_id) {
        if (!wp_verify_nonce($_POST['hozi_akadly_nonce'], 'hozi_akadly_nonce')) {
            return;
        }

        $order_id = intval($_POST['order_id']);
        $status = sanitize_text_field($_POST['status']);
        $notes = sanitize_textarea_field($_POST['notes']);

        // Add admin note to distinguish from agent action
        $admin_user = wp_get_current_user();
        $admin_note = sprintf(
            '[تم بواسطة المشرف: %s نيابة عن الوكيل] %s',
            $admin_user->display_name,
            $notes
        );

        // Update confirmation status with admin note
        $order_distributor = new Hozi_Akadly_Order_Distributor();
        if ($order_distributor->update_confirmation_status($order_id, $status, $admin_note)) {
            // Log the admin action separately
            global $wpdb;
            $wpdb->insert(
                $wpdb->prefix . 'hozi_confirmation_logs',
                array(
                    'order_id' => $order_id,
                    'agent_id' => $agent_id,
                    'action' => 'admin_as_agent_' . $status,
                    'old_status' => 'pending_confirmation',
                    'new_status' => $status,
                    'notes' => sprintf(
                        'المشرف %s عمل نيابة عن الوكيل - %s',
                        $admin_user->display_name,
                        $notes
                    ),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                    'created_at' => current_time('mysql')
                )
            );

            // Add order note to show admin action
            $order = wc_get_order($order_id);
            if ($order) {
                $agent_info = $wpdb->get_row($wpdb->prepare(
                    "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                    $agent_id
                ));

                $order->add_order_note(
                    sprintf(
                        '👨‍💼 تم التأكيد بواسطة المشرف: %s%s%s%s%s',
                        $admin_user->display_name,
                        "\n🎭 نيابة عن الوكيل: " . ($agent_info->name ?? 'غير محدد'),
                        "\n📋 الحالة: " . $status,
                        $notes ? "\n📝 ملاحظة: " . $notes : '',
                        "\n⏰ " . current_time('Y/m/d H:i')
                    ),
                    0 // Private note
                );
            }

            add_action('admin_notices', function() use ($admin_user) {
                echo '<div class="notice notice-success"><p>' .
                     sprintf(__('تم تحديث حالة الطلب بنجاح بواسطة المشرف: %s', 'hozi-akadly'), $admin_user->display_name) .
                     '</p></div>';
            });
        }
    }

    /**
     * Save settings
     */
    private function save_settings() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'hozi_settings')) {
            return;
        }

        $settings = array(
            'distribution_method' => sanitize_text_field($_POST['distribution_method']),
            'auto_assign_new_orders' => sanitize_text_field($_POST['auto_assign_new_orders']),
            'order_statuses_to_assign' => array_map('sanitize_text_field', $_POST['order_statuses_to_assign'] ?? array()),
            'enable_delivery_tracking' => sanitize_text_field($_POST['enable_delivery_tracking'] ?? 'yes'),
            'delivery_tracking_access' => sanitize_text_field($_POST['delivery_tracking_access'] ?? 'assigned_agent_only'),
            'delivery_tracking_assigned_user' => sanitize_text_field($_POST['delivery_tracking_assigned_user'] ?? '')
        );

        foreach ($settings as $key => $value) {
            update_option('hozi_akadly_' . $key, $value);
        }

        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>' . __('تم حفظ الإعدادات بنجاح', 'hozi-akadly') . '</p></div>';
        });
    }

    /**
     * Add custom columns to orders list
     */
    public function add_order_columns($columns) {
        $new_columns = array();

        foreach ($columns as $key => $column) {
            $new_columns[$key] = $column;

            if ($key === 'order_status') {
                $new_columns['confirmation_agent'] = __('وكيل التأكيد', 'hozi-akadly');
                $new_columns['confirmation_status'] = __('حالة التأكيد', 'hozi-akadly');
            }
        }

        return $new_columns;
    }

    /**
     * Populate custom order columns
     */
    public function populate_order_columns($column, $order_id) {
        if ($column === 'confirmation_agent' || $column === 'confirmation_status') {
            $order_distributor = new Hozi_Akadly_Order_Distributor();
            $assignment = $order_distributor->get_order_assignment($order_id);

            if ($assignment) {
                if ($column === 'confirmation_agent') {
                    echo esc_html($assignment->agent_name);
                } elseif ($column === 'confirmation_status') {
                    $statuses = get_option('hozi_akadly_confirmation_statuses', array());
                    echo esc_html($statuses[$assignment->confirmation_status] ?? $assignment->confirmation_status);
                }
            } else {
                echo '<span style="color: #999;">' . __('غير مخصص', 'hozi-akadly') . '</span>';
            }
        }
    }

    /**
     * AJAX: Assign order
     */
    public function ajax_assign_order() {
        check_ajax_referer('hozi_akadly_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('غير مصرح', 'hozi-akadly'));
        }

        $order_id = intval($_POST['order_id']);
        $agent_id = intval($_POST['agent_id']);

        if (!$order_id || !$agent_id) {
            wp_send_json_error(array(
                'message' => __('بيانات غير صحيحة', 'hozi-akadly')
            ));
        }

        global $wpdb;

        // Check if order exists
        $order = wc_get_order($order_id);
        if (!$order) {
            wp_send_json_error(array(
                'message' => __('الطلب غير موجود', 'hozi-akadly')
            ));
        }

        // Check if agent exists and is active
        $agent = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE id = %d AND is_active = 1",
            $agent_id
        ));

        if (!$agent) {
            wp_send_json_error(array(
                'message' => __('الوكيل غير موجود أو غير نشط', 'hozi-akadly')
            ));
        }

        // Check if order is already assigned
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
            $order_id
        ));

        if ($existing) {
            // Update existing assignment
            $result = $wpdb->update(
                $wpdb->prefix . 'hozi_order_assignments',
                array(
                    'agent_id' => $agent_id,
                    'assigned_at' => current_time('mysql'),
                    'assignment_method' => 'manual'
                ),
                array('order_id' => $order_id),
                array('%d', '%s', '%s'),
                array('%d')
            );
        } else {
            // Create new assignment
            $result = $wpdb->insert(
                $wpdb->prefix . 'hozi_order_assignments',
                array(
                    'order_id' => $order_id,
                    'agent_id' => $agent_id,
                    'assignment_method' => 'manual',
                    'confirmation_status' => 'pending_confirmation',
                    'assigned_at' => current_time('mysql')
                ),
                array('%d', '%d', '%s', '%s', '%s')
            );
        }

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => sprintf(__('تم تخصيص الطلب #%d للوكيل %s بنجاح', 'hozi-akadly'), $order_id, $agent->name)
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('فشل في تخصيص الطلب - خطأ في قاعدة البيانات', 'hozi-akadly')
            ));
        }
    }

    /**
     * AJAX: Update confirmation status
     */
    public function ajax_update_confirmation() {
        try {
            // Check nonce
            if (!wp_verify_nonce($_POST['nonce'], 'hozi_akadly_nonce')) {
                wp_send_json_error(array('message' => 'فشل التحقق من الأمان'));
                return;
            }

            // Check if user is agent or has proper permissions
            $agent_manager = new Hozi_Akadly_Agent_Manager();
            $is_agent = $agent_manager->is_agent();

            if (!$is_agent && !current_user_can('edit_shop_orders')) {
                wp_send_json_error(array('message' => 'غير مصرح'));
                return;
            }

            $order_id = intval($_POST['order_id']);
            $status = sanitize_text_field($_POST['status']);
            $notes = sanitize_textarea_field($_POST['notes'] ?? '');

            if (!$order_id || !$status) {
                wp_send_json_error(array('message' => 'بيانات غير صحيحة'));
                return;
            }

            // Validate status
            $valid_statuses = array('confirmed', 'rejected', 'no_answer', 'callback_later');
            if (!in_array($status, $valid_statuses)) {
                wp_send_json_error(array('message' => 'حالة غير صحيحة'));
                return;
            }

            $order_distributor = new Hozi_Akadly_Order_Distributor();
            $result = $order_distributor->update_confirmation_status($order_id, $status, $notes);

            if ($result) {
                // Get updated agent statistics
                $current_agent = $agent_manager->get_current_agent();
                $agent_stats = null;

                if ($current_agent) {
                    $agent_stats = $agent_manager->get_agent_stats($current_agent->id);
                }

                wp_send_json_success(array(
                    'message' => 'تم تحديث حالة الطلب بنجاح',
                    'stats' => $agent_stats
                ));
            } else {
                wp_send_json_error(array('message' => 'فشل في تحديث حالة الطلب'));
            }
        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'خطأ في النظام: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX: Create agent
     */
    public function ajax_create_agent() {
        check_ajax_referer('hozi_akadly_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('غير مصرح', 'hozi-akadly'));
        }

        $user_id = intval($_POST['user_id']);
        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'phone' => sanitize_text_field($_POST['phone']),
            'max_orders_per_day' => intval($_POST['max_orders_per_day'])
        );

        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $result = $agent_manager->create_agent($user_id, $data);

        if ($result) {
            wp_send_json_success(array(
                'message' => __('تم إنشاء الوكيل بنجاح', 'hozi-akadly'),
                'agent_id' => $result
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('فشل في إنشاء الوكيل', 'hozi-akadly')
            ));
        }
    }

    /**
     * AJAX: Toggle agent status
     */
    public function ajax_toggle_agent_status() {
        check_ajax_referer('hozi_akadly_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('غير مصرح', 'hozi-akadly'));
        }

        $agent_id = intval($_POST['agent_id']);

        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $result = $agent_manager->toggle_agent_status($agent_id);

        if ($result) {
            wp_send_json_success(array(
                'message' => __('تم تحديث حالة الوكيل', 'hozi-akadly')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('فشل في تحديث حالة الوكيل', 'hozi-akadly')
            ));
        }
    }

    /**
     * Add meta boxes to order edit page
     */
    public function add_order_meta_boxes() {
        // Check if we're on shop_order edit page
        $screen = get_current_screen();
        if (!$screen || $screen->post_type !== 'shop_order') {
            return;
        }

        // Add order assignment metabox
        add_meta_box(
            'hozi-order-assignment',
            __('تخصيص الطلب - أكدلي', 'hozi-akadly'),
            array($this, 'order_assignment_metabox_callback'),
            'shop_order',
            'side',
            'high'
        );

        // Add order info metabox
        add_meta_box(
            'hozi_akadly_order_info',
            __('معلومات أكدلي - Akadly', 'hozi-akadly'),
            array($this, 'render_order_meta_box'),
            'shop_order',
            'side',
            'high'
        );
    }

    /**
     * Render order meta box
     */
    public function render_order_meta_box($post) {
        global $wpdb;

        $order_id = $post->ID;

        // Get assignment info
        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT oa.*, a.name as agent_name
             FROM {$wpdb->prefix}hozi_order_assignments oa
             LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
             WHERE oa.order_id = %d",
            $order_id
        ));

        // Get upsell info
        $upsell = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_upsell_tracking WHERE order_id = %d",
            $order_id
        ));

        ?>
        <div class="hozi-order-meta-box">
            <?php if ($assignment) : ?>
                <div class="hozi-meta-section">
                    <h4><?php _e('معلومات التخصيص', 'hozi-akadly'); ?></h4>
                    <p><strong><?php _e('الوكيل:', 'hozi-akadly'); ?></strong> <?php echo esc_html($assignment->agent_name); ?></p>
                    <p><strong><?php _e('حالة التأكيد:', 'hozi-akadly'); ?></strong>
                        <span class="hozi-status-badge status-<?php echo esc_attr($assignment->confirmation_status); ?>">
                            <?php
                            $statuses = array(
                                'pending_confirmation' => 'في انتظار التأكيد',
                                'confirmed' => 'مؤكد',
                                'rejected' => 'مرفوض',
                                'no_answer' => 'لم يرد',
                                'callback_later' => 'إعادة اتصال لاحقاً'
                            );
                            echo esc_html($statuses[$assignment->confirmation_status] ?? $assignment->confirmation_status);
                            ?>
                        </span>
                    </p>
                    <p><strong><?php _e('تاريخ التخصيص:', 'hozi-akadly'); ?></strong> <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($assignment->assigned_at))); ?></p>
                    <?php if ($assignment->confirmed_at) : ?>
                        <p><strong><?php _e('تاريخ التأكيد:', 'hozi-akadly'); ?></strong> <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($assignment->confirmed_at))); ?></p>
                    <?php endif; ?>
                    <?php if ($assignment->notes) : ?>
                        <div class="hozi-notes">
                            <strong><?php _e('ملاحظات الوكيل:', 'hozi-akadly'); ?></strong>
                            <div class="hozi-notes-content"><?php echo nl2br(esc_html($assignment->notes)); ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if ($upsell) : ?>
                <div class="hozi-meta-section">
                    <h4><?php _e('البيع الإضافي', 'hozi-akadly'); ?></h4>
                    <p><strong><?php _e('تم المحاولة:', 'hozi-akadly'); ?></strong>
                        <?php echo $upsell->upsell_attempted ? '✅ نعم' : '❌ لا'; ?>
                    </p>
                    <?php if ($upsell->upsell_attempted) : ?>
                        <p><strong><?php _e('النتيجة:', 'hozi-akadly'); ?></strong>
                            <?php echo $upsell->upsell_successful ? '✅ نجح' : '❌ لم ينجح'; ?>
                        </p>
                        <p><strong><?php _e('تاريخ المحاولة:', 'hozi-akadly'); ?></strong> <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($upsell->created_at))); ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if (!$assignment) : ?>
                <div class="hozi-meta-section">
                    <p class="hozi-no-assignment"><?php _e('لم يتم تخصيص هذا الطلب لأي وكيل بعد', 'hozi-akadly'); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <style>
        .hozi-order-meta-box {
            font-size: 13px;
        }

        .hozi-meta-section {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .hozi-meta-section:last-child {
            border-bottom: none;
        }

        .hozi-meta-section h4 {
            margin: 0 0 10px 0;
            color: #0073aa;
            font-size: 14px;
        }

        .hozi-meta-section p {
            margin: 5px 0;
        }

        .hozi-status-badge {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }

        .hozi-status-badge.status-confirmed {
            background: #4CAF50;
        }

        .hozi-status-badge.status-rejected {
            background: #f44336;
        }

        .hozi-status-badge.status-no_answer {
            background: #FF9800;
        }

        .hozi-status-badge.status-callback_later {
            background: #2196F3;
        }

        .hozi-status-badge.status-pending_confirmation {
            background: #9E9E9E;
        }

        .hozi-notes {
            margin-top: 10px;
        }

        .hozi-notes-content {
            background: #f9f9f9;
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid #0073aa;
            margin-top: 5px;
            font-style: italic;
        }

        .hozi-no-assignment {
            color: #666;
            font-style: italic;
        }
        </style>
        <?php
    }

    /**
     * AJAX: Save upsell result
     */
    public function ajax_save_upsell() {
        check_ajax_referer('hozi_akadly_nonce', 'nonce');

        if (!current_user_can('hozi_confirm_orders')) {
            wp_die(__('غير مصرح', 'hozi-akadly'));
        }

        $order_id = intval($_POST['order_id']);
        $successful = intval($_POST['successful']);

        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $current_agent = $agent_manager->get_current_agent();

        if (!$current_agent) {
            wp_send_json_error(array(
                'message' => __('لم يتم العثور على الوكيل', 'hozi-akadly')
            ));
        }

        global $wpdb;

        // Check if upsell record already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}hozi_upsell_tracking WHERE order_id = %d AND agent_id = %d",
            $order_id, $current_agent->id
        ));

        if ($existing) {
            // Update existing record
            $result = $wpdb->update(
                $wpdb->prefix . 'hozi_upsell_tracking',
                array(
                    'upsell_attempted' => 1,
                    'upsell_successful' => $successful
                ),
                array('id' => $existing)
            );
        } else {
            // Insert new record
            $result = $wpdb->insert(
                $wpdb->prefix . 'hozi_upsell_tracking',
                array(
                    'order_id' => $order_id,
                    'agent_id' => $current_agent->id,
                    'upsell_attempted' => 1,
                    'upsell_successful' => $successful,
                    'created_at' => current_time('mysql')
                )
            );
        }

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => __('تم حفظ نتيجة البيع الإضافي', 'hozi-akadly')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('فشل في حفظ نتيجة البيع الإضافي', 'hozi-akadly')
            ));
        }
    }

    /**
     * AJAX: Quick tracking status update
     */
    public function ajax_quick_tracking_update() {
        check_ajax_referer('hozi_quick_tracking', 'nonce');

        if (!current_user_can('edit_shop_orders')) {
            wp_die(__('غير مصرح', 'hozi-akadly'));
        }

        $order_id = intval($_POST['order_id']);
        $status = sanitize_text_field($_POST['status']);

        $order_tracker = new Hozi_Akadly_Order_Tracker();
        $result = $order_tracker->update_tracking_status($order_id, $status);

        if ($result) {
            wp_send_json_success(array(
                'message' => __('تم تحديث حالة الطلب بنجاح', 'hozi-akadly')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('فشل في تحديث حالة الطلب', 'hozi-akadly')
            ));
        }
    }

    /**
     * Get confirmed orders for agent tracking - ENHANCED VERSION
     */
    private function get_agent_confirmed_orders($agent_id) {
        global $wpdb;

        // Enhanced query - get orders confirmed by this agent, regardless of tracking agent
        $query = $wpdb->prepare(
            "SELECT DISTINCT
                    oa.order_id,
                    oa.agent_id as confirming_agent_id,
                    oa.confirmed_at,
                    oa.notes as confirmation_notes,
                    oa.confirmation_status,
                    oa.assigned_at,
                    p.post_date as order_date,
                    p.post_status,
                    ot.status as tracking_status,
                    ot.updated_at as last_tracking_update,
                    ot.reason_category,
                    ot.notes as tracking_notes,
                    ot.agent_id as tracking_agent_id,
                    COALESCE(pm_total.meta_value, '0') as order_total,
                    COALESCE(pm_customer.meta_value, '0') as customer_id,
                    COALESCE(pm_billing_first.meta_value, '') as billing_first_name,
                    COALESCE(pm_billing_last.meta_value, '') as billing_last_name,
                    COALESCE(pm_billing_phone.meta_value, '') as billing_phone
             FROM {$wpdb->prefix}hozi_order_assignments oa
             INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
             LEFT JOIN (
                 SELECT order_id, status, updated_at, reason_category, notes, agent_id,
                        ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY updated_at DESC) as rn
                 FROM {$wpdb->prefix}hozi_order_tracking
             ) ot ON (oa.order_id = ot.order_id AND ot.rn = 1)
             LEFT JOIN {$wpdb->prefix}postmeta pm_total ON (p.ID = pm_total.post_id AND pm_total.meta_key = '_order_total')
             LEFT JOIN {$wpdb->prefix}postmeta pm_customer ON (p.ID = pm_customer.post_id AND pm_customer.meta_key = '_customer_user')
             LEFT JOIN {$wpdb->prefix}postmeta pm_billing_first ON (p.ID = pm_billing_first.post_id AND pm_billing_first.meta_key = '_billing_first_name')
             LEFT JOIN {$wpdb->prefix}postmeta pm_billing_last ON (p.ID = pm_billing_last.post_id AND pm_billing_last.meta_key = '_billing_last_name')
             LEFT JOIN {$wpdb->prefix}postmeta pm_billing_phone ON (p.ID = pm_billing_phone.post_id AND pm_billing_phone.meta_key = '_billing_phone')
             WHERE oa.agent_id = %d
             AND oa.confirmation_status = 'confirmed'
             AND p.post_type = 'shop_order'
             AND p.post_status NOT IN ('trash', 'auto-draft', 'inherit')
             AND (oa.archived IS NULL OR oa.archived = 0)
             ORDER BY
                CASE
                    WHEN ot.status IS NULL THEN 0
                    ELSE 1
                END,
                oa.confirmed_at DESC
             LIMIT 100",
            $agent_id
        );

        $results = $wpdb->get_results($query);

        // Debug logging for commercial version
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("Hozi Akadly - Agent Tracking Query for Agent ID {$agent_id}: " . $wpdb->last_query);
            error_log("Hozi Akadly - Results count: " . count($results));
            if ($wpdb->last_error) {
                error_log("Hozi Akadly - SQL Error: " . $wpdb->last_error);
            }
        }

        return $results ? $results : array();
    }

    /**
     * Get agent tracking statistics
     */
    private function get_agent_tracking_stats($agent_id) {
        global $wpdb;

        // Get total confirmed orders for this agent (only existing, non-archived orders)
        $total_confirmed = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT oa.order_id)
             FROM {$wpdb->prefix}hozi_order_assignments oa
             INNER JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
             WHERE oa.agent_id = %d
             AND oa.confirmation_status = 'confirmed'
             AND p.post_type = 'shop_order'
             AND p.post_status != 'trash'
             AND (oa.archived IS NULL OR oa.archived = 0)",
            $agent_id
        ));

        // Get tracking statistics (only for existing, non-archived orders)
        $tracking_stats = $wpdb->get_row($wpdb->prepare(
            "SELECT
                COUNT(DISTINCT ot.order_id) as total_tracked,
                SUM(CASE WHEN ot.status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN ot.status LIKE 'rejected_%' THEN 1 ELSE 0 END) as rejected,
                SUM(CASE WHEN ot.status LIKE 'postponed_%' THEN 1 ELSE 0 END) as postponed,
                SUM(CASE WHEN ot.status LIKE 'exchange_%' THEN 1 ELSE 0 END) as exchange
             FROM {$wpdb->prefix}hozi_order_tracking ot
             INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
             INNER JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
             WHERE oa.agent_id = %d
             AND oa.confirmation_status = 'confirmed'
             AND p.post_type = 'shop_order'
             AND p.post_status != 'trash'
             AND (oa.archived IS NULL OR oa.archived = 0)",
            $agent_id
        ));

        return (object) array(
            'total_confirmed' => intval($total_confirmed),
            'total_tracked' => intval($tracking_stats->total_tracked ?? 0),
            'delivered' => intval($tracking_stats->delivered ?? 0),
            'rejected' => intval($tracking_stats->rejected ?? 0),
            'postponed' => intval($tracking_stats->postponed ?? 0),
            'exchange' => intval($tracking_stats->exchange ?? 0)
        );
    }

    /**
     * Filter agent orders based on criteria
     */
    private function filter_agent_orders($orders, $status_filter = '', $date_from = '', $date_to = '') {
        if (empty($orders)) {
            return $orders;
        }

        $filtered = $orders;

        // Filter by tracking status
        if ($status_filter) {
            $filtered = array_filter($filtered, function($order) use ($status_filter) {
                if ($status_filter === 'needs_update') {
                    return empty($order->tracking_status);
                }
                return $order->tracking_status === $status_filter;
            });
        }

        // Filter by date range
        if ($date_from || $date_to) {
            $filtered = array_filter($filtered, function($order) use ($date_from, $date_to) {
                $order_date = strtotime($order->order_date);

                if ($date_from && $order_date < strtotime($date_from)) {
                    return false;
                }

                if ($date_to && $order_date > strtotime($date_to . ' 23:59:59')) {
                    return false;
                }

                return true;
            });
        }

        return array_values($filtered);
    }

    /**
     * Handle agent tracking update
     */
    private function handle_agent_tracking_update() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'hozi_agent_tracking_update')) {
            return;
        }

        $order_id = intval($_POST['order_id']);
        $status = sanitize_text_field($_POST['status']);
        $reason_category = sanitize_text_field($_POST['reason_category']);
        $reason_details = sanitize_textarea_field($_POST['reason_details']);
        $notes = sanitize_textarea_field($_POST['notes']);

        $order_tracker = new Hozi_Akadly_Order_Tracker();
        $result = $order_tracker->update_tracking_status($order_id, $status, $reason_category, $reason_details, $notes);

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('تم تحديث حالة الطلب بنجاح', 'hozi-akadly') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('فشل في تحديث حالة الطلب', 'hozi-akadly') . '</p></div>';
            });
        }
    }

    /**
     * Hide menu items for agents
     */
    public function hide_menu_for_agents() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();

        if ($agent_manager->is_agent()) {
            // Hide all menu items except agent dashboard
            remove_submenu_page('hozi-akadly', 'hozi-akadly'); // Dashboard
            remove_submenu_page('hozi-akadly', 'hozi-akadly-analytics'); // Analytics
            remove_submenu_page('hozi-akadly', 'hozi-akadly-agents'); // Agents
            remove_submenu_page('hozi-akadly', 'hozi-akadly-assignments'); // Assignments
            remove_submenu_page('hozi-akadly', 'hozi-akadly-settings'); // Settings
            remove_submenu_page('hozi-akadly', 'hozi-akadly-license'); // License

            // Hide other WordPress menus for agents
            remove_menu_page('edit.php'); // Posts
            remove_menu_page('upload.php'); // Media
            remove_menu_page('edit.php?post_type=page'); // Pages
            remove_menu_page('edit-comments.php'); // Comments
            remove_menu_page('themes.php'); // Appearance
            remove_menu_page('plugins.php'); // Plugins
            remove_menu_page('users.php'); // Users
            remove_menu_page('tools.php'); // Tools
            remove_menu_page('options-general.php'); // Settings

            // Keep only essential WooCommerce menus
            // remove_menu_page('woocommerce'); // Keep WooCommerce main menu
            // remove_menu_page('edit.php?post_type=shop_order'); // Keep Orders
        }
    }

    /**
     * Redirect agents to their dashboard on login
     */
    public function redirect_agent_on_login($redirect_to, $request, $user) {
        if (isset($user->roles) && is_array($user->roles)) {
            $agent_manager = new Hozi_Akadly_Agent_Manager();
            if ($agent_manager->is_agent($user->ID)) {
                // Force redirect to admin area for agents
                return admin_url('admin.php?page=hozi-akadly-my-orders');
            }
        }
        return $redirect_to;
    }

    /**
     * Prevent agents from accessing frontend my-account
     */
    public function prevent_agent_frontend_access() {
        if (is_user_logged_in() && !is_admin()) {
            $agent_manager = new Hozi_Akadly_Agent_Manager();
            if ($agent_manager->is_agent()) {
                // If agent tries to access frontend, redirect to admin
                if (is_account_page() || is_page('my-account') || is_page('my-account-2')) {
                    wp_redirect(admin_url('admin.php?page=hozi-akadly-my-orders'));
                    exit;
                }
            }
        }
    }

    /**
     * AJAX: Get agent statistics
     */
    public function ajax_get_agent_stats() {
        check_ajax_referer('hozi_akadly_nonce', 'nonce');

        if (!current_user_can('hozi_confirm_orders')) {
            wp_die(__('غير مصرح', 'hozi-akadly'));
        }

        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $current_agent = $agent_manager->get_current_agent();

        if (!$current_agent) {
            wp_send_json_error(array(
                'message' => __('لم يتم العثور على الوكيل', 'hozi-akadly')
            ));
        }

        $stats = $this->get_agent_tracking_stats($current_agent->id);

        wp_send_json_success(array(
            'stats' => $stats
        ));
    }

    /**
     * AJAX: Update tracking status
     */
    public function ajax_update_tracking_status() {
        try {
            // Check nonce - support both old and new nonce names
            $nonce_valid = false;
            if (isset($_POST['nonce'])) {
                if (wp_verify_nonce($_POST['nonce'], 'hozi_tracking_update')) {
                    $nonce_valid = true;
                } elseif (wp_verify_nonce($_POST['nonce'], 'hozi_tracking_nonce')) {
                    $nonce_valid = true;
                }
            }

            if (!$nonce_valid) {
                wp_send_json_error('فشل التحقق من الأمان');
                return;
            }

            // Check if user is agent
            $agent_manager = new Hozi_Akadly_Agent_Manager();
            if (!$agent_manager->is_agent()) {
                wp_send_json_error('غير مصرح لك بهذا الإجراء');
                return;
            }

            $current_agent = $agent_manager->get_current_agent();
            if (!$current_agent) {
                wp_send_json_error('لم يتم العثور على بيانات الوكيل');
                return;
            }

            // Get form data
            $order_id = intval($_POST['order_id']);
            $status = sanitize_text_field($_POST['status']);
            $reason_category = sanitize_text_field($_POST['reason_category'] ?? '');
            $reason_details = sanitize_text_field($_POST['reason_details'] ?? '');
            $notes = sanitize_textarea_field($_POST['notes'] ?? '');

            // Validate order
            $order = wc_get_order($order_id);
            if (!$order) {
                wp_send_json_error('الطلب غير موجود');
                return;
            }

            // Check if order is assigned to this agent and confirmed
            global $wpdb;
            $assignment = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_assignments
                 WHERE order_id = %d AND agent_id = %d AND confirmation_status = 'confirmed'",
                $order_id,
                $current_agent->id
            ));

            if (!$assignment) {
                wp_send_json_error('هذا الطلب غير مخصص لك أو غير مؤكد');
                return;
            }

            // Update tracking status
            $order_tracker = new Hozi_Akadly_Order_Tracker();

            // Combine reason if both category and details exist
            $final_reason = $reason_category;
            if ($reason_details) {
                $final_reason = $reason_category ? $reason_category . ': ' . $reason_details : $reason_details;
            }

            $result = $order_tracker->update_tracking_status($order_id, $status, $final_reason, $notes);

            if ($result) {
                // 🎯 AUTO-ARCHIVE LOGIC: If status is 'delivered', archive the order
                if ($status === 'delivered') {
                    $archive_result = $wpdb->update(
                        $wpdb->prefix . 'hozi_order_assignments',
                        array(
                            'confirmation_status' => 'akadly_completed',
                            'archived' => 1,
                            'archived_at' => current_time('mysql'),
                            'notes' => ($assignment->notes ? $assignment->notes . ' ' : '') . '(مؤرشف تلقائياً - تم التوصيل)'
                        ),
                        array('order_id' => $order_id),
                        array('%s', '%d', '%s', '%s'),
                        array('%d')
                    );

                    if ($archive_result) {
                        // Update WooCommerce order status to completed
                        $order->update_status('completed', __('تم إكمال الطلب - أكدلي مكتمل (تم التوصيل)', 'hozi-akadly'));

                        // Add archive note
                        $archive_note = sprintf(
                            '🗂️ تم أرشفة الطلب تلقائياً - تم التوصيل%s%s%s',
                            "\n👤 الوكيل: " . ($current_agent->name ?? 'غير محدد'),
                            $notes ? "\n📝 ملاحظة: " . $notes : '',
                            "\n⏰ تاريخ الأرشفة: " . current_time('Y/m/d H:i')
                        );
                        $order->add_order_note($archive_note, 0); // Private note

                        // Log the archive action
                        Hozi_Akadly_Database::log_action(
                            $order_id,
                            $current_agent->id,
                            'auto_archived_delivered',
                            'confirmed',
                            'akadly_completed',
                            'تم الأرشفة تلقائياً - تم التوصيل'
                        );

                        $message = '✅ تم تحديث حالة الطلب وأرشفته تلقائياً - أكدلي مكتمل!';
                    } else {
                        $message = 'تم تحديث حالة الطلب بنجاح';
                    }
                } else {
                    $message = 'تم تحديث حالة الطلب بنجاح';
                }

                // Get updated stats
                $stats = $this->get_agent_tracking_stats($current_agent->id);

                wp_send_json_success(array(
                    'message' => $message,
                    'stats' => $stats,
                    'archived' => ($status === 'delivered') // Flag to indicate if order was archived
                ));
            } else {
                wp_send_json_error('فشل في تحديث حالة الطلب');
            }

        } catch (Exception $e) {
            wp_send_json_error('خطأ في النظام: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Bulk update tracking status
     */
    public function ajax_bulk_update_tracking_status() {
        check_ajax_referer('hozi_bulk_update_nonce', 'nonce');

        if (!current_user_can('hozi_confirm_orders')) {
            wp_die(__('غير مصرح', 'hozi-akadly'));
        }

        $order_ids = array_map('intval', $_POST['order_ids'] ?? array());
        $status = sanitize_text_field($_POST['status'] ?? '');
        $reason_category = sanitize_text_field($_POST['reason'] ?? '');
        $notes = sanitize_textarea_field($_POST['notes'] ?? '');

        if (empty($order_ids) || empty($status)) {
            wp_send_json_error(array(
                'message' => __('بيانات غير صحيحة', 'hozi-akadly')
            ));
        }

        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $current_agent = $agent_manager->get_current_agent();

        if (!$current_agent) {
            wp_send_json_error(array(
                'message' => __('لم يتم العثور على الوكيل', 'hozi-akadly')
            ));
        }

        $order_tracker = new Hozi_Akadly_Order_Tracker();
        $success_count = 0;
        $failed_orders = array();

        foreach ($order_ids as $order_id) {
            $result = $order_tracker->update_tracking_status($order_id, $status, $reason_category, $notes, $current_agent->id);
            if ($result) {
                $success_count++;
            } else {
                $failed_orders[] = $order_id;
            }
        }

        // Get updated stats
        $stats = $this->get_agent_tracking_stats($current_agent->id);

        if ($success_count > 0) {
            $message = sprintf(
                __('تم تحديث %d من %d طلبية بنجاح', 'hozi-akadly'),
                $success_count,
                count($order_ids)
            );

            if (!empty($failed_orders)) {
                $message .= sprintf(
                    __(' (فشل في تحديث الطلبيات: %s)', 'hozi-akadly'),
                    implode(', ', $failed_orders)
                );
            }

            wp_send_json_success(array(
                'message' => $message,
                'success_count' => $success_count,
                'failed_count' => count($failed_orders),
                'stats' => $stats
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('فشل في تحديث جميع الطلبيات', 'hozi-akadly')
            ));
        }
    }

    /**
     * Clear all confirmation data properly
     */
    public function clear_all_confirmation_data() {
        global $wpdb;

        // Clear confirmation logs
        $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_confirmation_logs");

        // Clear order tracking
        $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_order_tracking");

        // Reset order assignments to pending
        $wpdb->query("UPDATE {$wpdb->prefix}hozi_order_assignments
                     SET confirmation_status = 'pending_confirmation',
                         confirmed_at = NULL,
                         notes = NULL");

        return true;
    }



    /**
     * AJAX: Reset confirmation logs
     */
    public function ajax_reset_logs() {
        try {
            // Check nonce
            if (!wp_verify_nonce($_POST['nonce'], 'hozi_akadly_nonce')) {
                wp_send_json_error(array('message' => 'فشل التحقق من الأمان'));
                return;
            }

            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'غير مصرح'));
                return;
            }

            global $wpdb;

            // Clear confirmation logs only
            $result1 = $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_confirmation_logs");

            // Clear order tracking
            $result2 = $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_order_tracking");

            // Reset order assignments to pending
            $result3 = $wpdb->query("UPDATE {$wpdb->prefix}hozi_order_assignments
                                   SET confirmation_status = 'pending_confirmation',
                                       confirmed_at = NULL,
                                       notes = NULL");

            if ($result1 !== false && $result2 !== false && $result3 !== false) {
                wp_send_json_success(array(
                    'message' => 'تم مسح سجلات التأكيد بنجاح'
                ));
            } else {
                wp_send_json_error(array(
                    'message' => 'فشل في مسح السجلات'
                ));
            }
        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'خطأ في النظام: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX: Reset all assignments
     */
    public function ajax_reset_assignments() {
        try {
            // Check nonce
            if (!wp_verify_nonce($_POST['nonce'], 'hozi_akadly_nonce')) {
                wp_send_json_error(array('message' => 'فشل التحقق من الأمان'));
                return;
            }

            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'غير مصرح'));
                return;
            }

            global $wpdb;

            // Clear all data
            $result1 = $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_confirmation_logs");
            $result2 = $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_order_tracking");
            $result3 = $wpdb->query("DELETE FROM {$wpdb->prefix}hozi_order_assignments");

            if ($result1 !== false && $result2 !== false && $result3 !== false) {
                wp_send_json_success(array(
                    'message' => 'تم مسح جميع التخصيصات والسجلات بنجاح'
                ));
            } else {
                wp_send_json_error(array(
                    'message' => 'فشل في مسح البيانات'
                ));
            }
        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'خطأ في النظام: ' . $e->getMessage()));
        }
    }



    /**
     * AJAX: Check for new orders assigned to current agent
     */
    public function ajax_check_new_orders() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'hozi_check_new_orders')) {
            wp_send_json_error(array('message' => 'فشل التحقق من الأمان'));
            return;
        }

        // Check if user is an agent
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        if (!$agent_manager->is_agent()) {
            wp_send_json_error(array('message' => 'غير مصرح لك بهذا الإجراء'));
            return;
        }

        $current_agent = $agent_manager->get_current_agent();
        if (!$current_agent) {
            wp_send_json_error(array('message' => 'لم يتم العثور على بيانات الوكيل'));
            return;
        }

        try {
            $order_distributor = new Hozi_Akadly_Order_Distributor();

            // Get current count of pending orders for this agent
            $pending_count = $order_distributor->get_pending_assignments_count($current_agent->id);

            wp_send_json_success(array(
                'count' => $pending_count,
                'agent_id' => $current_agent->id,
                'timestamp' => current_time('timestamp')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'خطأ في النظام: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX: Clear completed orders for current agent
     */
    public function ajax_clear_completed_orders() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'hozi_clear_completed_orders')) {
            wp_send_json_error(array('message' => 'فشل التحقق من الأمان'));
            return;
        }

        // Check if user is an agent
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        if (!$agent_manager->is_agent()) {
            wp_send_json_error(array('message' => 'غير مصرح لك بهذا الإجراء'));
            return;
        }

        $current_agent = $agent_manager->get_current_agent();
        if (!$current_agent) {
            wp_send_json_error(array('message' => 'لم يتم العثور على بيانات الوكيل'));
            return;
        }

        try {
            global $wpdb;

            // Get completed orders for this agent
            $completed_orders = $wpdb->get_results($wpdb->prepare("
                SELECT a.order_id, a.id as assignment_id
                FROM {$wpdb->prefix}hozi_order_assignments a
                LEFT JOIN {$wpdb->prefix}hozi_order_tracking t ON a.order_id = t.order_id
                WHERE a.agent_id = %d
                AND a.confirmation_status = 'confirmed'
                AND t.tracking_status = 'delivered'
                AND (a.archived IS NULL OR a.archived = 0)
            ", $current_agent->id));

            if (empty($completed_orders)) {
                wp_send_json_error(array('message' => 'لا توجد طلبات مكتملة للمسح'));
                return;
            }

            $archived_count = 0;

            // Archive each completed order
            foreach ($completed_orders as $order_data) {
                $result = $wpdb->update(
                    $wpdb->prefix . 'hozi_order_assignments',
                    array(
                        'confirmation_status' => 'akadly_completed',
                        'archived' => 1,
                        'archived_at' => current_time('mysql'),
                        'notes' => ($order_data->notes ?? '') . ' (مؤرشف تلقائياً - مسح الطلبات المكتملة)'
                    ),
                    array('id' => $order_data->assignment_id),
                    array('%s', '%d', '%s', '%s'),
                    array('%d')
                );

                if ($result) {
                    $archived_count++;

                    // Update WooCommerce order status
                    $order = wc_get_order($order_data->order_id);
                    if ($order) {
                        $order->update_status('completed', __('تم إكمال الطلب - أكدلي مكتمل (مسح تلقائي)', 'hozi-akadly'));

                        // Add archive note
                        $archive_note = sprintf(
                            '🗂️ تم أرشفة الطلب تلقائياً - مسح الطلبات المكتملة%s%s',
                            "\n👤 الوكيل: " . ($current_agent->name ?? 'غير محدد'),
                            "\n⏰ تاريخ الأرشفة: " . current_time('Y/m/d H:i')
                        );
                        $order->add_order_note($archive_note, 0); // Private note
                    }

                    // Log the archive action
                    Hozi_Akadly_Database::log_action(
                        $order_data->order_id,
                        $current_agent->id,
                        'bulk_archived',
                        'confirmed',
                        'akadly_completed',
                        'تم الأرشفة تلقائياً - مسح الطلبات المكتملة'
                    );
                }
            }

            if ($archived_count > 0) {
                wp_send_json_success(array(
                    'message' => sprintf('تم أرشفة %d طلب مكتمل بنجاح', $archived_count),
                    'archived_count' => $archived_count
                ));
            } else {
                wp_send_json_error(array('message' => 'فشل في أرشفة الطلبات'));
            }

        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'خطأ في النظام: ' . $e->getMessage()));
        }
    }

    /**
     * Get agent archived orders
     */
    private function get_agent_archived_orders($agent_id, $limit = 10, $offset = 0) {
        global $wpdb;

        $query = "
            SELECT
                a.order_id,
                a.confirmation_status,
                a.confirmed_at,
                a.archived_at,
                a.notes,
                p.post_date as order_date
            FROM {$wpdb->prefix}hozi_order_assignments a
            LEFT JOIN {$wpdb->posts} p ON a.order_id = p.ID
            WHERE a.agent_id = %d
            AND (a.archived = 1 OR a.confirmation_status = 'akadly_completed')
            ORDER BY a.archived_at DESC, a.confirmed_at DESC
            LIMIT %d OFFSET %d
        ";

        return $wpdb->get_results($wpdb->prepare($query, $agent_id, $limit, $offset));
    }

    /**
     * Get agent archived orders count
     */
    private function get_agent_archived_orders_count($agent_id) {
        global $wpdb;

        $query = "
            SELECT COUNT(*)
            FROM {$wpdb->prefix}hozi_order_assignments a
            WHERE a.agent_id = %d
            AND (a.archived = 1 OR a.confirmation_status = 'akadly_completed')
        ";

        return $wpdb->get_var($wpdb->prepare($query, $agent_id));
    }

    /**
     * Get agent archive statistics
     */
    private function get_agent_archive_stats($agent_id) {
        global $wpdb;

        // Get total archived
        $total_archived = $this->get_agent_archived_orders_count($agent_id);

        // Get this month
        $this_month = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->prefix}hozi_order_assignments a
            WHERE a.agent_id = %d
            AND (a.archived = 1 OR a.confirmation_status = 'akadly_completed')
            AND MONTH(COALESCE(a.archived_at, a.confirmed_at)) = MONTH(CURRENT_DATE())
            AND YEAR(COALESCE(a.archived_at, a.confirmed_at)) = YEAR(CURRENT_DATE())
        ", $agent_id));

        // Get this week
        $this_week = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->prefix}hozi_order_assignments a
            WHERE a.agent_id = %d
            AND (a.archived = 1 OR a.confirmation_status = 'akadly_completed')
            AND WEEK(COALESCE(a.archived_at, a.confirmed_at)) = WEEK(CURRENT_DATE())
            AND YEAR(COALESCE(a.archived_at, a.confirmed_at)) = YEAR(CURRENT_DATE())
        ", $agent_id));

        // Calculate daily average (last 30 days)
        $last_30_days = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->prefix}hozi_order_assignments a
            WHERE a.agent_id = %d
            AND (a.archived = 1 OR a.confirmation_status = 'akadly_completed')
            AND COALESCE(a.archived_at, a.confirmed_at) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
        ", $agent_id));

        $daily_average = round($last_30_days / 30, 1);

        return (object) array(
            'total_archived' => $total_archived ?: 0,
            'this_month' => $this_month ?: 0,
            'this_week' => $this_week ?: 0,
            'daily_average' => $daily_average ?: 0
        );
    }
}
