<?php
/**
 * Quick Diagnosis for Akadly Plugin
 * تشخيص سريع لإضافة أكدلي
 */

// Security check
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/../../..');
}

require_once ABSPATH . 'wp-config.php';
require_once ABSPATH . 'wp-includes/wp-db.php';
require_once ABSPATH . 'wp-includes/functions.php';

// Initialize WordPress
global $wpdb;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>تشخيص سريع - أكدلي</title></head><body>";
echo "<h1>🔍 تشخيص سريع - إضافة أكدلي</h1>";

// Check if plugin is active
$active_plugins = get_option('active_plugins', array());
$plugin_active = in_array('Akadly/hozi-akadly.php', $active_plugins);

echo "<h2>📋 حالة الإضافة</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>حالة الإضافة:</strong> " . ($plugin_active ? "✅ مفعلة" : "❌ غير مفعلة") . "</p>";
echo "</div>";

if (!$plugin_active) {
    echo "<p style='color: red;'>⚠️ الإضافة غير مفعلة. يرجى تفعيلها من لوحة تحكم ووردبريس.</p>";
    echo "</body></html>";
    exit;
}

// Check database tables
echo "<h2>🗄️ حالة قاعدة البيانات</h2>";

$tables_to_check = array(
    'hozi_agents' => 'جدول الوكلاء',
    'hozi_order_assignments' => 'جدول تخصيص الطلبات',
    'hozi_confirmation_logs' => 'جدول سجلات التأكيد',
    'hozi_order_tracking' => 'جدول تتبع الطلبات',
    'hozi_upsell_tracking' => 'جدول تتبع البيع الإضافي'
);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
foreach ($tables_to_check as $table => $description) {
    $full_table_name = $wpdb->prefix . $table;
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'") == $full_table_name;
    
    echo "<p><strong>$description:</strong> " . ($table_exists ? "✅ موجود" : "❌ غير موجود") . "</p>";
}
echo "</div>";

// Check archive columns specifically
echo "<h2>🗂️ حالة أعمدة الأرشيف</h2>";
$assignments_table = $wpdb->prefix . 'hozi_order_assignments';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$assignments_table'") == $assignments_table;

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
if ($table_exists) {
    $archived_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived'");
    $archived_at_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived_at'");
    
    echo "<p><strong>عمود 'archived':</strong> " . (!empty($archived_exists) ? "✅ موجود" : "❌ غير موجود") . "</p>";
    echo "<p><strong>عمود 'archived_at':</strong> " . (!empty($archived_at_exists) ? "✅ موجود" : "❌ غير موجود") . "</p>";
    
    if (empty($archived_exists) || empty($archived_at_exists)) {
        echo "<p style='color: #dc3545;'>⚠️ أعمدة الأرشيف مفقودة. استخدم زر التحديث أدناه.</p>";
        echo "<p><a href='test-archive-feature.php?update_db=1' class='button' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔧 تحديث قاعدة البيانات</a></p>";
    }
} else {
    echo "<p style='color: #dc3545;'>❌ جدول تخصيص الطلبات غير موجود!</p>";
    echo "<p><a href='/wp-admin/admin.php?page=hozi-akadly' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>إنشاء الجداول</a></p>";
}
echo "</div>";

// Check for agents
echo "<h2>👥 حالة الوكلاء</h2>";
$agents_table = $wpdb->prefix . 'hozi_agents';
$agents_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$agents_table'") == $agents_table;

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
if ($agents_table_exists) {
    $agents_count = $wpdb->get_var("SELECT COUNT(*) FROM $agents_table");
    $active_agents = $wpdb->get_var("SELECT COUNT(*) FROM $agents_table WHERE is_active = 1");
    
    echo "<p><strong>إجمالي الوكلاء:</strong> $agents_count</p>";
    echo "<p><strong>الوكلاء النشطون:</strong> $active_agents</p>";
    
    if ($agents_count == 0) {
        echo "<p style='color: #dc3545;'>⚠️ لا يوجد وكلاء مسجلون.</p>";
    }
} else {
    echo "<p style='color: #dc3545;'>❌ جدول الوكلاء غير موجود!</p>";
}
echo "</div>";

// Check for orders
echo "<h2>📦 حالة الطلبات</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

if ($table_exists) {
    $total_assignments = $wpdb->get_var("SELECT COUNT(*) FROM $assignments_table");
    $confirmed_orders = $wpdb->get_var("SELECT COUNT(*) FROM $assignments_table WHERE confirmation_status = 'confirmed'");
    $pending_orders = $wpdb->get_var("SELECT COUNT(*) FROM $assignments_table WHERE confirmation_status = 'pending_confirmation'");
    
    // Check archived orders (handle missing column gracefully)
    $archived_orders = 0;
    if (!empty($archived_exists)) {
        $archived_orders = $wpdb->get_var("SELECT COUNT(*) FROM $assignments_table WHERE archived = 1 OR confirmation_status = 'akadly_completed'");
    }
    
    echo "<p><strong>إجمالي الطلبات المخصصة:</strong> $total_assignments</p>";
    echo "<p><strong>الطلبات المؤكدة:</strong> $confirmed_orders</p>";
    echo "<p><strong>الطلبات المعلقة:</strong> $pending_orders</p>";
    echo "<p><strong>الطلبات المؤرشفة:</strong> $archived_orders</p>";
} else {
    echo "<p style='color: #dc3545;'>❌ لا يمكن فحص الطلبات - الجدول غير موجود!</p>";
}
echo "</div>";

// Quick actions
echo "<h2>⚡ إجراءات سريعة</h2>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly-my-orders' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📋 طلباتي</a>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly-my-tracking' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📊 تتبع الطلبات</a>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly-my-archived' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🗂️ الطلبات المؤرشفة</a>";
echo "<a href='test-archive-feature.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🧪 اختبار الأرشفة</a>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🏠 لوحة التحكم</a>";
echo "</div>";

// System info
echo "<h2>ℹ️ معلومات النظام</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px;'>";
echo "<p><strong>WordPress Version:</strong> " . get_bloginfo('version') . "</p>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Database Prefix:</strong> " . $wpdb->prefix . "</p>";
echo "<p><strong>Current Time:</strong> " . current_time('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Plugin Directory:</strong> " . (defined('HOZI_AKADLY_PLUGIN_DIR') ? HOZI_AKADLY_PLUGIN_DIR : 'غير محدد') . "</p>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666; font-size: 12px;'>تشخيص سريع - إضافة أكدلي | " . date('Y-m-d H:i:s') . "</p>";
echo "</body></html>";
?>
