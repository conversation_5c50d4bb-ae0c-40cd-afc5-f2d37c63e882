<?php
/**
 * أكدلي - Akadly System Check
 * فحص شامل لنظام أكدلي
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only allow admin access
if (!current_user_can('manage_options')) {
    wp_die('غير مصرح');
}

echo '<div class="wrap">';
echo '<h1>🔍 فحص شامل لنظام أكدلي - Akadly</h1>';

// 1. Check if plugin is active
echo '<h2>1️⃣ حالة الإضافة</h2>';
if (is_plugin_active('hozi-akadly/hozi-akadly.php')) {
    echo '<p style="color: green;">✅ الإضافة مفعلة</p>';
} else {
    echo '<p style="color: red;">❌ الإضافة غير مفعلة</p>';
}

// 2. Check database tables
echo '<h2>2️⃣ جداول قاعدة البيانات</h2>';
global $wpdb;

$tables = [
    'hozi_agents' => 'جدول الوكلاء',
    'hozi_order_assignments' => 'جدول تخصيص الطلبات',
    'hozi_confirmation_logs' => 'جدول سجلات التأكيد',
    'hozi_upsell_tracking' => 'جدول تتبع البيع الإضافي',
    'hozi_order_tracking' => 'جدول تتبع الطلبات'
];

foreach ($tables as $table => $description) {
    $table_name = $wpdb->prefix . $table;
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo "<p style='color: green;'>✅ $description ($table_name) - $count سجل</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($table_name) غير موجود</p>";
    }
}

// 3. Check license status
echo '<h2>3️⃣ حالة الترخيص</h2>';
if (class_exists('Hozi_Akadly_License_Manager')) {
    $license_manager = Hozi_Akadly_License_Manager::get_instance();
    if ($license_manager->is_license_valid()) {
        echo '<p style="color: green;">✅ الترخيص صالح ومفعل</p>';
    } else {
        echo '<p style="color: red;">❌ الترخيص غير صالح أو غير مفعل</p>';
    }
} else {
    echo '<p style="color: red;">❌ مدير الترخيص غير متوفر</p>';
}

// 4. Check agents
echo '<h2>4️⃣ الوكلاء</h2>';
if (class_exists('Hozi_Akadly_Agent_Manager')) {
    $agent_manager = new Hozi_Akadly_Agent_Manager();
    $agents = $agent_manager->get_agents();
    $active_agents = $agent_manager->get_agents(true);
    
    echo "<p>📊 إجمالي الوكلاء: " . count($agents) . "</p>";
    echo "<p>✅ الوكلاء النشطون: " . count($active_agents) . "</p>";
    
    if (!empty($agents)) {
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>الاسم</th><th>المستخدم</th><th>الحالة</th><th>الطلبات المعلقة</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($agents as $agent) {
            $user = get_user_by('id', $agent->user_id);
            $pending_orders = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments 
                 WHERE agent_id = %d AND confirmation_status = 'pending_confirmation'",
                $agent->id
            ));
            
            $status_color = $agent->is_active ? 'green' : 'red';
            $status_text = $agent->is_active ? 'نشط' : 'غير نشط';
            
            echo "<tr>";
            echo "<td>" . esc_html($agent->name) . "</td>";
            echo "<td>" . ($user ? esc_html($user->user_login) : 'غير موجود') . "</td>";
            echo "<td style='color: $status_color;'>$status_text</td>";
            echo "<td>$pending_orders</td>";
            echo "</tr>";
        }
        
        echo '</tbody></table>';
    }
} else {
    echo '<p style="color: red;">❌ مدير الوكلاء غير متوفر</p>';
}

// 5. Check orders and assignments
echo '<h2>5️⃣ الطلبات والتخصيصات</h2>';

// Get order statistics
$total_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'shop_order' AND post_status != 'trash'");
$assigned_orders = $wpdb->get_var("SELECT COUNT(DISTINCT order_id) FROM {$wpdb->prefix}hozi_order_assignments");
$confirmed_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments WHERE confirmation_status = 'confirmed'");
$pending_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments WHERE confirmation_status = 'pending_confirmation'");

echo "<p>📦 إجمالي الطلبات: $total_orders</p>";
echo "<p>👥 الطلبات المخصصة: $assigned_orders</p>";
echo "<p>✅ الطلبات المؤكدة: $confirmed_orders</p>";
echo "<p>⏳ الطلبات المعلقة: $pending_orders</p>";

// 6. Check AJAX handlers
echo '<h2>6️⃣ معالجات AJAX</h2>';
$ajax_actions = [
    'hozi_update_confirmation' => 'تحديث حالة التأكيد',
    'hozi_save_upsell' => 'حفظ البيع الإضافي',
    'hozi_assign_order' => 'تخصيص الطلب',
    'hozi_update_tracking_status' => 'تحديث حالة التتبع'
];

foreach ($ajax_actions as $action => $description) {
    if (has_action("wp_ajax_$action")) {
        echo "<p style='color: green;'>✅ $description ($action)</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($action) غير مسجل</p>";
    }
}

// 7. Check nonce functionality
echo '<h2>7️⃣ فحص Nonce</h2>';
$test_nonce = wp_create_nonce('hozi_akadly_nonce');
if ($test_nonce) {
    echo "<p style='color: green;'>✅ إنشاء Nonce يعمل: $test_nonce</p>";
    
    if (wp_verify_nonce($test_nonce, 'hozi_akadly_nonce')) {
        echo "<p style='color: green;'>✅ التحقق من Nonce يعمل</p>";
    } else {
        echo "<p style='color: red;'>❌ التحقق من Nonce لا يعمل</p>";
    }
} else {
    echo "<p style='color: red;'>❌ إنشاء Nonce لا يعمل</p>";
}

// 8. Check user capabilities
echo '<h2>8️⃣ صلاحيات المستخدمين</h2>';
$current_user = wp_get_current_user();
echo "<p>👤 المستخدم الحالي: " . $current_user->user_login . "</p>";

$capabilities = [
    'manage_options' => 'إدارة الخيارات',
    'hozi_view_assigned_orders' => 'عرض الطلبات المخصصة',
    'hozi_confirm_orders' => 'تأكيد الطلبات',
    'edit_shop_orders' => 'تعديل طلبات المتجر'
];

foreach ($capabilities as $cap => $description) {
    if (current_user_can($cap)) {
        echo "<p style='color: green;'>✅ $description ($cap)</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ $description ($cap) غير متوفرة</p>";
    }
}

echo '</div>';

// Add some CSS for better presentation
echo '<style>
.wrap h2 { 
    background: #f1f1f1; 
    padding: 10px; 
    border-left: 4px solid #0073aa; 
    margin-top: 20px; 
}
.wrap p { 
    margin: 5px 0; 
    padding: 5px 10px; 
}
.wp-list-table { 
    margin-top: 10px; 
}
</style>';
?>
