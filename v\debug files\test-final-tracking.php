<?php
/**
 * Final Test for Auto Tracking System
 * Complete test of the automatic transition system
 */

// WordPress environment
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo "<h1>🎯 الاختبار النهائي للنظام التلقائي</h1>";

global $wpdb;

// Get test agent
$test_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 LIMIT 1");

if (!$test_agent) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ لا يوجد وكلاء نشطين!</h3>";
    echo "</div>";
    exit;
}

echo "<h2>🧪 الوكيل: {$test_agent->name} (ID: {$test_agent->id})</h2>";

// Handle complete action
if (isset($_GET['action']) && $_GET['action'] === 'complete' && isset($_GET['order_id'])) {
    $order_id = intval($_GET['order_id']);
    
    echo "<h2>🎯 اختبار إكمال الطلب #{$order_id}:</h2>";
    
    // Check current status
    $order = wc_get_order($order_id);
    if (!$order) {
        echo "<p>❌ الطلب غير موجود</p>";
    } else {
        echo "<p>📝 الحالة الحالية: {$order->get_status()}</p>";
        
        // Check assignment
        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments 
             WHERE order_id = %d AND confirmation_status = 'confirmed'",
            $order_id
        ));
        
        if (!$assignment) {
            echo "<p>❌ الطلب غير مؤكد</p>";
        } else {
            echo "<p>✅ الطلب مؤكد من الوكيل ID: {$assignment->agent_id}</p>";
            
            // Check existing tracking
            $existing_tracking = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $order_id
            ));
            
            if ($existing_tracking) {
                echo "<p>⚠️ الطلب له تتبع موجود: {$existing_tracking->status}</p>";
            } else {
                echo "<p>✅ الطلب ليس له تتبع - مناسب للاختبار</p>";
            }
            
            // Update to completed
            echo "<p>🔄 تغيير الحالة إلى 'completed'...</p>";
            $order->update_status('completed', 'اختبار النظام التلقائي النهائي');
            
            // Wait and check
            sleep(2);
            
            $new_tracking = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d ORDER BY created_at DESC LIMIT 1",
                $order_id
            ));
            
            if ($new_tracking) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h4>🎉 نجح النظام التلقائي!</h4>";
                echo "<p><strong>تفاصيل التتبع:</strong></p>";
                echo "<p>• الحالة: {$new_tracking->status}</p>";
                echo "<p>• الوكيل: {$new_tracking->agent_id}</p>";
                echo "<p>• السبب: {$new_tracking->reason_category}</p>";
                echo "<p>• الملاحظات: {$new_tracking->notes}</p>";
                echo "<p>• التاريخ: {$new_tracking->created_at}</p>";
                echo "</div>";
                
                echo "<p><a href='admin.php?page=hozi-akadly-my-tracking' target='_blank' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 عرض في صفحة التتبع</a></p>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                echo "<h4>❌ فشل النظام التلقائي!</h4>";
                echo "<p>لم يتم إضافة التتبع تلقائياً.</p>";
                echo "</div>";
            }
        }
    }
    
    echo "<hr>";
}

// Show orders ready for testing
echo "<h2>📋 الطلبات المؤكدة بدون تتبع:</h2>";

$ready_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmed_at, p.post_status
     FROM {$wpdb->prefix}hozi_order_assignments oa
     LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND ot.order_id IS NULL
     AND p.post_status != 'wc-completed'
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $test_agent->id
));

if ($ready_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة WC</th><th>تاريخ التأكيد</th><th>إجراءات</th></tr>";
    
    foreach ($ready_orders as $ready_order) {
        echo "<tr>";
        echo "<td>#{$ready_order->order_id}</td>";
        echo "<td>{$ready_order->post_status}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($ready_order->confirmed_at)) . "</td>";
        echo "<td>";
        echo "<a href='?action=complete&order_id={$ready_order->order_id}' style='background: #4caf50; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>🎯 اختبار الإكمال</a>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات مؤكدة بدون تتبع.</p>";
}

// Show orders with tracking
echo "<h2>📊 الطلبات في نظام التتبع:</h2>";

$tracked_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, ot.status, ot.notes, ot.created_at, p.post_status
     FROM {$wpdb->prefix}hozi_order_assignments oa
     INNER JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
     LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     ORDER BY ot.created_at DESC
     LIMIT 10",
    $test_agent->id
));

if ($tracked_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة WC</th><th>حالة التتبع</th><th>الملاحظات</th><th>تاريخ التتبع</th></tr>";
    
    foreach ($tracked_orders as $tracked_order) {
        echo "<tr>";
        echo "<td>#{$tracked_order->order_id}</td>";
        echo "<td>{$tracked_order->post_status}</td>";
        echo "<td style='color: green;'>{$tracked_order->status}</td>";
        echo "<td>" . substr($tracked_order->notes, 0, 50) . "...</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($tracked_order->created_at)) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات في نظام التتبع.</p>";
}

echo "<h2>🔧 إجراءات:</h2>";
echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل</a></p>";
echo "<p><a href='admin.php?page=hozi-akadly-my-tracking' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📊 صفحة التتبع</a></p>";

echo "<h2>📈 إحصائيات النظام:</h2>";

// System stats
$total_confirmed = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments 
     WHERE agent_id = %d AND confirmation_status = 'confirmed'",
    $test_agent->id
));

$total_tracked = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(DISTINCT ot.order_id) 
     FROM {$wpdb->prefix}hozi_order_tracking ot
     INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
     WHERE oa.agent_id = %d AND oa.confirmation_status = 'confirmed'",
    $test_agent->id
));

$auto_tracked = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(DISTINCT ot.order_id) 
     FROM {$wpdb->prefix}hozi_order_tracking ot
     INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
     WHERE oa.agent_id = %d AND oa.confirmation_status = 'confirmed'
     AND ot.reason_category = 'auto_completed'",
    $test_agent->id
));

echo "<div style='display: flex; gap: 20px;'>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; flex: 1;'>";
echo "<h4>📋 إجمالي المؤكدة</h4>";
echo "<p style='font-size: 24px; margin: 0;'>{$total_confirmed}</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; flex: 1;'>";
echo "<h4>🎯 في التتبع</h4>";
echo "<p style='font-size: 24px; margin: 0;'>{$total_tracked}</p>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px; flex: 1;'>";
echo "<h4>🤖 تلقائي</h4>";
echo "<p style='font-size: 24px; margin: 0;'>{$auto_tracked}</p>";
echo "</div>";
echo "</div>";

$success_rate = $total_confirmed > 0 ? round(($total_tracked / $total_confirmed) * 100, 1) : 0;
echo "<p><strong>معدل النجاح:</strong> {$success_rate}%</p>";
?>
