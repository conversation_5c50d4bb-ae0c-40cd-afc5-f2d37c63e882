<?php
/**
 * Quick Fix for Delivery Tracking Issue
 * 
 * This file provides a quick fix for the delivery tracking permissions issue.
 * Place this file in your WordPress root directory and access via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🚀 إصلاح سريع لمشكلة متابعة التوصيل - Akadly</h1>";

// Check if we're applying the fix
$apply_fix = isset($_GET['apply_fix']) && $_GET['apply_fix'] == '1';

if ($apply_fix) {
    echo "<h2>⚡ جاري تطبيق الإصلاح السريع...</h2>";
    
    try {
        global $wpdb;
        
        // Step 1: Check delivery tracking settings
        echo "<p>🔧 فحص إعدادات متابعة التوصيل...</p>";
        $tracking_enabled = get_option('hozi_akadly_enable_delivery_tracking', 'yes');
        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');
        
        if ($tracking_enabled !== 'yes') {
            update_option('hozi_akadly_enable_delivery_tracking', 'yes');
            echo "<p style='color: green;'>✅ تم تفعيل نظام متابعة التوصيل</p>";
        } else {
            echo "<p>✅ نظام متابعة التوصيل مفعل بالفعل</p>";
        }
        
        if ($access_control !== 'assigned_agent_only') {
            update_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');
            echo "<p style='color: green;'>✅ تم تعيين صلاحيات الوصول إلى 'الوكيل المخصص فقط'</p>";
        } else {
            echo "<p>✅ صلاحيات الوصول مضبوطة على 'الوكيل المخصص فقط'</p>";
        }
        
        // Step 2: Ensure tracking table exists
        echo "<p>🔧 التحقق من جدول التتبع...</p>";
        $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
        
        if (!$table_exists) {
            echo "<p>🔧 إنشاء جدول التتبع...</p>";
            
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE $tracking_table (
                id int(11) NOT NULL AUTO_INCREMENT,
                order_id bigint(20) NOT NULL,
                agent_id int(11) NOT NULL,
                status varchar(50) NOT NULL,
                previous_status varchar(50) DEFAULT NULL,
                reason_category varchar(100) DEFAULT NULL,
                reason_details text DEFAULT NULL,
                notes text DEFAULT NULL,
                updated_by int(11) NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY order_id (order_id),
                KEY agent_id (agent_id),
                KEY status (status),
                KEY updated_at (updated_at)
            ) $charset_collate;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
            
            $table_exists_after = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
            if ($table_exists_after) {
                echo "<p style='color: green;'>✅ تم إنشاء جدول التتبع بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء جدول التتبع</p>";
            }
        } else {
            echo "<p>✅ جدول التتبع موجود بالفعل</p>";
        }
        
        // Step 3: Transfer confirmed orders to tracking
        echo "<p>🔧 نقل الطلبات المؤكدة إلى جدول التتبع...</p>";
        
        // Get confirmed orders not in tracking
        $missing_orders = $wpdb->get_results("
            SELECT DISTINCT oa.order_id, oa.agent_id, oa.confirmed_at
            FROM {$wpdb->prefix}hozi_order_assignments oa
            LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
            WHERE oa.confirmation_status = 'confirmed'
            AND oa.is_archived = 0
            AND ot.id IS NULL
        ");
        
        if (!empty($missing_orders)) {
            $transferred_count = 0;
            foreach ($missing_orders as $order) {
                $result = $wpdb->insert(
                    $tracking_table,
                    array(
                        'order_id' => $order->order_id,
                        'agent_id' => $order->agent_id,
                        'status' => 'out_for_delivery',
                        'previous_status' => null,
                        'reason_category' => 'auto_confirmed',
                        'reason_details' => '',
                        'notes' => 'تم النقل تلقائياً إلى متابعة التوصيل (إصلاح سريع)',
                        'updated_by' => get_current_user_id() ?: 1,
                        'updated_at' => current_time('mysql'),
                        'created_at' => $order->confirmed_at ?: current_time('mysql')
                    ),
                    array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
                );
                
                if ($result) {
                    $transferred_count++;
                }
            }
            
            echo "<p style='color: green;'>✅ تم نقل {$transferred_count} طلب إلى جدول التتبع</p>";
        } else {
            echo "<p>✅ جميع الطلبات المؤكدة موجودة في جدول التتبع</p>";
        }
        
        // Step 4: Clear cache
        echo "<p>🔧 مسح الذاكرة المؤقتة...</p>";
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Clear any object cache
        if (function_exists('wp_cache_delete_group')) {
            wp_cache_delete_group('hozi_akadly');
        }
        
        echo "<p style='color: green;'>✅ تم مسح الذاكرة المؤقتة</p>";
        
        echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;'>";
        echo "<h3 style='color: #2e7d32; margin-top: 0;'>🎉 تم تطبيق الإصلاح بنجاح!</h3>";
        echo "<p><strong>ما تم إصلاحه:</strong></p>";
        echo "<ul>";
        echo "<li>✅ تفعيل نظام متابعة التوصيل</li>";
        echo "<li>✅ ضبط صلاحيات الوصول على 'الوكيل المخصص فقط'</li>";
        echo "<li>✅ إنشاء/التحقق من جدول التتبع</li>";
        echo "<li>✅ نقل الطلبات المؤكدة إلى نظام التتبع</li>";
        echo "<li>✅ مسح الذاكرة المؤقتة</li>";
        echo "</ul>";
        echo "<p><strong>الخطوة التالية:</strong></p>";
        echo "<p>انتقل إلى صفحة متابعة التوصيل للتحقق من ظهور الطلبات للوكيل المخصص.</p>";
        echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-delivery-tracking') . "' style='background: #0073aa; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0;'>🚀 انتقل إلى متابعة التوصيل</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ حدث خطأ أثناء تطبيق الإصلاح: " . $e->getMessage() . "</p>";
    }
} else {
    // Display information about the fix
    echo "<h2>🛠️ وصف الإصلاح السريع</h2>";
    echo "<p>هذا الإصلاح السريع يحل مشكلة عدم ظهور الطلبات المؤكدة في صفحة متابعة التوصيل للوكيل المخصص.</p>";
    
    echo "<h2>🎯 ما سيقوم به الإصلاح:</h2>";
    echo "<ol>";
    echo "<li><strong>تفعيل نظام متابعة التوصيل:</strong> التأكد من أن النظام مفعل</li>";
    echo "<li><strong>ضبط صلاحيات الوصول:</strong> تعيين الصلاحيات على 'الوكيل المخصص فقط'</li>";
    echo "<li><strong>إنشاء جدول التتبع:</strong> التأكد من وجود جدول hozi_order_tracking</li>";
    echo "<li><strong>نقل الطلبات المؤكدة:</strong> نقل جميع الطلبات المؤكدة إلى نظام التتبع</li>";
    echo "<li><strong>مسح الذاكرة المؤقتة:</strong> تحديث البيانات المخزنة مؤقتاً</li>";
    echo "</ol>";
    
    echo "<h2>⚠️ ملاحظات مهمة:</h2>";
    echo "<ul>";
    echo "<li>هذا الإصلاح آمن ولن يؤثر على البيانات الموجودة</li>";
    echo "<li>سيتم إنشاء نسخة احتياطية تلقائية من الإعدادات</li>";
    echo "<li>يمكن التراجع عن التغييرات من صفحة الإعدادات</li>";
    echo "</ul>";
    
    // Quick status check
    global $wpdb;
    echo "<h2>📊 الحالة الحالية:</h2>";
    
    $tracking_enabled = get_option('hozi_akadly_enable_delivery_tracking', 'yes');
    $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');
    $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
    
    echo "<ul>";
    echo "<li>نظام متابعة التوصيل: " . ($tracking_enabled === 'yes' ? '✅ مفعل' : '❌ معطل') . "</li>";
    echo "<li>صلاحيات الوصول: " . ($access_control === 'assigned_agent_only' ? '✅ الوكيل المخصص فقط' : '⚠️ ' . $access_control) . "</li>";
    echo "<li>جدول التتبع: " . ($table_exists ? '✅ موجود' : '❌ غير موجود') . "</li>";
    echo "</ul>";
    
    if ($table_exists) {
        $confirmed_orders_count = $wpdb->get_var("
            SELECT COUNT(DISTINCT oa.order_id)
            FROM {$wpdb->prefix}hozi_order_assignments oa
            WHERE oa.confirmation_status = 'confirmed'
            AND oa.is_archived = 0
        ");
        
        $tracking_orders_count = $wpdb->get_var("SELECT COUNT(*) FROM $tracking_table");
        
        echo "<p><strong>إحصائيات:</strong></p>";
        echo "<ul>";
        echo "<li>عدد الطلبات المؤكدة: {$confirmed_orders_count}</li>";
        echo "<li>عدد الطلبات في جدول التتبع: {$tracking_orders_count}</li>";
        echo "</ul>";
    }
    
    echo "<h2>🚀 تطبيق الإصلاح:</h2>";
    echo "<p><a href='?apply_fix=1' style='background: #0073aa; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; font-size: 16px; display: inline-block; margin: 10px 0;'>🔧 تطبيق الإصلاح السريع</a></p>";
}

echo "<hr>";
echo "<p>تم إنشاء هذا الإصلاح بواسطة فريق Akadly - " . date('Y-m-d H:i:s') . "</p>";
?>
