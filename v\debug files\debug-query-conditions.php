<?php
/**
 * Debug query conditions step by step
 */

require_once('wp-config.php');

global $wpdb;

echo "<h1>🔍 تشخيص شروط الاستعلام خطوة بخطوة</h1>";

// Get current agent
$user_id = get_current_user_id();
$agent_data = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d AND is_active = 1",
    $user_id
));

if (!$agent_data) {
    echo "<p>❌ لم يتم العثور على بيانات الوكيل للمستخدم الحالي</p>";
    exit;
}

echo "<p>✅ <strong>الوكيل:</strong> {$agent_data->name} (ID: {$agent_data->id})</p>";

echo "<h2>📋 اختبار الشروط خطوة بخطوة:</h2>";

// Step 1: Basic confirmed orders
$step1 = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmed_at, oa.archived
     FROM {$wpdb->prefix}hozi_order_assignments oa
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $agent_data->id
));

echo "<h3>1️⃣ الطلبات المؤكدة الأساسية:</h3>";
echo "<p><strong>العدد:</strong> " . count($step1) . "</p>";

if ($step1) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>مؤرشف</th></tr>";
    foreach ($step1 as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>" . ($order->archived ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Step 2: Add archive condition
$step2 = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmed_at, oa.archived
     FROM {$wpdb->prefix}hozi_order_assignments oa
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND (oa.archived IS NULL OR oa.archived = 0)
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $agent_data->id
));

echo "<h3>2️⃣ بعد إضافة شرط الأرشفة:</h3>";
echo "<p><strong>العدد:</strong> " . count($step2) . "</p>";

// Step 3: Add posts join
$step3 = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmed_at, oa.archived, p.post_status
     FROM {$wpdb->prefix}hozi_order_assignments oa
     INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND (oa.archived IS NULL OR oa.archived = 0)
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $agent_data->id
));

echo "<h3>3️⃣ بعد إضافة JOIN مع posts:</h3>";
echo "<p><strong>العدد:</strong> " . count($step3) . "</p>";

if ($step3) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>مؤرشف</th><th>حالة WC</th></tr>";
    foreach ($step3 as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>" . ($order->archived ? 'نعم' : 'لا') . "</td>";
        echo "<td>{$order->post_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Step 4: Add post_status condition
$step4 = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmed_at, oa.archived, p.post_status
     FROM {$wpdb->prefix}hozi_order_assignments oa
     INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND (oa.archived IS NULL OR oa.archived = 0)
     AND p.post_status NOT IN ('trash', 'auto-draft', 'inherit')
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $agent_data->id
));

echo "<h3>4️⃣ بعد إضافة شرط post_status:</h3>";
echo "<p><strong>العدد:</strong> " . count($step4) . "</p>";

// Step 5: Full query with tracking LEFT JOIN
$step5 = $wpdb->get_results($wpdb->prepare(
    "SELECT DISTINCT
        oa.order_id,
        oa.confirmed_at,
        oa.notes as confirmation_notes,
        p.post_date as order_date,
        p.post_status,
        ot.status as delivery_status,
        ot.notes as delivery_notes,
        ot.updated_at as delivery_date,
        ot.created_at as tracking_created
    FROM {$wpdb->prefix}hozi_order_assignments oa
    INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
    LEFT JOIN (
        SELECT order_id, status, notes, updated_at, created_at,
               ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY updated_at DESC) as rn
        FROM {$wpdb->prefix}hozi_order_tracking
    ) ot ON (oa.order_id = ot.order_id AND ot.rn = 1)
    WHERE oa.agent_id = %d
    AND oa.confirmation_status = 'confirmed'
    AND (oa.archived IS NULL OR oa.archived = 0)
    AND p.post_status NOT IN ('trash', 'auto-draft', 'inherit')
    ORDER BY oa.confirmed_at DESC
    LIMIT 10",
    $agent_data->id
));

echo "<h3>5️⃣ الاستعلام الكامل مع LEFT JOIN للتتبع:</h3>";
echo "<p><strong>العدد:</strong> " . count($step5) . "</p>";

if ($step5) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>حالة WC</th><th>حالة التتبع</th></tr>";
    foreach ($step5 as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>{$order->post_status}</td>";
        echo "<td>" . ($order->delivery_status ?: '<span style="color: orange;">بانتظار التتبع</span>') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>🔍 تحليل المشكلة:</h2>";
echo "<ul>";
echo "<li><strong>الخطوة 1:</strong> " . count($step1) . " طلبات مؤكدة</li>";
echo "<li><strong>الخطوة 2:</strong> " . count($step2) . " طلبات غير مؤرشفة</li>";
echo "<li><strong>الخطوة 3:</strong> " . count($step3) . " طلبات مع JOIN posts</li>";
echo "<li><strong>الخطوة 4:</strong> " . count($step4) . " طلبات مع شرط post_status</li>";
echo "<li><strong>الخطوة 5:</strong> " . count($step5) . " طلبات مع LEFT JOIN للتتبع</li>";
echo "</ul>";

if (count($step1) > count($step5)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;'>";
    echo "<h3>⚠️ تم العثور على المشكلة!</h3>";
    echo "<p>الطلبات موجودة لكن الاستعلام المعقد يفقدها في إحدى الخطوات.</p>";
    echo "</div>";
}
?>
