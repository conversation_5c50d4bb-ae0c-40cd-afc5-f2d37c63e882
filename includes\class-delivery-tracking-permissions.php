<?php
/**
 * Delivery Tracking Permissions Manager
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Delivery_Tracking_Permissions {

    /**
     * Check if delivery tracking is enabled
     */
    public static function is_delivery_tracking_enabled() {
        return get_option('hozi_akadly_enable_delivery_tracking', 'yes') === 'yes';
    }

    /**
     * Check if current user can access delivery tracking
     */
    public static function can_access_delivery_tracking($user_id = null) {
        if (!self::is_delivery_tracking_enabled()) {
            return false;
        }

        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Admins can always access if enabled
        if (current_user_can('manage_options')) {
            return true;
        }

        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'assigned_agent_only':
                return self::can_agent_access_assigned_orders($user_id);

            case 'any_agent':
                return self::is_user_agent($user_id);

            case 'managers_only':
                return current_user_can('manage_options');

            case 'custom_assignment':
                return self::is_user_assigned_for_delivery_tracking($user_id);

            default:
                return false;
        }
    }

    /**
     * Check if user is an agent
     */
    private static function is_user_agent($user_id) {
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $agent = $agent_manager->get_agent_by_user_id($user_id);
        return $agent && $agent->is_active;
    }

    /**
     * Check if agent can access their assigned orders only
     */
    private static function can_agent_access_assigned_orders($user_id) {
        return self::is_user_agent($user_id);
    }

    /**
     * Check if user is assigned for delivery tracking in custom assignment mode
     */
    private static function is_user_assigned_for_delivery_tracking($user_id) {
        $assigned_user = get_option('hozi_akadly_delivery_tracking_assigned_user', '');
        
        if (empty($assigned_user)) {
            return false;
        }

        // Check if it's a manager assignment
        if (strpos($assigned_user, 'manager_') === 0) {
            $manager_id = str_replace('manager_', '', $assigned_user);
            return $user_id == $manager_id && current_user_can('manage_options');
        }

        // Check if it's an agent assignment
        if (strpos($assigned_user, 'agent_') === 0) {
            $agent_user_id = str_replace('agent_', '', $assigned_user);
            return $user_id == $agent_user_id && self::is_user_agent($user_id);
        }

        return false;
    }

    /**
     * Get orders that user can track based on access control
     * 🎯 FIXED: Now properly checks tracking table and agent assignments
     */
    public static function get_trackable_orders_for_user($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!self::can_access_delivery_tracking($user_id)) {
            return array();
        }

        global $wpdb;
        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'assigned_agent_only':
                // Only orders assigned to this specific agent
                $agent_manager = new Hozi_Akadly_Agent_Manager();
                $agent = $agent_manager->get_agent_by_user_id($user_id);
                if (!$agent) {
                    return array();
                }

                // 🚀 CRITICAL FIX: Ensure all confirmed orders are in tracking table
                self::ensure_confirmed_orders_in_tracking($agent->id);

                // 🔧 FIXED: Get orders from tracking table that belong to this agent
                // First, get confirmed orders for this agent
                $confirmed_orders = $wpdb->get_results($wpdb->prepare(
                    "SELECT DISTINCT oa.order_id
                     FROM {$wpdb->prefix}hozi_order_assignments oa
                     WHERE oa.agent_id = %d
                     AND oa.confirmation_status = 'confirmed'
                     AND oa.is_archived = 0",
                    $agent->id
                ));

                // If no confirmed orders, return empty
                if (empty($confirmed_orders)) {
                    return array();
                }

                // Extract order IDs
                $order_ids = array_column($confirmed_orders, 'order_id');
                $order_ids_str = implode(',', array_map('intval', $order_ids));

                // 🎯 CRITICAL FIX: Check if tracking table exists and get orders from it
                $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;

                if ($table_exists && $order_ids_str) {
                    // Get orders that are in tracking table for this agent
                    $tracking_orders = $wpdb->get_results("
                        SELECT DISTINCT ot.order_id
                        FROM {$wpdb->prefix}hozi_order_tracking ot
                        WHERE ot.agent_id = {$agent->id}
                        AND ot.order_id IN ({$order_ids_str})
                    ");

                    // If orders exist in tracking, return them
                    if (!empty($tracking_orders)) {
                        return $tracking_orders;
                    }
                }

                // 🚀 FALLBACK: If no tracking data, return confirmed orders
                // This ensures orders show up even if auto_transfer_to_tracking failed
                return $confirmed_orders;

            case 'any_agent':
            case 'managers_only':
            case 'custom_assignment':
                // All confirmed orders
                $confirmed_orders = $wpdb->get_results(
                    "SELECT DISTINCT oa.order_id
                     FROM {$wpdb->prefix}hozi_order_assignments oa
                     WHERE oa.confirmation_status = 'confirmed'
                     AND oa.is_archived = 0"
                );

                // For non-agent-specific access, show all confirmed orders
                return $confirmed_orders;

            default:
                return array();
        }
    }

    /**
     * Ensure confirmed orders are transferred to tracking system
     * 🎯 CRITICAL FIX: Auto-transfer missing orders to tracking
     */
    public static function ensure_confirmed_orders_in_tracking($agent_id = null) {
        global $wpdb;

        // Get all confirmed orders that are not in tracking table
        $missing_orders_query = "
            SELECT DISTINCT oa.order_id, oa.agent_id, oa.confirmed_at
            FROM {$wpdb->prefix}hozi_order_assignments oa
            LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
            WHERE oa.confirmation_status = 'confirmed'
            AND oa.is_archived = 0
            AND ot.id IS NULL
        ";

        // If specific agent, filter by agent
        if ($agent_id) {
            $missing_orders_query .= $wpdb->prepare(" AND oa.agent_id = %d", $agent_id);
        }

        $missing_orders = $wpdb->get_results($missing_orders_query);

        if (!empty($missing_orders)) {
            error_log("Hozi Akadly: Found " . count($missing_orders) . " confirmed orders missing from tracking table");

            // Ensure tracking table exists
            self::ensure_tracking_table_exists();

            $transferred_count = 0;
            foreach ($missing_orders as $order) {
                $result = $wpdb->insert(
                    $wpdb->prefix . 'hozi_order_tracking',
                    array(
                        'order_id' => $order->order_id,
                        'agent_id' => $order->agent_id,
                        'status' => 'out_for_delivery',
                        'previous_status' => null,
                        'reason_category' => 'auto_confirmed',
                        'reason_details' => '',
                        'notes' => 'تم النقل تلقائياً إلى متابعة التوصيل (إصلاح تلقائي)',
                        'updated_by' => get_current_user_id() ?: 1,
                        'updated_at' => current_time('mysql'),
                        'created_at' => $order->confirmed_at ?: current_time('mysql')
                    ),
                    array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
                );

                if ($result) {
                    $transferred_count++;
                    error_log("Hozi Akadly: ✅ Auto-transferred order {$order->order_id} to tracking");
                } else {
                    error_log("Hozi Akadly: ❌ Failed to auto-transfer order {$order->order_id}: " . $wpdb->last_error);
                }
            }

            error_log("Hozi Akadly: Auto-transferred {$transferred_count} orders to tracking table");
            return $transferred_count;
        }

        return 0;
    }

    /**
     * Ensure tracking table exists
     */
    private static function ensure_tracking_table_exists() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'hozi_order_tracking';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            error_log("Hozi Akadly: Tracking table missing, creating it now...");

            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                order_id bigint(20) NOT NULL,
                agent_id int(11) NOT NULL,
                status varchar(50) NOT NULL,
                previous_status varchar(50) DEFAULT NULL,
                reason_category varchar(100) DEFAULT NULL,
                reason_details text DEFAULT NULL,
                notes text DEFAULT NULL,
                updated_by int(11) NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY order_id (order_id),
                KEY agent_id (agent_id),
                KEY status (status),
                KEY updated_at (updated_at)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);

            $table_exists_after = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
            if ($table_exists_after) {
                error_log("Hozi Akadly: Tracking table created successfully");
            } else {
                error_log("Hozi Akadly: Failed to create tracking table");
            }
        }
    }

    /**
     * Get access control description for display
     */
    public static function get_access_control_description() {
        if (!self::is_delivery_tracking_enabled()) {
            return __('نظام متابعة التوصيل معطل', 'hozi-akadly');
        }

        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'assigned_agent_only':
                return __('يمكن للوكيل متابعة الطلبات التي أكدها فقط', 'hozi-akadly');

            case 'any_agent':
                return __('يمكن لأي وكيل متابعة جميع الطلبات المؤكدة', 'hozi-akadly');

            case 'managers_only':
                return __('يمكن للمشرفين فقط متابعة التوصيل', 'hozi-akadly');

            case 'custom_assignment':
                $assigned_user = get_option('hozi_akadly_delivery_tracking_assigned_user', '');
                if (empty($assigned_user)) {
                    return __('لم يتم تحديد مسؤول متابعة التوصيل', 'hozi-akadly');
                }

                if (strpos($assigned_user, 'manager_') === 0) {
                    $manager_id = str_replace('manager_', '', $assigned_user);
                    $manager = get_user_by('id', $manager_id);
                    return sprintf(__('مسؤول متابعة التوصيل: %s (مشرف)', 'hozi-akadly'), $manager ? $manager->display_name : __('غير معروف', 'hozi-akadly'));
                }

                if (strpos($assigned_user, 'agent_') === 0) {
                    $agent_user_id = str_replace('agent_', '', $assigned_user);
                    $agent_manager = new Hozi_Akadly_Agent_Manager();
                    $agent = $agent_manager->get_agent_by_user_id($agent_user_id);
                    return sprintf(__('مسؤول متابعة التوصيل: %s (وكيل)', 'hozi-akadly'), $agent ? $agent->name : __('غير معروف', 'hozi-akadly'));
                }

                return __('إعداد مخصص غير صحيح', 'hozi-akadly');

            default:
                return __('إعداد غير معروف', 'hozi-akadly');
        }
    }

    /**
     * Check if user should see delivery tracking menu
     */
    public static function should_show_delivery_tracking_menu($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Always show for admins if enabled
        if (current_user_can('manage_options')) {
            return self::is_delivery_tracking_enabled();
        }

        return self::can_access_delivery_tracking($user_id);
    }

    /**
     * Get error message for access denied
     */
    public static function get_access_denied_message() {
        if (!self::is_delivery_tracking_enabled()) {
            return __('نظام متابعة التوصيل معطل من قبل المشرف.', 'hozi-akadly');
        }

        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'managers_only':
                return __('متابعة التوصيل متاحة للمشرفين فقط.', 'hozi-akadly');

            case 'custom_assignment':
                return __('متابعة التوصيل مخصصة لمستخدم محدد فقط.', 'hozi-akadly');

            default:
                return __('ليس لديك صلاحية للوصول إلى متابعة التوصيل.', 'hozi-akadly');
        }
    }
}
