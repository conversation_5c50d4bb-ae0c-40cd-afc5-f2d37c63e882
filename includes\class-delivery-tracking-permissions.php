<?php
/**
 * Delivery Tracking Permissions Manager
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Delivery_Tracking_Permissions {

    /**
     * Check if delivery tracking is enabled
     */
    public static function is_delivery_tracking_enabled() {
        return get_option('hozi_akadly_enable_delivery_tracking', 'yes') === 'yes';
    }

    /**
     * Check if current user can access delivery tracking
     */
    public static function can_access_delivery_tracking($user_id = null) {
        if (!self::is_delivery_tracking_enabled()) {
            return false;
        }

        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Admins can always access if enabled
        if (current_user_can('manage_options')) {
            return true;
        }

        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'assigned_agent_only':
                return self::can_agent_access_assigned_orders($user_id);

            case 'any_agent':
                return self::is_user_agent($user_id);

            case 'managers_only':
                return current_user_can('manage_options');

            case 'custom_assignment':
                return self::is_user_assigned_for_delivery_tracking($user_id);

            default:
                return false;
        }
    }

    /**
     * Check if user is an agent
     */
    private static function is_user_agent($user_id) {
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $agent = $agent_manager->get_agent_by_user_id($user_id);
        return $agent && $agent->is_active;
    }

    /**
     * Check if agent can access their assigned orders only
     */
    private static function can_agent_access_assigned_orders($user_id) {
        return self::is_user_agent($user_id);
    }

    /**
     * Check if user is assigned for delivery tracking in custom assignment mode
     */
    private static function is_user_assigned_for_delivery_tracking($user_id) {
        $assigned_user = get_option('hozi_akadly_delivery_tracking_assigned_user', '');
        
        if (empty($assigned_user)) {
            return false;
        }

        // Check if it's a manager assignment
        if (strpos($assigned_user, 'manager_') === 0) {
            $manager_id = str_replace('manager_', '', $assigned_user);
            return $user_id == $manager_id && current_user_can('manage_options');
        }

        // Check if it's an agent assignment
        if (strpos($assigned_user, 'agent_') === 0) {
            $agent_user_id = str_replace('agent_', '', $assigned_user);
            return $user_id == $agent_user_id && self::is_user_agent($user_id);
        }

        return false;
    }

    /**
     * Get orders that user can track based on access control
     */
    public static function get_trackable_orders_for_user($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!self::can_access_delivery_tracking($user_id)) {
            return array();
        }

        global $wpdb;
        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'assigned_agent_only':
                // Only orders assigned to this specific agent
                $agent_manager = new Hozi_Akadly_Agent_Manager();
                $agent = $agent_manager->get_agent_by_user_id($user_id);
                if (!$agent) {
                    return array();
                }

                return $wpdb->get_results($wpdb->prepare(
                    "SELECT DISTINCT oa.order_id 
                     FROM {$wpdb->prefix}hozi_order_assignments oa
                     WHERE oa.agent_id = %d 
                     AND oa.confirmation_status = 'confirmed'
                     AND oa.is_archived = 0",
                    $agent->id
                ));

            case 'any_agent':
            case 'managers_only':
            case 'custom_assignment':
                // All confirmed orders
                return $wpdb->get_results(
                    "SELECT DISTINCT oa.order_id 
                     FROM {$wpdb->prefix}hozi_order_assignments oa
                     WHERE oa.confirmation_status = 'confirmed'
                     AND oa.is_archived = 0"
                );

            default:
                return array();
        }
    }

    /**
     * Get access control description for display
     */
    public static function get_access_control_description() {
        if (!self::is_delivery_tracking_enabled()) {
            return __('نظام متابعة التوصيل معطل', 'hozi-akadly');
        }

        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'assigned_agent_only':
                return __('يمكن للوكيل متابعة الطلبات التي أكدها فقط', 'hozi-akadly');

            case 'any_agent':
                return __('يمكن لأي وكيل متابعة جميع الطلبات المؤكدة', 'hozi-akadly');

            case 'managers_only':
                return __('يمكن للمشرفين فقط متابعة التوصيل', 'hozi-akadly');

            case 'custom_assignment':
                $assigned_user = get_option('hozi_akadly_delivery_tracking_assigned_user', '');
                if (empty($assigned_user)) {
                    return __('لم يتم تحديد مسؤول متابعة التوصيل', 'hozi-akadly');
                }

                if (strpos($assigned_user, 'manager_') === 0) {
                    $manager_id = str_replace('manager_', '', $assigned_user);
                    $manager = get_user_by('id', $manager_id);
                    return sprintf(__('مسؤول متابعة التوصيل: %s (مشرف)', 'hozi-akadly'), $manager ? $manager->display_name : __('غير معروف', 'hozi-akadly'));
                }

                if (strpos($assigned_user, 'agent_') === 0) {
                    $agent_user_id = str_replace('agent_', '', $assigned_user);
                    $agent_manager = new Hozi_Akadly_Agent_Manager();
                    $agent = $agent_manager->get_agent_by_user_id($agent_user_id);
                    return sprintf(__('مسؤول متابعة التوصيل: %s (وكيل)', 'hozi-akadly'), $agent ? $agent->name : __('غير معروف', 'hozi-akadly'));
                }

                return __('إعداد مخصص غير صحيح', 'hozi-akadly');

            default:
                return __('إعداد غير معروف', 'hozi-akadly');
        }
    }

    /**
     * Check if user should see delivery tracking menu
     */
    public static function should_show_delivery_tracking_menu($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Always show for admins if enabled
        if (current_user_can('manage_options')) {
            return self::is_delivery_tracking_enabled();
        }

        return self::can_access_delivery_tracking($user_id);
    }

    /**
     * Get error message for access denied
     */
    public static function get_access_denied_message() {
        if (!self::is_delivery_tracking_enabled()) {
            return __('نظام متابعة التوصيل معطل من قبل المشرف.', 'hozi-akadly');
        }

        $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');

        switch ($access_control) {
            case 'managers_only':
                return __('متابعة التوصيل متاحة للمشرفين فقط.', 'hozi-akadly');

            case 'custom_assignment':
                return __('متابعة التوصيل مخصصة لمستخدم محدد فقط.', 'hozi-akadly');

            default:
                return __('ليس لديك صلاحية للوصول إلى متابعة التوصيل.', 'hozi-akadly');
        }
    }
}
