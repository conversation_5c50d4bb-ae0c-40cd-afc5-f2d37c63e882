<?php
/**
 * Simple Test for Auto Tracking
 * Tests the automatic transition of orders to tracking when status changes to 'completed'
 */

// WordPress environment
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo "<h1>🎯 اختبار بسيط للانتقال التلقائي للتتبع</h1>";

global $wpdb;

// Get test agent
$test_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 LIMIT 1");

if (!$test_agent) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ لا يوجد وكلاء نشطين!</h3>";
    echo "</div>";
    exit;
}

echo "<h2>🧪 الوكيل المستخدم: {$test_agent->name} (ID: {$test_agent->id})</h2>";

// Handle action
if (isset($_GET['action']) && $_GET['action'] === 'create_test') {
    echo "<h2>🔧 إنشاء طلب اختبار:</h2>";
    
    // Create a test order
    $order = wc_create_order();
    $order->set_billing_first_name('عميل');
    $order->set_billing_last_name('اختبار');
    $order->set_billing_phone('01234567890');
    $order->set_billing_email('<EMAIL>');
    $order->set_billing_address_1('عنوان اختبار');
    $order->set_billing_city('القاهرة');
    $order->set_billing_country('EG');
    
    // Add a simple product
    $product = wc_get_product(wc_get_products(array('limit' => 1))[0]->get_id());
    if ($product) {
        $order->add_product($product, 1);
    }
    
    $order->calculate_totals();
    $order->save();
    
    $order_id = $order->get_id();
    
    echo "<p>✅ تم إنشاء طلب اختبار: #{$order_id}</p>";
    
    // Create confirmed assignment
    $result = $wpdb->insert(
        $wpdb->prefix . 'hozi_order_assignments',
        array(
            'order_id' => $order_id,
            'agent_id' => $test_agent->id,
            'confirmation_status' => 'confirmed',
            'assigned_at' => current_time('mysql'),
            'confirmed_at' => current_time('mysql'),
            'assignment_method' => 'manual',
            'archived' => 0,
            'notes' => 'طلب اختبار للنظام التلقائي'
        ),
        array('%d', '%d', '%s', '%s', '%s', '%s', '%d', '%s')
    );
    
    if ($result) {
        echo "<p>✅ تم إنشاء تخصيص مؤكد</p>";
        
        // Now test completing the order
        echo "<h3>🎯 اختبار إكمال الطلب:</h3>";
        
        echo "<p>📝 الحالة الحالية: {$order->get_status()}</p>";
        
        // Update to completed
        $order->update_status('completed', 'اختبار النظام التلقائي - أكدلي');
        
        echo "<p>🎯 تم تغيير حالة الطلب إلى 'completed'</p>";
        
        // Check if tracking was added
        sleep(2); // Wait a moment
        
        $tracking = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d ORDER BY created_at DESC LIMIT 1",
            $order_id
        ));
        
        if ($tracking) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h4>🎉 نجح النظام التلقائي!</h4>";
            echo "<p><strong>تم إضافة التتبع تلقائياً:</strong></p>";
            echo "<p>• الحالة: {$tracking->status}</p>";
            echo "<p>• الوكيل: {$tracking->agent_id}</p>";
            echo "<p>• الملاحظات: {$tracking->notes}</p>";
            echo "<p>• التاريخ: {$tracking->created_at}</p>";
            echo "</div>";
            
            echo "<p><a href='admin.php?page=hozi-akadly-my-tracking' target='_blank' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 عرض في صفحة التتبع</a></p>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h4>❌ فشل النظام التلقائي!</h4>";
            echo "<p>لم يتم إضافة التتبع تلقائياً.</p>";
            echo "</div>";
        }
        
        echo "<p><a href='post.php?post={$order_id}&action=edit' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📝 عرض الطلب</a></p>";
        
    } else {
        echo "<p>❌ فشل في إنشاء التخصيص</p>";
    }
    
    echo "<hr>";
}

// Show existing test orders
echo "<h2>📋 الطلبات الموجودة للاختبار:</h2>";

$test_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmation_status, oa.confirmed_at, p.post_status,
            ot.status as tracking_status, ot.created_at as tracking_date
     FROM {$wpdb->prefix}hozi_order_assignments oa
     LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $test_agent->id
));

if ($test_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة WC</th><th>حالة التتبع</th><th>تاريخ التتبع</th><th>إجراءات</th></tr>";
    
    foreach ($test_orders as $test_order) {
        echo "<tr>";
        echo "<td>#{$test_order->order_id}</td>";
        echo "<td>{$test_order->post_status}</td>";
        echo "<td>" . ($test_order->tracking_status ?: 'لا يوجد') . "</td>";
        echo "<td>" . ($test_order->tracking_date ?: 'لا يوجد') . "</td>";
        echo "<td>";
        
        if ($test_order->post_status === 'wc-completed' && !$test_order->tracking_status) {
            echo "<span style='color: red;'>⚠️ مكتمل بدون تتبع</span>";
        } elseif ($test_order->tracking_status) {
            echo "<span style='color: green;'>✅ في التتبع</span>";
        } else {
            echo "<a href='post.php?post={$test_order->order_id}&action=edit' target='_blank' style='background: #ff9800; color: white; padding: 3px 8px; text-decoration: none; border-radius: 3px;'>إكمال</a>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات مؤكدة للوكيل.</p>";
}

echo "<h2>🔧 إجراءات الاختبار:</h2>";
echo "<p><a href='?action=create_test' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🆕 إنشاء طلب اختبار جديد</a></p>";
echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل</a></p>";

echo "<h2>📊 حالة النظام:</h2>";

// Check system status
if (class_exists('Hozi_Akadly_Order_Tracker')) {
    echo "<p>✅ كلاس Order Tracker موجود</p>";
} else {
    echo "<p>❌ كلاس Order Tracker غير موجود</p>";
}

// Check if main class has order tracker
$main_instance = Hozi_Akadly::get_instance();
if ($main_instance && isset($main_instance->order_tracker)) {
    echo "<p>✅ Order Tracker مُهيأ في الكلاس الرئيسي</p>";
} else {
    echo "<p>❌ Order Tracker غير مُهيأ في الكلاس الرئيسي</p>";
}

// Check hooks
$hooks = $GLOBALS['wp_filter']['woocommerce_order_status_changed'] ?? null;
if ($hooks) {
    echo "<p>✅ Hook woocommerce_order_status_changed مسجل</p>";
    
    $found_handlers = 0;
    foreach ($hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function'])) {
                $class_name = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : 'Unknown';
                $method_name = $callback['function'][1] ?? 'Unknown';
                
                if ($class_name === 'Hozi_Akadly_Order_Tracker' || $class_name === 'Hozi_Akadly') {
                    echo "<p>✅ Handler: {$class_name}::{$method_name} (أولوية: {$priority})</p>";
                    $found_handlers++;
                }
            }
        }
    }
    
    if ($found_handlers === 0) {
        echo "<p>❌ لا توجد handlers من أكدلي مسجلة</p>";
    }
} else {
    echo "<p>❌ Hook woocommerce_order_status_changed غير مسجل</p>";
}
?>
