<?php
/**
 * Debug Delivery Tracking System - NEW
 * Check why orders are not showing in delivery tracking
 */

// WordPress environment
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

echo "<h1>🔍 تشخيص نظام متابعة التوصيل - الجديد</h1>";

global $wpdb;

// Get current user info
$current_user = wp_get_current_user();
echo "<h2>👤 المستخدم الحالي: {$current_user->display_name} (ID: {$current_user->ID})</h2>";

// Get agent manager and current agent
$plugin_dir = dirname(__FILE__);
$agent_manager_file = $plugin_dir . '/includes/class-agent-manager.php';

if (file_exists($agent_manager_file)) {
    require_once($agent_manager_file);
    $agent_manager = new Hozi_Akadly_Agent_Manager();
    $current_agent = $agent_manager->get_current_agent();
} else {
    echo "<p style='color: red;'>❌ ملف Agent Manager غير موجود: {$agent_manager_file}</p>";

    // Try alternative path
    $alt_path = WP_PLUGIN_DIR . '/Akadly/includes/class-agent-manager.php';
    if (file_exists($alt_path)) {
        require_once($alt_path);
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $current_agent = $agent_manager->get_current_agent();
        echo "<p style='color: green;'>✅ تم العثور على الملف في: {$alt_path}</p>";
    } else {
        echo "<p style='color: red;'>❌ لم يتم العثور على ملف Agent Manager في أي مكان</p>";
        echo "<p>المسارات المجربة:</p>";
        echo "<ul>";
        echo "<li>{$agent_manager_file}</li>";
        echo "<li>{$alt_path}</li>";
        echo "</ul>";

        // Manual agent detection
        $current_agent = null;
        $user_id = get_current_user_id();
        $agent_data = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d AND is_active = 1",
            $user_id
        ));

        if ($agent_data) {
            $current_agent = (object) $agent_data;
            echo "<p style='color: green;'>✅ تم العثور على الوكيل يدوياً: {$current_agent->name}</p>";
        }
    }
}

if ($current_agent) {
    echo "<h2>🧑‍💼 الوكيل الحالي: {$current_agent->name} (ID: {$current_agent->id})</h2>";
} else {
    echo "<h2 style='color: red;'>❌ لم يتم العثور على وكيل للمستخدم الحالي!</h2>";

    // Show all agents
    $all_agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents");
    echo "<h3>جميع الوكلاء:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>ID المستخدم</th><th>نشط</th></tr>";
    foreach ($all_agents as $agent) {
        echo "<tr>";
        echo "<td>{$agent->id}</td>";
        echo "<td>{$agent->name}</td>";
        echo "<td>{$agent->user_id}</td>";
        echo "<td>" . ($agent->is_active ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    exit;
}

// Check confirmed orders for this agent
echo "<h2>📋 الطلبات المؤكدة للوكيل:</h2>";

$confirmed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT
        oa.order_id,
        oa.confirmation_status,
        oa.confirmed_at,
        oa.archived,
        oa.notes as confirmation_notes,
        p.post_status,
        p.post_date
    FROM {$wpdb->prefix}hozi_order_assignments oa
    INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
    WHERE oa.agent_id = %d
    ORDER BY oa.confirmed_at DESC",
    $current_agent->id
));

if ($confirmed_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة التأكيد</th><th>تاريخ التأكيد</th><th>حالة WC</th><th>مؤرشف</th><th>يظهر في التتبع؟</th></tr>";

    foreach ($confirmed_orders as $order) {
        $should_show = ($order->confirmation_status === 'confirmed' && ($order->archived === null || $order->archived == 0));
        $show_text = $should_show ? '✅ نعم' : '❌ لا';
        $show_color = $should_show ? 'green' : 'red';

        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>{$order->confirmation_status}</td>";
        echo "<td>" . ($order->confirmed_at ? date('Y/m/d H:i', strtotime($order->confirmed_at)) : 'غير محدد') . "</td>";
        echo "<td>{$order->post_status}</td>";
        echo "<td>" . ($order->archived ? 'نعم' : 'لا') . "</td>";
        echo "<td style='color: {$show_color}; font-weight: bold;'>{$show_text}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Count orders that should show
    $should_show_count = 0;
    foreach ($confirmed_orders as $order) {
        if ($order->confirmation_status === 'confirmed' && ($order->archived === null || $order->archived == 0)) {
            $should_show_count++;
        }
    }

    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📊 الملخص:</h3>";
    echo "<p><strong>إجمالي الطلبات:</strong> " . count($confirmed_orders) . "</p>";
    echo "<p><strong>الطلبات التي يجب أن تظهر في التتبع:</strong> {$should_show_count}</p>";
    echo "</div>";

} else {
    echo "<p style='color: red;'>❌ لا توجد طلبات مؤكدة لهذا الوكيل!</p>";
}

// Test the exact query used in delivery tracking
echo "<h2>🔍 اختبار الاستعلام المستخدم في صفحة التتبع:</h2>";

$delivery_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT
        oa.order_id,
        oa.confirmed_at,
        oa.notes as confirmation_notes,
        p.post_date as order_date,
        p.post_status
    FROM {$wpdb->prefix}hozi_order_assignments oa
    INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
    WHERE oa.agent_id = %d
    AND oa.confirmation_status = 'confirmed'
    AND (oa.archived IS NULL OR oa.archived = 0)
    ORDER BY oa.confirmed_at DESC
    LIMIT 50",
    $current_agent->id
));

echo "<p><strong>نتائج الاستعلام:</strong> " . count($delivery_orders) . " طلب</p>";

if ($delivery_orders) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🎉 ممتاز! الطلبات موجودة!</h3>";
    echo "<p>يجب أن تظهر هذه الطلبات في صفحة متابعة التوصيل</p>";
    echo "</div>";

    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>حالة WC</th><th>تاريخ الطلب</th></tr>";

    foreach ($delivery_orders as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . ($order->confirmed_at ? date('Y/m/d H:i', strtotime($order->confirmed_at)) : 'غير محدد') . "</td>";
        echo "<td>{$order->post_status}</td>";
        echo "<td>" . ($order->order_date ? date('Y/m/d H:i', strtotime($order->order_date)) : 'غير محدد') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ الاستعلام لم يعيد أي نتائج!</p>";

    echo "<h3>🔍 تحليل المشكلة:</h3>";
    echo "<ul>";
    echo "<li>تحقق من أن الوكيل الحالي له طلبات مؤكدة</li>";
    echo "<li>تحقق من أن الطلبات غير مؤرشفة (archived = 0 أو NULL)</li>";
    echo "<li>تحقق من أن حالة التأكيد = 'confirmed'</li>";
    echo "</ul>";
}

// Find which agent has confirmed orders
echo "<h2>🔍 البحث عن الوكيل الذي أكد الطلبات:</h2>";

$agents_with_orders = $wpdb->get_results(
    "SELECT
        oa.agent_id,
        a.name as agent_name,
        a.user_id as agent_user_id,
        COUNT(*) as confirmed_count
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    WHERE oa.confirmation_status = 'confirmed'
    AND (oa.archived IS NULL OR oa.archived = 0)
    GROUP BY oa.agent_id, a.name, a.user_id
    ORDER BY confirmed_count DESC"
);

if ($agents_with_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID الوكيل</th><th>اسم الوكيل</th><th>ID المستخدم</th><th>عدد الطلبات المؤكدة</th><th>إجراء</th></tr>";

    foreach ($agents_with_orders as $agent) {
        $is_current = ($agent->agent_user_id == $current_user->ID) ? '✅ الحالي' : '';

        echo "<tr>";
        echo "<td>{$agent->agent_id}</td>";
        echo "<td>{$agent->agent_name} {$is_current}</td>";
        echo "<td>{$agent->agent_user_id}</td>";
        echo "<td><strong>{$agent->confirmed_count}</strong></td>";
        echo "<td>";

        if ($agent->agent_user_id != $current_user->ID && $agent->confirmed_count > 0) {
            echo "<a href='?action=link_to_agent&agent_id={$agent->agent_id}' style='background: #4caf50; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; font-size: 12px;'>🔗 ربط بي</a>";
        } elseif ($agent->agent_user_id == $current_user->ID) {
            echo "<span style='color: green; font-weight: bold;'>✅ مربوط</span>";
        }

        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ لا توجد طلبات مؤكدة من أي وكيل!</p>";
}

// Handle linking action
if (isset($_GET['action']) && $_GET['action'] === 'link_to_agent' && isset($_GET['agent_id'])) {
    $agent_id = intval($_GET['agent_id']);

    // Update agent to link with current user
    $result = $wpdb->update(
        $wpdb->prefix . 'hozi_agents',
        array('user_id' => $current_user->ID),
        array('id' => $agent_id),
        array('%d'),
        array('%d')
    );

    if ($result !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0; border: 2px solid #4caf50;'>";
        echo "<h3>🎉 تم ربط الوكيل بنجاح!</h3>";
        echo "<p><strong>المستخدم:</strong> {$current_user->display_name} (ID: {$current_user->ID})</p>";
        echo "<p><strong>الوكيل:</strong> ID {$agent_id}</p>";
        echo "<p><a href='?' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 تحديث الصفحة</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>❌ فشل في ربط الوكيل!</h3>";
        echo "</div>";
    }
}

// Handle manual transfer to tracking
if (isset($_GET['action']) && $_GET['action'] === 'transfer_to_tracking') {
    echo "<h2>🚀 نقل الطلبات المؤكدة إلى متابعة التوصيل:</h2>";

    // Get all confirmed orders not in tracking
    $confirmed_orders = $wpdb->get_results(
        "SELECT oa.order_id, oa.agent_id, a.name as agent_name, p.post_status
         FROM {$wpdb->prefix}hozi_order_assignments oa
         LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
         LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
         LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
         WHERE oa.confirmation_status = 'confirmed'
         AND (oa.archived IS NULL OR oa.archived = 0)
         AND ot.order_id IS NULL
         ORDER BY oa.confirmed_at DESC"
    );

    if (empty($confirmed_orders)) {
        echo "<p style='color: orange;'>⚠️ لا توجد طلبات مؤكدة تحتاج للنقل</p>";
    } else {
        $transfer_count = 0;

        foreach ($confirmed_orders as $order_data) {
            // Add to tracking system
            $result = $wpdb->insert(
                $wpdb->prefix . 'hozi_order_tracking',
                array(
                    'order_id' => $order_data->order_id,
                    'agent_id' => $order_data->agent_id,
                    'status' => 'out_for_delivery',
                    'previous_status' => null,
                    'reason_category' => 'manual_transfer',
                    'reason_details' => '',
                    'notes' => 'تم النقل يدوياً من الطلبات المؤكدة إلى متابعة التوصيل',
                    'updated_by' => get_current_user_id() ?: 1,
                    'updated_at' => current_time('mysql'),
                    'created_at' => current_time('mysql')
                ),
                array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
            );

            if ($result) {
                $transfer_count++;

                // Update WooCommerce order status to processing
                $order = wc_get_order($order_data->order_id);
                if ($order) {
                    $order->update_status('processing', 'تم نقل الطلب إلى متابعة التوصيل - أكدلي');
                    $order->add_order_note(
                        sprintf(
                            '🎯 تم نقل الطلب إلى نظام متابعة التوصيل%s%s',
                            "\n👤 الوكيل: " . $order_data->agent_name,
                            "\n📊 الحالة: في الطريق للتوصيل"
                        ),
                        0
                    );
                }

                echo "<p style='color: green;'>✅ تم نقل الطلب #{$order_data->order_id} - الوكيل: {$order_data->agent_name}</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل نقل الطلب #{$order_data->order_id}</p>";
            }
        }

        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0; border: 2px solid #4caf50;'>";
        echo "<h3>🎉 تم النقل بنجاح!</h3>";
        echo "<p><strong>عدد الطلبات المنقولة:</strong> {$transfer_count} من " . count($confirmed_orders) . "</p>";
        echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 فتح صفحة متابعة التوصيل</a></p>";
        echo "<p><a href='?' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 تحديث التشخيص</a></p>";
        echo "</div>";
    }
}

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0; border: 2px solid #4caf50;'>";
echo "<h2>🎉 تم تطبيق التسلسل المطلوب!</h2>";
echo "<p><strong>✅ التسلسل الجديد للطلبات:</strong></p>";
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>1️⃣ الطلب الجديد:</strong> <span style='color: #007cba;'>قيد التنفيذ (Processing)</span></p>";
echo "<p><strong>2️⃣ بعد التأكيد:</strong> <span style='color: #ff9800;'>قيد الانتظار (On-Hold)</span></p>";
echo "<p><strong>3️⃣ النقل التلقائي:</strong> <span style='color: #4caf50;'>إلى متابعة التوصيل</span></p>";
echo "</div>";
echo "<p>🔧 <strong>التحديثات المطبقة:</strong></p>";
echo "<ul>";
echo "<li>✅ الطلبات الجديدة تبدأ بحالة 'قيد التنفيذ'</li>";
echo "<li>✅ الطلبات المؤكدة تصبح 'قيد الانتظار'</li>";
echo "<li>✅ النقل التلقائي إلى متابعة التوصيل</li>";
echo "<li>✅ رسائل واضحة لكل مرحلة</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🛠️ إجراءات سريعة:</h2>";
echo "<p><a href='?action=transfer_to_tracking' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 نقل الطلبات القديمة (إن وجدت)</a></p>";
echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 فتح صفحة متابعة التوصيل</a></p>";
echo "<p><a href='admin.php?page=hozi-akadly-my-orders' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📋 فتح الطلبات المخصصة لي</a></p>";
echo "<p><a href='?' style='background: #9c27b0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 تحديث التشخيص</a></p>";

if (count($delivery_orders) > 0) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>💡 إذا لم تظهر الطلبات في صفحة التتبع:</h3>";
    echo "<ol>";
    echo "<li>تأكد من أنك تستخدم نفس المستخدم في كلا الصفحتين</li>";
    echo "<li>امسح الكاش (Cache) من المتصفح</li>";
    echo "<li>تحقق من وجود أخطاء JavaScript في المتصفح</li>";
    echo "<li>تأكد من أن ملف delivery-tracking.php يتم تحميله بشكل صحيح</li>";
    echo "</ol>";
    echo "</div>";
}
?>
