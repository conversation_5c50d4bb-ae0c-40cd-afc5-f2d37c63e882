<?php
/**
 * Test the new tracking query
 */

require_once('wp-config.php');

global $wpdb;

echo "<h1>🔍 اختبار الاستعلام الجديد لصفحة التتبع</h1>";

// Get current agent
$user_id = get_current_user_id();
$agent_data = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d AND is_active = 1",
    $user_id
));

if (!$agent_data) {
    echo "<p>❌ لم يتم العثور على بيانات الوكيل للمستخدم الحالي</p>";
    exit;
}

echo "<p>✅ <strong>الوكيل:</strong> {$agent_data->name} (ID: {$agent_data->id})</p>";

echo "<h2>📋 الاستعلام الجديد:</h2>";

// Test the new query from delivery-tracking.php
$completed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT DISTINCT
        oa.order_id,
        oa.confirmed_at,
        oa.notes as confirmation_notes,
        p.post_date as order_date,
        p.post_status,
        ot.status as delivery_status,
        ot.notes as delivery_notes,
        ot.updated_at as delivery_date,
        ot.created_at as tracking_created
    FROM {$wpdb->prefix}hozi_order_assignments oa
    INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
    LEFT JOIN (
        SELECT order_id, status, notes, updated_at, created_at,
               ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY updated_at DESC) as rn
        FROM {$wpdb->prefix}hozi_order_tracking
    ) ot ON (oa.order_id = ot.order_id AND ot.rn = 1)
    WHERE oa.agent_id = %d
    AND oa.confirmation_status = 'confirmed'
    AND (oa.archived IS NULL OR oa.archived = 0)
    AND p.post_status NOT IN ('trash', 'auto-draft', 'inherit')
    ORDER BY oa.confirmed_at DESC
    LIMIT 50",
    $agent_data->id
));

echo "<p><strong>عدد النتائج:</strong> " . count($completed_orders) . "</p>";

if ($completed_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>رقم الطلب</th>";
    echo "<th>تاريخ التأكيد</th>";
    echo "<th>حالة WC</th>";
    echo "<th>حالة التتبع</th>";
    echo "<th>ملاحظات التتبع</th>";
    echo "<th>تاريخ التتبع</th>";
    echo "</tr>";
    
    foreach ($completed_orders as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>{$order->post_status}</td>";
        echo "<td>" . ($order->delivery_status ?: '<span style="color: orange;">بانتظار التتبع</span>') . "</td>";
        echo "<td>" . ($order->delivery_notes ?: '-') . "</td>";
        echo "<td>" . ($order->delivery_date ? date('Y/m/d H:i', strtotime($order->delivery_date)) : '-') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ لا توجد نتائج!</p>";
    
    // Debug: Check confirmed orders without tracking
    echo "<h3>🔍 فحص الطلبات المؤكدة بدون تتبع:</h3>";
    
    $confirmed_only = $wpdb->get_results($wpdb->prepare(
        "SELECT oa.order_id, oa.confirmed_at, oa.archived, p.post_status
         FROM {$wpdb->prefix}hozi_order_assignments oa
         INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
         WHERE oa.agent_id = %d
         AND oa.confirmation_status = 'confirmed'
         ORDER BY oa.confirmed_at DESC
         LIMIT 10",
        $agent_data->id
    ));
    
    echo "<p><strong>الطلبات المؤكدة (بدون شروط إضافية):</strong> " . count($confirmed_only) . "</p>";
    
    if ($confirmed_only) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>مؤرشف</th><th>حالة WC</th></tr>";
        foreach ($confirmed_only as $order) {
            echo "<tr>";
            echo "<td>#{$order->order_id}</td>";
            echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
            echo "<td>" . ($order->archived ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$order->post_status}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

echo "<h2>📊 إحصائيات سريعة:</h2>";

// Quick stats
$total_confirmed = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments 
     WHERE agent_id = %d AND confirmation_status = 'confirmed'",
    $agent_data->id
));

$total_archived = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments 
     WHERE agent_id = %d AND confirmation_status = 'confirmed' AND archived = 1",
    $agent_data->id
));

$total_with_tracking = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(DISTINCT oa.order_id) 
     FROM {$wpdb->prefix}hozi_order_assignments oa
     INNER JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
     WHERE oa.agent_id = %d AND oa.confirmation_status = 'confirmed'",
    $agent_data->id
));

echo "<ul>";
echo "<li><strong>إجمالي المؤكدة:</strong> {$total_confirmed}</li>";
echo "<li><strong>المؤرشفة:</strong> {$total_archived}</li>";
echo "<li><strong>لها سجل تتبع:</strong> {$total_with_tracking}</li>";
echo "<li><strong>بدون سجل تتبع:</strong> " . ($total_confirmed - $total_with_tracking) . "</li>";
echo "</ul>";
?>
