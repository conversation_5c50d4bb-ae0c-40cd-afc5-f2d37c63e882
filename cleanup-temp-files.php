<?php
/**
 * Cleanup Temporary Fix Files
 * 
 * This file removes all temporary fix files after the permanent fix has been applied.
 * Place this file in your WordPress root directory and access via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🧹 تنظيف ملفات الإصلاح المؤقتة - Akadly</h1>";

// Check if we're applying the cleanup
$apply_cleanup = isset($_GET['apply_cleanup']) && $_GET['apply_cleanup'] == '1';

if ($apply_cleanup) {
    echo "<h2>⚡ جاري تنظيف الملفات المؤقتة...</h2>";
    
    // List of temporary files to remove
    $temp_files = array(
        'akadly-tracking-fix.php',
        'debug-auto-tracking-transfer.php',
        'test-delivery-tracking-fix.php',
        'quick-fix-delivery-tracking.php',
        'final-delivery-tracking-fix.php',
        'debug-tracking-flow.php',
        'cleanup-temp-files.php' // This file itself
    );
    
    $removed_count = 0;
    $failed_files = array();
    
    foreach ($temp_files as $file) {
        $file_path = ABSPATH . $file;
        
        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                echo "<p style='color: green;'>✅ تم حذف: {$file}</p>";
                $removed_count++;
            } else {
                echo "<p style='color: red;'>❌ فشل في حذف: {$file}</p>";
                $failed_files[] = $file;
            }
        } else {
            echo "<p style='color: gray;'>⚪ غير موجود: {$file}</p>";
        }
    }
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;'>";
    echo "<h3 style='color: #2e7d32; margin-top: 0;'>🎉 تم تنظيف الملفات المؤقتة!</h3>";
    echo "<p><strong>النتائج:</strong></p>";
    echo "<ul>";
    echo "<li>✅ تم حذف {$removed_count} ملف</li>";
    
    if (!empty($failed_files)) {
        echo "<li>❌ فشل في حذف " . count($failed_files) . " ملف: " . implode(', ', $failed_files) . "</li>";
    }
    
    echo "</ul>";
    echo "<p><strong>الإصلاح الدائم مطبق:</strong></p>";
    echo "<ul>";
    echo "<li>✅ النقل التلقائي للطلبات مدمج في الكود الأساسي</li>";
    echo "<li>✅ نظام النقل الاحتياطي مفعل</li>";
    echo "<li>✅ الفحص الدوري كل ساعة مجدول</li>";
    echo "<li>✅ الفحص التلقائي عند تحميل صفحة متابعة التوصيل</li>";
    echo "<li>✅ الإصلاح يعمل من أول تثبيت للإضافة</li>";
    echo "</ul>";
    echo "<p><strong>لا حاجة لملفات الإصلاح المؤقتة بعد الآن!</strong></p>";
    echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-delivery-tracking') . "' style='background: #0073aa; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px;'>🚀 انتقل إلى متابعة التوصيل</a></p>";
    echo "</div>";
    
} else {
    // Display information about the cleanup
    echo "<h2>🛠️ وصف عملية التنظيف</h2>";
    echo "<p>بعد تطبيق الإصلاح الدائم في الكود الأساسي للإضافة، لم تعد هناك حاجة لملفات الإصلاح المؤقتة.</p>";
    
    echo "<h2>🎯 الإصلاح الدائم المطبق:</h2>";
    echo "<ul>";
    echo "<li><strong>النقل التلقائي:</strong> مدمج في دالة update_confirmation_status</li>";
    echo "<li><strong>النقل الاحتياطي:</strong> يعمل بعد 10 ثواني كضمان إضافي</li>";
    echo "<li><strong>الفحص الدوري:</strong> كل ساعة للتأكد من عدم فقدان أي طلب</li>";
    echo "<li><strong>الفحص التلقائي:</strong> عند تحميل صفحة متابعة التوصيل</li>";
    echo "<li><strong>الفحص عند التحديث:</strong> عند تحديث الإضافة</li>";
    echo "</ul>";
    
    echo "<h2>📁 الملفات المؤقتة التي سيتم حذفها:</h2>";
    
    $temp_files = array(
        'akadly-tracking-fix.php' => 'ملف الإصلاح الأولي',
        'debug-auto-tracking-transfer.php' => 'ملف تشخيص النقل التلقائي',
        'test-delivery-tracking-fix.php' => 'ملف اختبار الإصلاح',
        'quick-fix-delivery-tracking.php' => 'ملف الإصلاح السريع',
        'final-delivery-tracking-fix.php' => 'ملف الإصلاح النهائي',
        'debug-tracking-flow.php' => 'ملف تشخيص مسار التتبع',
        'cleanup-temp-files.php' => 'ملف التنظيف هذا'
    );
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>اسم الملف</th><th>الوصف</th><th>الحالة</th></tr>";
    
    foreach ($temp_files as $file => $description) {
        $file_path = ABSPATH . $file;
        $exists = file_exists($file_path);
        $status = $exists ? '✅ موجود' : '⚪ غير موجود';
        
        echo "<tr>";
        echo "<td>{$file}</td>";
        echo "<td>{$description}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>⚠️ تأكيد مهم:</h2>";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 15px 0;'>";
    echo "<p><strong>تأكد من أن الإصلاح الدائم يعمل بشكل صحيح قبل حذف الملفات المؤقتة:</strong></p>";
    echo "<ol>";
    echo "<li>اختبر تأكيد طلب جديد</li>";
    echo "<li>تأكد من ظهور الطلب في متابعة التوصيل</li>";
    echo "<li>تحقق من عمل النظام للوكلاء المختلفين</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🚀 تطبيق التنظيف:</h2>";
    echo "<p><a href='?apply_cleanup=1' style='background: #dc3545; color: white; padding: 15px 25px; text-decoration: none; border-radius: 4px; font-size: 16px; display: inline-block; margin: 10px 0;'>🧹 حذف جميع الملفات المؤقتة</a></p>";
    
    echo "<p style='color: #666; font-size: 14px;'><strong>ملاحظة:</strong> هذا الإجراء لا يمكن التراجع عنه. تأكد من أن النظام يعمل بشكل صحيح قبل المتابعة.</p>";
}

echo "<hr>";
echo "<p>تم إنشاء هذا الملف بواسطة فريق Akadly - " . date('Y-m-d H:i:s') . "</p>";
?>
