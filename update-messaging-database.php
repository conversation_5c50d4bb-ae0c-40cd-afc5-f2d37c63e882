<?php
/**
 * Update Messaging Database Schema
 * Add archived column to hozi_messages table
 * 
 * Run this by visiting: /wp-admin/admin.php?page=hozi-akadly-settings&update_db=1
 */

// Function to update messaging database
function hozi_update_messaging_database() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'hozi_messages';
    
    // Check if table exists first
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if (!$table_exists) {
        // Create the table with new schema
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            sender_id int(11) NOT NULL,
            recipient_id int(11) DEFAULT NULL,
            recipient_type enum('agent', 'manager', 'all') DEFAULT 'agent',
            message_type enum('general', 'urgent', 'stock', 'order', 'product') DEFAULT 'general',
            subject varchar(255) NOT NULL,
            message text NOT NULL,
            priority enum('normal', 'high', 'urgent') DEFAULT 'normal',
            category varchar(50) DEFAULT 'general',
            related_order_id bigint(20) DEFAULT NULL,
            related_product_id bigint(20) DEFAULT NULL,
            attachments text DEFAULT NULL,
            is_read tinyint(1) DEFAULT 0,
            is_deleted tinyint(1) DEFAULT 0,
            is_archived tinyint(1) DEFAULT 0,
            requires_response tinyint(1) DEFAULT 0,
            parent_message_id int(11) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            read_at datetime DEFAULT NULL,
            archived_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY sender_id (sender_id),
            KEY recipient_id (recipient_id),
            KEY message_type (message_type),
            KEY priority (priority),
            KEY is_read (is_read),
            KEY is_archived (is_archived),
            KEY created_at (created_at),
            KEY parent_message_id (parent_message_id)
        ) $charset_collate;";
        
        dbDelta($sql);
        return "✅ Created new messaging table with archive support";
    }

    $results = array();

    // Check if is_archived column exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'is_archived'");

    if (empty($column_exists)) {
        $sql = "ALTER TABLE {$table_name} ADD COLUMN is_archived tinyint(1) DEFAULT 0 AFTER is_deleted";
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            $results[] = "✅ Added is_archived column";
        } else {
            $results[] = "❌ Failed to add is_archived column: " . $wpdb->last_error;
        }
    } else {
        $results[] = "✅ is_archived column already exists";
    }

    // Check if archived_at column exists
    $archived_at_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'archived_at'");

    if (empty($archived_at_exists)) {
        $sql = "ALTER TABLE {$table_name} ADD COLUMN archived_at datetime DEFAULT NULL AFTER read_at";
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            $results[] = "✅ Added archived_at column";
        } else {
            $results[] = "❌ Failed to add archived_at column: " . $wpdb->last_error;
        }
    } else {
        $results[] = "✅ archived_at column already exists";
    }

    // Add index for is_archived if it doesn't exist
    $indexes = $wpdb->get_results("SHOW INDEX FROM {$table_name} WHERE Key_name = 'is_archived'");
    if (empty($indexes)) {
        $sql = "ALTER TABLE {$table_name} ADD KEY is_archived (is_archived)";
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            $results[] = "✅ Added index for is_archived column";
        } else {
            $results[] = "❌ Failed to add index: " . $wpdb->last_error;
        }
    } else {
        $results[] = "✅ Index for is_archived already exists";
    }

    return implode("\n", $results);
}

// Auto-run if accessed with update_db parameter
if (isset($_GET['update_db']) && $_GET['update_db'] == '1') {
    echo '<div style="background: white; padding: 20px; margin: 20px; border: 1px solid #ccc; border-radius: 5px;">';
    echo '<h2>🔄 تحديث قاعدة بيانات نظام المراسلة</h2>';
    echo '<pre style="background: #f5f5f5; padding: 15px; border-radius: 3px;">';
    echo hozi_update_messaging_database();
    echo '</pre>';
    echo '<p><a href="' . admin_url('admin.php?page=hozi-akadly-settings') . '" class="button button-primary">العودة للإعدادات</a></p>';
    echo '</div>';
}
?>