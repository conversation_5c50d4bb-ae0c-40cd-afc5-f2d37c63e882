<?php
/**
 * Messaging Widget for Hozi Akadly
 * 
 * Floating chat widget for communication between managers and agents
 */

if (!defined('ABSPATH')) {
    exit;
}

$current_user = wp_get_current_user();
$agent_manager = new Hozi_Akadly_Agent_Manager();
$is_agent = $agent_manager->is_agent();
$current_agent = $is_agent ? $agent_manager->get_current_agent() : null;
?>

<!-- Messaging Widget -->
<div id="hozi-messaging-widget" class="hozi-messaging-widget">
    <!-- Widget Toggle Button -->
    <div id="hozi-messaging-toggle" class="hozi-messaging-toggle">
        <span class="hozi-message-icon">💬</span>
        <span id="hozi-unread-badge" class="hozi-unread-badge" style="display: none;">0</span>
    </div>
    
    <!-- Widget Panel -->
    <div id="hozi-messaging-panel" class="hozi-messaging-panel" style="display: none;">
        <!-- Header -->
        <div class="hozi-messaging-header">
            <h4>💬 الرسائل</h4>
            <div class="hozi-messaging-controls">
                <button type="button" id="hozi-new-message-btn" class="hozi-btn-icon" title="رسالة جديدة">
                    ✏️
                </button>
                <button type="button" id="hozi-refresh-messages-btn" class="hozi-btn-icon" title="تحديث">
                    🔄
                </button>
                <button type="button" id="hozi-minimize-widget" class="hozi-btn-icon" title="تصغير">
                    ➖
                </button>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="hozi-messaging-filters">
            <select id="hozi-message-filter" class="hozi-select-small">
                <option value="">جميع الرسائل</option>
                <option value="unread">غير مقروءة</option>
                <option value="urgent">عاجلة</option>
                <option value="stock">المخزون</option>
                <option value="order">الطلبات</option>
            </select>
            <input type="text" id="hozi-message-search" placeholder="بحث..." class="hozi-input-small">
        </div>
        
        <!-- Messages List -->
        <div id="hozi-messages-list" class="hozi-messages-list">
            <div class="hozi-loading">🔄 جاري التحميل...</div>
        </div>
        
        <!-- Message Composer (hidden by default) -->
        <div id="hozi-message-composer" class="hozi-message-composer" style="display: none;">
            
            <form id="hozi-message-form">
                <!-- Hidden fields for compatibility -->
                <input type="hidden" name="message_type" value="general">
                <input type="hidden" name="category" value="general">

                <?php if (!$is_agent): ?>
                <div class="hozi-form-group">
                    <label>المرسل إليه:</label>
                    <select name="recipient_type" id="hozi-recipient-type" required>
                        <option value="">اختر...</option>
                        <option value="all">جميع الوكلاء</option>
                        <option value="agent">وكيل محدد</option>
                    </select>
                </div>
                
                <div class="hozi-form-group" id="hozi-specific-agent" style="display: none;">
                    <label>الوكيل:</label>
                    <select name="recipient_id" id="hozi-recipient-id">
                        <option value="">اختر وكيل...</option>
                        <?php
                        $agents = $agent_manager->get_all_agents();
                        foreach ($agents as $agent) {
                            echo "<option value='{$agent->user_id}'>{$agent->name}</option>";
                        }
                        ?>
                    </select>
                </div>
                <?php else: ?>
                <input type="hidden" name="recipient_type" value="manager">
                <div class="hozi-form-group">
                    <p style="color: #666; font-size: 14px; margin: 0; padding: 10px; background: #f0f8ff; border-radius: 6px; border-left: 4px solid #2196f3;">
                        📤 سيتم إرسال الرسالة إلى المدير
                    </p>
                </div>
                <?php endif; ?>
                
                <div class="hozi-form-group">
                    <label>نوع الرسالة:</label>
                    <select name="message_type" required>
                        <option value="general">عامة</option>
                        <option value="urgent">عاجلة</option>
                        <option value="stock">مخزون</option>
                        <option value="order">طلب</option>
                        <option value="product">منتج</option>
                    </select>
                </div>
                
                <div class="hozi-form-group">
                    <label>الأولوية:</label>
                    <select name="priority">
                        <option value="normal">عادي</option>
                        <option value="high">مهم</option>
                        <option value="urgent">عاجل</option>
                    </select>
                </div>
                
                <div class="hozi-form-group">
                    <label>الموضوع:</label>
                    <input type="text" name="subject" placeholder="موضوع الرسالة..." required>
                </div>
                
                <div class="hozi-form-group">
                    <label>الرسالة:</label>
                    <textarea name="message" rows="6" placeholder="اكتب رسالتك هنا..." required style="min-height: 120px; max-height: 180px; resize: vertical; width: 100%; box-sizing: border-box; font-size: 14px; line-height: 1.5; padding: 12px;"></textarea>
                </div>
                
                <div class="hozi-form-group">
                    <label>
                        <input type="checkbox" name="requires_response" value="1">
                        يتطلب رد
                    </label>
                </div>
                
                <div class="hozi-form-actions" style="display: flex !important; visibility: visible !important;">
                    <button type="submit" class="hozi-btn-primary" style="display: inline-block !important; visibility: visible !important;">📤 إرسال</button>
                    <button type="button" id="hozi-cancel-message" class="hozi-btn-secondary" style="display: inline-block !important; visibility: visible !important;">إلغاء</button>
                </div>
            </form>
        </div>
        
        <!-- Message Thread (hidden by default) -->
        <div id="hozi-message-thread" class="hozi-message-thread" style="display: none;">
            <div class="hozi-thread-header">
                <button type="button" id="hozi-back-to-list" class="hozi-btn-icon">⬅️</button>
                <h5 id="hozi-thread-subject">موضوع الرسالة</h5>
            </div>
            
            <div id="hozi-thread-messages" class="hozi-thread-messages">
                <!-- Messages will be loaded here -->
            </div>
            
            <div class="hozi-thread-reply">
                <form id="hozi-reply-form">
                    <div class="hozi-reply-input-container">
                        <textarea name="reply_message" placeholder="اكتب ردك هنا..." rows="3" required style="min-height: 80px; max-height: 120px; resize: vertical; width: 100%; margin-bottom: 10px;"></textarea>
                        <button type="submit" class="hozi-btn-primary" style="float: left;">📤 رد</button>
                        <div style="clear: both;"></div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.hozi-messaging-widget {
    position: fixed;
    bottom: 20px;
    left: 20px; /* Moved to left side */
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: none; /* Hidden by default, controlled by header button */
}

.hozi-messaging-toggle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 15px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    position: relative;
    z-index: 9998;
}

.hozi-messaging-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0,0,0,0.2);
}

.hozi-message-icon {
    font-size: 20px;
    color: white;
}

.hozi-unread-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.hozi-messaging-panel {
    position: absolute;
    bottom: 60px;
    left: 0; /* Changed from right to left */
    width: 400px;
    max-height: 550px; /* Increased height */
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    border: 1px solid #e1e8ed;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    z-index: 9997;
}

.hozi-messaging-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hozi-messaging-header h4 {
    margin: 0;
    font-size: 16px;
}

.hozi-messaging-controls {
    display: flex;
    gap: 8px;
}

.hozi-btn-icon {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    font-size: 12px;
}

.hozi-btn-icon:hover {
    background: rgba(255,255,255,0.3);
}

.hozi-messaging-filters {
    padding: 10px;
    display: flex;
    gap: 8px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e8ed;
}

.hozi-select-small, .hozi-input-small {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.hozi-messages-list {
    flex: 1;
    overflow-y: auto;
    max-height: 300px;
}

.hozi-message-item {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.2s;
}

.hozi-message-item:hover {
    background: #f8f9fa;
}

.hozi-message-item.unread {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.hozi-message-item.urgent {
    border-left: 4px solid #f44336;
}

.hozi-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.hozi-message-sender {
    font-weight: bold;
    font-size: 13px;
    color: #333;
}

.hozi-message-time {
    font-size: 11px;
    color: #666;
}

.hozi-message-subject {
    font-size: 13px;
    color: #333;
    margin-bottom: 3px;
    font-weight: 500;
}

.hozi-message-preview {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

.hozi-message-composer, .hozi-message-thread {
    padding: 15px;
    background: white;
}

.hozi-composer-header, .hozi-thread-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.hozi-form-group {
    margin-bottom: 12px;
}

.hozi-form-group label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    font-weight: 500;
    color: #333;
}

.hozi-form-group input, .hozi-form-group select, .hozi-form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    box-sizing: border-box;
    resize: vertical;
}

.hozi-form-group textarea {
    min-height: 80px;
    max-height: 120px;
}

.hozi-form-actions {
    display: flex !important;
    gap: 8px;
    margin-top: 10px;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 15px;
    border-top: 2px solid #eee;
    background: #f8f9fa;
    margin-left: -15px;
    margin-right: -15px;
    position: sticky;
    bottom: 0;
    z-index: 100;
}

.hozi-btn-primary, .hozi-btn-secondary {
    padding: 8px 16px !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    transition: all 0.2s !important;
    display: inline-block !important;
    text-decoration: none !important;
    text-align: center !important;
    min-width: 80px !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.hozi-btn-primary {
    background: #667eea;
    color: white;
}

.hozi-btn-primary:hover {
    background: #5a6fd8;
}

.hozi-btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.hozi-btn-secondary:hover {
    background: #e9ecef;
}

.hozi-loading {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 14px;
}

.hozi-thread-messages {
    max-height: 250px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.hozi-thread-message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    background: #f8f9fa;
}

.hozi-thread-message.own {
    background: #e3f2fd;
    margin-left: 20px;
}

.hozi-thread-message.other {
    background: #f1f8e9;
    margin-right: 20px;
}

.hozi-thread-reply textarea {
    resize: vertical;
    min-height: 60px;
}

/* 🚀 FIXED: Ensure buttons are always visible */
.hozi-message-composer .hozi-form-actions,
.hozi-thread-reply .hozi-reply-input-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.hozi-message-composer button,
.hozi-thread-reply button {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
}

.hozi-reply-input-container {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
}

@media (max-width: 768px) {
    .hozi-messaging-panel {
        width: 360px;
        max-height: 500px; /* Increased for mobile */
        bottom: 55px;
    }

    .hozi-messaging-widget {
        bottom: 10px;
        left: 10px; /* Changed from right to left */
    }

    .hozi-messaging-toggle {
        width: 45px;
        height: 45px;
    }

    .hozi-message-icon {
        font-size: 18px;
    }

    .hozi-form-group textarea {
        min-height: 100px; /* Increased for mobile */
        max-height: 140px;
        font-size: 16px; /* Larger font for mobile */
        padding: 12px;
    }

    .hozi-messaging-header h4 {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .hozi-messaging-panel {
        width: calc(100vw - 40px);
        max-width: 350px;
        left: -10px; /* Changed from right to left */
    }

    .hozi-form-group {
        margin-bottom: 10px;
    }

    .hozi-form-group input, .hozi-form-group select, .hozi-form-group textarea {
        font-size: 12px;
        padding: 6px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize messaging widget
    const MessagingWidget = {
        isOpen: false,
        currentView: 'list', // list, composer, thread
        currentThreadId: null,
        unreadCount: 0,
        
        init: function() {
            this.bindEvents();
            this.loadUnreadCount();
            this.loadMessages();
            this.startPolling();
        },
        
        bindEvents: function() {
            const self = this;

            // Toggle widget
            $(document).on('click', '#hozi-messaging-toggle', function() {
                self.toggleWidget();
            });

            // Minimize widget
            $(document).on('click', '#hozi-minimize-widget', function() {
                self.closeWidget();
            });

            // New message button
            $(document).on('click', '#hozi-new-message-btn', function() {
                self.showComposer();
            });

            // Refresh messages
            $(document).on('click', '#hozi-refresh-messages-btn', function() {
                self.loadMessages();
            });

            // Close composer
            $(document).on('click', '#hozi-close-composer, #hozi-cancel-message', function() {
                self.showMessagesList();
            });

            // Back to list from thread
            $(document).on('click', '#hozi-back-to-list', function() {
                self.showMessagesList();
            });
            
            // Recipient type change
            $('#hozi-recipient-type').on('change', function() {
                if ($(this).val() === 'agent') {
                    $('#hozi-specific-agent').show();
                } else {
                    $('#hozi-specific-agent').hide();
                }
            });
            
            // Message form submit
            $('#hozi-message-form').on('submit', function(e) {
                e.preventDefault();
                MessagingWidget.sendMessage();
            });
            
            // Reply form submit
            $('#hozi-reply-form').on('submit', (e) => {
                e.preventDefault();
                this.sendReply();
            });
            
            // Filter and search
            $('#hozi-message-filter, #hozi-message-search').on('change keyup', () => {
                this.loadMessages();
            });
            
            // Message click
            $(document).on('click', '.hozi-message-item', function() {
                const messageId = $(this).data('message-id');
                MessagingWidget.openThread(messageId);
            });
        },
        
        toggleWidget: function() {
            // Show the widget first if it's hidden
            $('.hozi-messaging-widget').show();

            if (this.isOpen) {
                this.closeWidget();
            } else {
                this.openWidget();
            }
        },

        openWidget: function() {
            $('.hozi-messaging-widget').show();
            $('#hozi-messaging-panel').slideDown(300);
            this.isOpen = true;
            this.loadMessages();
        },

        closeWidget: function() {
            $('#hozi-messaging-panel').slideUp(300);
            this.isOpen = false;
            // Don't hide the widget completely, just close the panel
        },

        showMessagesList: function() {
            $('#hozi-message-composer, #hozi-message-thread').hide();
            $('#hozi-messages-list, .hozi-messaging-filters').show();
            this.currentView = 'list';
        },

        showComposer: function() {
            $('#hozi-messages-list, #hozi-message-thread, .hozi-messaging-filters').hide();
            $('#hozi-message-composer').show();
            this.currentView = 'composer';
        },

        showThread: function(threadId) {
            $('#hozi-messages-list, #hozi-message-composer, .hozi-messaging-filters').hide();
            $('#hozi-message-thread').show();
            this.currentView = 'thread';
            this.currentThreadId = threadId;
        },

        loadUnreadCount: function() {
            $.post(ajaxurl, {
                action: 'hozi_get_unread_count',
                nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>'
            }, (response) => {
                if (response.success) {
                    this.updateUnreadBadge(response.data.count);
                }
            });
        },

        updateUnreadBadge: function(count) {
            this.unreadCount = count;
            const $badge = $('#hozi-unread-badge');

            if (count > 0) {
                $badge.text(count).show();
            } else {
                $badge.hide();
            }
        },

        loadMessages: function() {
            const filter = $('#hozi-message-filter').val();
            const search = $('#hozi-message-search').val();

            $('#hozi-messages-list').html('<div class="hozi-loading">🔄 جاري التحميل...</div>');

            $.post(ajaxurl, {
                action: 'hozi_get_messages',
                nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>',
                limit: 20,
                unread_only: filter === 'unread',
                message_type: filter && filter !== 'unread' ? filter : '',
                search: search
            }, (response) => {
                if (response.success) {
                    this.renderMessages(response.data);
                } else {
                    $('#hozi-messages-list').html('<div class="hozi-loading">❌ فشل في تحميل الرسائل</div>');
                }
            });
        },

        renderMessages: function(messages) {
            const $list = $('#hozi-messages-list');

            if (messages.length === 0) {
                $list.html('<div class="hozi-loading">📭 لا توجد رسائل</div>');
                return;
            }

            let html = '';
            messages.forEach(message => {
                const isUnread = !message.is_read;
                const isUrgent = message.priority === 'urgent';
                const timeAgo = this.timeAgo(message.created_at);

                html += `
                    <div class="hozi-message-item ${isUnread ? 'unread' : ''} ${isUrgent ? 'urgent' : ''}"
                         data-message-id="${message.id}">
                        <div class="hozi-message-header">
                            <span class="hozi-message-sender">${message.sender_name}</span>
                            <span class="hozi-message-time">${timeAgo}</span>
                        </div>
                        <div class="hozi-message-subject">${message.subject || 'بدون موضوع'}</div>
                        <div class="hozi-message-preview">${this.truncateText(message.message, 60)}</div>
                    </div>
                `;
            });

            $list.html(html);
        },

        sendMessage: function() {
            console.log('🚀 Sending message...');

            const form = $('#hozi-message-form')[0];
            const formData = new FormData(form);
            formData.append('action', 'hozi_send_message');
            formData.append('nonce', '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>');

            // Debug form data
            for (let pair of formData.entries()) {
                console.log('Form data:', pair[0], pair[1]);
            }

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    $('#hozi-message-form button[type="submit"]').prop('disabled', true).text('جاري الإرسال...');
                },
                success: function(response) {
                    console.log('✅ Response:', response);
                    if (response.success) {
                        MessagingWidget.showMessagesList();
                        MessagingWidget.loadMessages();
                        form.reset();
                        MessagingWidget.showNotification('✅ تم إرسال الرسالة بنجاح');
                    } else {
                        MessagingWidget.showNotification('❌ ' + (response.data || 'حدث خطأ'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX Error:', xhr, status, error);
                    MessagingWidget.showNotification('❌ حدث خطأ في الإرسال', 'error');
                },
                complete: function() {
                    $('#hozi-message-form button[type="submit"]').prop('disabled', false).text('📤 إرسال');
                }
            });
        },

        openThread: function(messageId) {
            this.showThread(messageId);

            // Mark as read
            $.post(ajaxurl, {
                action: 'hozi_mark_message_read',
                nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>',
                message_id: messageId
            });

            // Load thread
            $.post(ajaxurl, {
                action: 'hozi_get_message_thread',
                nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>',
                message_id: messageId
            }, (response) => {
                if (response.success) {
                    this.renderThread(response.data);
                    this.loadUnreadCount(); // Update badge
                }
            });
        },

        renderThread: function(messages) {
            if (messages.length === 0) return;

            const mainMessage = messages[0];
            $('#hozi-thread-subject').text(mainMessage.subject || 'بدون موضوع');

            let html = '';
            const currentUserId = <?php echo get_current_user_id(); ?>;

            messages.forEach(message => {
                const isOwn = message.sender_id == currentUserId;
                const timeAgo = this.timeAgo(message.created_at);

                html += `
                    <div class="hozi-thread-message ${isOwn ? 'own' : 'other'}">
                        <div class="hozi-message-header">
                            <strong>${message.sender_name}</strong>
                            <span class="hozi-message-time">${timeAgo}</span>
                        </div>
                        <div class="hozi-message-content">${this.formatMessage(message.message)}</div>
                    </div>
                `;
            });

            $('#hozi-thread-messages').html(html);
        },

        sendReply: function() {
            const message = $('#hozi-reply-form textarea[name="reply_message"]').val();

            if (!message.trim()) {
                this.showNotification('❌ يرجى كتابة رد', 'error');
                return;
            }

            $.post(ajaxurl, {
                action: 'hozi_send_message',
                nonce: '<?php echo wp_create_nonce('hozi_messaging_nonce'); ?>',
                message: message,
                parent_message_id: this.currentThreadId,
                subject: 'رد',
                message_type: 'general'
            }, (response) => {
                if (response.success) {
                    $('#hozi-reply-form textarea').val('');
                    this.openThread(this.currentThreadId); // Reload thread
                    this.showNotification('✅ تم إرسال الرد');
                } else {
                    this.showNotification('❌ فشل في إرسال الرد', 'error');
                }
            });
        },

        startPolling: function() {
            // Check for new messages every 30 seconds
            setInterval(() => {
                this.loadUnreadCount();
                if (this.isOpen && this.currentView === 'list') {
                    this.loadMessages();
                }
            }, 30000);
        },

        timeAgo: function(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'الآن';
            if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' د';
            if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' س';
            return Math.floor(diffInSeconds / 86400) + ' ي';
        },

        truncateText: function(text, length) {
            return text.length > length ? text.substring(0, length) + '...' : text;
        },

        formatMessage: function(message) {
            return message.replace(/\n/g, '<br>');
        },

        showNotification: function(message, type = 'success') {
            // Simple notification - you can enhance this
            const className = type === 'error' ? 'notice-error' : 'notice-success';
            const $notice = $(`<div class="notice ${className} is-dismissible"><p>${message}</p></div>`);

            $('body').append($notice);
            setTimeout(() => $notice.remove(), 3000);
        }
    };

    // Initialize the widget with delay to ensure DOM is ready
    setTimeout(function() {
        MessagingWidget.init();
        console.log('💬 Messaging widget initialized');
    }, 1000);
});
</script>
