<?php
/**
 * Database management class
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Database {

    /**
     * Create plugin tables
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Agents table
        $agents_table = $wpdb->prefix . 'hozi_agents';
        $agents_sql = "CREATE TABLE $agents_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            name varchar(255) NOT NULL,
            phone varchar(50) DEFAULT NULL,
            email varchar(255) DEFAULT NULL,
            is_active tinyint(1) DEFAULT 1,
            max_orders_per_day int(11) DEFAULT 0,
            current_orders_count int(11) DEFAULT 0,
            total_confirmed int(11) DEFAULT 0,
            total_rejected int(11) DEFAULT 0,
            total_no_answer int(11) DEFAULT 0,
            last_assigned_at datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id),
            KEY is_active (is_active),
            KEY last_assigned_at (last_assigned_at)
        ) $charset_collate;";

        // Order assignments table
        $assignments_table = $wpdb->prefix . 'hozi_order_assignments';
        $assignments_sql = "CREATE TABLE $assignments_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            agent_id int(11) NOT NULL,
            assignment_method enum('auto','manual') DEFAULT 'auto',
            confirmation_status varchar(50) DEFAULT 'pending_confirmation',
            assigned_at datetime DEFAULT CURRENT_TIMESTAMP,
            confirmed_at datetime DEFAULT NULL,
            notes text DEFAULT NULL,
            archived tinyint(1) DEFAULT 0,
            archived_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY order_id (order_id),
            KEY agent_id (agent_id),
            KEY confirmation_status (confirmation_status),
            KEY assigned_at (assigned_at),
            KEY archived (archived),
            FOREIGN KEY (agent_id) REFERENCES $agents_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Confirmation logs table
        $logs_table = $wpdb->prefix . 'hozi_confirmation_logs';
        $logs_sql = "CREATE TABLE $logs_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            agent_id int(11) NOT NULL,
            action varchar(50) NOT NULL,
            old_status varchar(50) DEFAULT NULL,
            new_status varchar(50) DEFAULT NULL,
            notes text DEFAULT NULL,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY agent_id (agent_id),
            KEY action (action),
            KEY created_at (created_at),
            FOREIGN KEY (agent_id) REFERENCES $agents_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Upsell tracking table
        $upsell_table = $wpdb->prefix . 'hozi_upsell_tracking';
        $upsell_sql = "CREATE TABLE $upsell_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            agent_id int(11) NOT NULL,
            upsell_attempted tinyint(1) DEFAULT 0,
            upsell_successful tinyint(1) DEFAULT 0,
            upsell_amount decimal(10,2) DEFAULT 0.00,
            upsell_products text DEFAULT NULL,
            notes text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY agent_id (agent_id),
            KEY upsell_successful (upsell_successful),
            FOREIGN KEY (agent_id) REFERENCES $agents_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Order tracking table for post-confirmation status
        $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
        $tracking_sql = "CREATE TABLE $tracking_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            agent_id int(11) NOT NULL,
            status varchar(50) NOT NULL,
            previous_status varchar(50) DEFAULT NULL,
            reason_category varchar(100) DEFAULT NULL,
            reason_details text DEFAULT NULL,
            notes text DEFAULT NULL,
            updated_by int(11) NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY agent_id (agent_id),
            KEY status (status),
            KEY updated_at (updated_at),
            FOREIGN KEY (agent_id) REFERENCES $agents_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Create delivery tracking table (NEW SIMPLE APPROACH)
        $delivery_tracking_sql = "CREATE TABLE {$wpdb->prefix}hozi_delivery_tracking (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            agent_id bigint(20) NOT NULL,
            status varchar(50) NOT NULL,
            notes text DEFAULT NULL,
            updated_at datetime NOT NULL,
            created_at datetime NOT NULL,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY agent_id (agent_id),
            KEY status (status),
            KEY updated_at (updated_at)
        ) $charset_collate;";

        // Create messages table
        $messages_sql = "CREATE TABLE {$wpdb->prefix}hozi_messages (
            id int(11) NOT NULL AUTO_INCREMENT,
            sender_id int(11) NOT NULL,
            recipient_id int(11) DEFAULT NULL,
            recipient_type enum('agent', 'manager', 'all') DEFAULT 'agent',
            message_type enum('general', 'urgent', 'stock', 'order', 'product') DEFAULT 'general',
            subject varchar(255) NOT NULL,
            message text NOT NULL,
            priority enum('normal', 'high', 'urgent') DEFAULT 'normal',
            category varchar(50) DEFAULT 'general',
            related_order_id bigint(20) DEFAULT NULL,
            related_product_id bigint(20) DEFAULT NULL,
            attachments text DEFAULT NULL,
            is_read tinyint(1) DEFAULT 0,
            is_deleted tinyint(1) DEFAULT 0,
            requires_response tinyint(1) DEFAULT 0,
            parent_message_id int(11) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            read_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY sender_id (sender_id),
            KEY recipient_id (recipient_id),
            KEY message_type (message_type),
            KEY priority (priority),
            KEY is_read (is_read),
            KEY created_at (created_at),
            KEY parent_message_id (parent_message_id)
        ) $charset_collate;";

        dbDelta($agents_sql);
        dbDelta($assignments_sql);
        dbDelta($logs_sql);
        dbDelta($upsell_sql);
        dbDelta($tracking_sql);
        dbDelta($delivery_tracking_sql);
        dbDelta($messages_sql);

        // Update database version
        update_option('hozi_akadly_db_version', HOZI_AKADLY_VERSION);

        // Add archive columns if they don't exist (for existing installations)
        self::add_archive_columns();
    }

    /**
     * Add archive columns to existing installations
     */
    public static function add_archive_columns() {
        global $wpdb;

        $assignments_table = $wpdb->prefix . 'hozi_order_assignments';

        // Check if archived column exists
        $archived_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived'");
        if (empty($archived_exists)) {
            $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN archived tinyint(1) DEFAULT 0");
            $wpdb->query("ALTER TABLE $assignments_table ADD INDEX archived (archived)");
        }

        // Check if archived_at column exists
        $archived_at_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived_at'");
        if (empty($archived_at_exists)) {
            $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN archived_at datetime DEFAULT NULL");
        }
    }

    /**
     * Check if all plugin tables exist
     */
    public static function tables_exist() {
        global $wpdb;

        $tables = array(
            $wpdb->prefix . 'hozi_agents',
            $wpdb->prefix . 'hozi_order_assignments',
            $wpdb->prefix . 'hozi_confirmation_logs',
            $wpdb->prefix . 'hozi_upsell_tracking',
            $wpdb->prefix . 'hozi_order_tracking'
        );

        foreach ($tables as $table) {
            $result = $wpdb->get_var("SHOW TABLES LIKE '$table'");
            if ($result !== $table) {
                return false;
            }
        }

        return true;
    }

    /**
     * Drop plugin tables (for uninstall)
     */
    public static function drop_tables() {
        global $wpdb;

        $tables = array(
            $wpdb->prefix . 'hozi_order_tracking',
            $wpdb->prefix . 'hozi_upsell_tracking',
            $wpdb->prefix . 'hozi_confirmation_logs',
            $wpdb->prefix . 'hozi_order_assignments',
            $wpdb->prefix . 'hozi_agents'
        );

        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }

        delete_option('hozi_akadly_db_version');
    }

    /**
     * Get agent by user ID
     */
    public static function get_agent_by_user_id($user_id) {
        global $wpdb;

        // Check if table exists first
        $table_name = $wpdb->prefix . 'hozi_agents';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            return null;
        }

        try {
            return $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d",
                $user_id
            ));
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get all active agents
     */
    public static function get_active_agents() {
        global $wpdb;

        return $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 ORDER BY last_assigned_at ASC"
        );
    }

    /**
     * Get agent assignments
     */
    public static function get_agent_assignments($agent_id, $status = null, $limit = null) {
        global $wpdb;

        $where = $wpdb->prepare("WHERE agent_id = %d", $agent_id);

        if ($status) {
            $where .= $wpdb->prepare(" AND confirmation_status = %s", $status);
        }

        $limit_clause = $limit ? $wpdb->prepare("LIMIT %d", $limit) : '';

        return $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments
             $where
             ORDER BY assigned_at DESC
             $limit_clause"
        );
    }

    /**
     * Update agent stats
     */
    public static function update_agent_stats($agent_id) {
        global $wpdb;

        $stats = $wpdb->get_row($wpdb->prepare(
            "SELECT
                COUNT(CASE WHEN confirmation_status = 'confirmed' THEN 1 END) as confirmed,
                COUNT(CASE WHEN confirmation_status = 'rejected' THEN 1 END) as rejected,
                COUNT(CASE WHEN confirmation_status = 'no_answer' THEN 1 END) as no_answer,
                COUNT(CASE WHEN confirmation_status = 'pending_confirmation' THEN 1 END) as current_orders
             FROM {$wpdb->prefix}hozi_order_assignments
             WHERE agent_id = %d",
            $agent_id
        ));

        if ($stats) {
            $wpdb->update(
                $wpdb->prefix . 'hozi_agents',
                array(
                    'total_confirmed' => $stats->confirmed,
                    'total_rejected' => $stats->rejected,
                    'total_no_answer' => $stats->no_answer,
                    'current_orders_count' => $stats->current_orders
                ),
                array('id' => $agent_id)
            );
        }
    }

    /**
     * Log confirmation action
     */
    public static function log_action($order_id, $agent_id, $action, $old_status = null, $new_status = null, $notes = null) {
        global $wpdb;

        return $wpdb->insert(
            $wpdb->prefix . 'hozi_confirmation_logs',
            array(
                'order_id' => $order_id,
                'agent_id' => $agent_id,
                'action' => $action,
                'old_status' => $old_status,
                'new_status' => $new_status,
                'notes' => $notes,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => current_time('mysql')
            )
        );
    }
}
