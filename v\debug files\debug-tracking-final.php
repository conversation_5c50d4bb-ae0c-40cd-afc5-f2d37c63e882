<?php
// Debug tracking system - Final diagnosis
require_once('wp-config.php');
require_once('wp-load.php');

if (!is_user_logged_in()) {
    wp_redirect(wp_login_url());
    exit;
}

global $wpdb;

echo "<h1>🔍 تشخيص نهائي لنظام متابعة التوصيل</h1>";

// Get current user info
$current_user = wp_get_current_user();
echo "<h2>👤 المستخدم الحالي:</h2>";
echo "<p><strong>ID:</strong> {$current_user->ID}</p>";
echo "<p><strong>الاسم:</strong> {$current_user->display_name}</p>";
echo "<p><strong>البريد:</strong> {$current_user->user_email}</p>";

// Check if user is agent
$agent = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d",
    $current_user->ID
));

if ($agent) {
    echo "<p>✅ <strong>الوكيل:</strong> {$agent->name} (ID: {$agent->id})</p>";
} else {
    echo "<p>❌ <strong>المستخدم ليس وكيل</strong></p>";
    exit;
}

echo "<h2>📋 فحص الطلب #5244:</h2>";

// Check order assignment
$assignment = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
    5244
));

if ($assignment) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ تخصيص الطلب:</h3>";
    echo "<p><strong>رقم الطلب:</strong> {$assignment->order_id}</p>";
    echo "<p><strong>الوكيل المخصص:</strong> {$assignment->agent_id}</p>";
    echo "<p><strong>حالة التأكيد:</strong> {$assignment->confirmation_status}</p>";
    echo "<p><strong>تاريخ التأكيد:</strong> {$assignment->confirmed_at}</p>";
    echo "<p><strong>مؤرشف:</strong> " . ($assignment->archived ? 'نعم' : 'لا') . "</p>";
    echo "</div>";
} else {
    echo "<p>❌ لا يوجد تخصيص للطلب #5244</p>";
}

// Check order tracking
$tracking = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d ORDER BY updated_at DESC LIMIT 1",
    5244
));

if ($tracking) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ تتبع الطلب:</h3>";
    echo "<p><strong>رقم الطلب:</strong> {$tracking->order_id}</p>";
    echo "<p><strong>الوكيل:</strong> {$tracking->agent_id}</p>";
    echo "<p><strong>الحالة:</strong> {$tracking->status}</p>";
    echo "<p><strong>الملاحظات:</strong> {$tracking->notes}</p>";
    echo "<p><strong>آخر تحديث:</strong> {$tracking->updated_at}</p>";
    echo "</div>";
} else {
    echo "<p>❌ لا يوجد تتبع للطلب #5244</p>";
}

echo "<h2>🔍 اختبار الاستعلام الجديد:</h2>";

// Test the FIXED query from delivery-tracking.php
$test_query = $wpdb->prepare(
    "SELECT DISTINCT
        ot.order_id,
        ot.status as delivery_status,
        ot.notes as delivery_notes,
        ot.updated_at as delivery_date,
        ot.created_at as tracking_created,
        oa.confirmed_at,
        oa.notes as confirmation_notes,
        p.post_date as order_date,
        p.post_status
    FROM {$wpdb->prefix}hozi_order_tracking ot
    INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
    INNER JOIN {$wpdb->prefix}posts p ON (ot.order_id = p.ID AND p.post_type = 'shop_order')
    WHERE oa.agent_id = %d
    AND oa.confirmation_status = 'confirmed'
    AND (oa.archived IS NULL OR oa.archived = 0)
    ORDER BY ot.updated_at DESC
    LIMIT 50",
    $agent->id
);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>📝 الاستعلام المستخدم:</h3>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 4px; overflow-x: auto;'>";
echo htmlspecialchars($test_query);
echo "</pre>";
echo "</div>";

$results = $wpdb->get_results($test_query);

echo "<p><strong>عدد النتائج:</strong> " . count($results) . "</p>";

if (count($results) > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ النتائج:</h3>";
    echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة التوصيل</th><th>تاريخ التحديث</th><th>حالة WC</th></tr>";
    foreach ($results as $row) {
        echo "<tr>";
        echo "<td>{$row->order_id}</td>";
        echo "<td>{$row->delivery_status}</td>";
        echo "<td>{$row->delivery_date}</td>";
        echo "<td>{$row->post_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ لا توجد نتائج!</h3>";
    echo "<p>الاستعلام لم يعيد أي نتائج. دعنا نحلل السبب:</p>";

    // Check each condition separately
    echo "<h4>🔍 فحص الشروط منفصلة:</h4>";

    // Check if order exists in tracking
    $tracking_exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
        5244
    ));
    echo "<p>✅ الطلب في جدول التتبع: " . ($tracking_exists ? 'نعم' : 'لا') . "</p>";

    // Check if assignment exists for this agent
    $assignment_exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d AND agent_id = %d",
        5244, $agent->id
    ));
    echo "<p>✅ التخصيص للوكيل الحالي: " . ($assignment_exists ? 'نعم' : 'لا') . "</p>";

    // Check confirmation status
    $is_confirmed = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d AND confirmation_status = 'confirmed'",
        5244
    ));
    echo "<p>✅ الطلب مؤكد: " . ($is_confirmed ? 'نعم' : 'لا') . "</p>";

    // Check if archived
    $is_archived = $wpdb->get_var($wpdb->prepare(
        "SELECT archived FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
        5244
    ));
    echo "<p>✅ الطلب غير مؤرشف: " . (($is_archived === null || $is_archived == 0) ? 'نعم' : 'لا') . "</p>";

    // Test step by step joins
    echo "<h4>🔍 اختبار الـ JOINs خطوة بخطوة:</h4>";

    // Step 1: Just tracking table
    $step1 = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
        5244
    ));
    echo "<p>1️⃣ جدول التتبع فقط: {$step1}</p>";

    // Step 2: Join with assignments
    $step2 = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking ot
         INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
         WHERE ot.order_id = %d",
        5244
    ));
    echo "<p>2️⃣ مع جدول التخصيصات: {$step2}</p>";

    // Step 3: Add agent condition
    $step3 = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking ot
         INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
         WHERE ot.order_id = %d AND oa.agent_id = %d",
        5244, $agent->id
    ));
    echo "<p>3️⃣ مع شرط الوكيل: {$step3}</p>";

    // Step 4: Add confirmation status
    $step4 = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking ot
         INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
         WHERE ot.order_id = %d AND oa.agent_id = %d AND oa.confirmation_status = 'confirmed'",
        5244, $agent->id
    ));
    echo "<p>4️⃣ مع شرط التأكيد: {$step4}</p>";

    // Step 5: Add posts join
    $step5 = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking ot
         INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
         INNER JOIN {$wpdb->prefix}posts p ON (ot.order_id = p.ID AND p.post_type = 'shop_order')
         WHERE ot.order_id = %d AND oa.agent_id = %d AND oa.confirmation_status = 'confirmed'",
        5244, $agent->id
    ));
    echo "<p>5️⃣ مع جدول المنشورات: {$step5}</p>";

    // Check if order exists in posts table
    echo "<h4>🔍 فحص جدول المنشورات:</h4>";
    $post_exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}posts WHERE ID = %d",
        5244
    ));
    echo "<p>📄 الطلب موجود في جدول المنشورات: " . ($post_exists ? 'نعم' : 'لا') . "</p>";

    if ($post_exists) {
        $post_type = $wpdb->get_var($wpdb->prepare(
            "SELECT post_type FROM {$wpdb->prefix}posts WHERE ID = %d",
            5244
        ));
        echo "<p>📝 نوع المنشور: {$post_type}</p>";

        $post_status = $wpdb->get_var($wpdb->prepare(
            "SELECT post_status FROM {$wpdb->prefix}posts WHERE ID = %d",
            5244
        ));
        echo "<p>📊 حالة المنشور: {$post_status}</p>";
    }

    // Test the exact query with archive condition
    echo "<h4>🔍 اختبار شرط الأرشفة:</h4>";
    $step6 = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking ot
         INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
         INNER JOIN {$wpdb->prefix}posts p ON (ot.order_id = p.ID AND p.post_type = 'shop_order')
         WHERE ot.order_id = %d AND oa.agent_id = %d AND oa.confirmation_status = 'confirmed'
         AND (oa.archived IS NULL OR oa.archived = 0)",
        5244, $agent->id
    ));
    echo "<p>6️⃣ مع شرط الأرشفة: {$step6}</p>";

    // Check archived value specifically
    $archived_value = $wpdb->get_var($wpdb->prepare(
        "SELECT archived FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d AND agent_id = %d",
        5244, $agent->id
    ));
    echo "<p>📦 قيمة الأرشفة الفعلية: " . ($archived_value === null ? 'NULL' : $archived_value) . "</p>";

    // Test without archive condition
    $step7 = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking ot
         INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
         INNER JOIN {$wpdb->prefix}posts p ON (ot.order_id = p.ID AND p.post_type = 'shop_order')
         WHERE ot.order_id = %d AND oa.agent_id = %d AND oa.confirmation_status = 'confirmed'",
        5244, $agent->id
    ));
    echo "<p>7️⃣ بدون شرط الأرشفة: {$step7}</p>";

    // Check if order exists in tracking table
    echo "<h4>🔍 فحص جدول التتبع:</h4>";
    $tracking_exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
        5244
    ));
    echo "<p>📊 الطلب موجود في جدول التتبع: " . ($tracking_exists ? 'نعم' : 'لا') . "</p>";

    if (!$tracking_exists) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>❌ المشكلة الحقيقية!</h4>";
        echo "<p>الطلب غير موجود في جدول التتبع، لذلك لا يظهر في صفحة التتبع.</p>";
        echo "<p>صفحة التتبع تستخدم INNER JOIN مع جدول التتبع، مما يعني أنها تعرض فقط الطلبات التي لها سجل تتبع.</p>";
        echo "<a href='?create_tracking=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>➕ إنشاء سجل تتبع</a>";
        echo "</div>";
    }

    // Show unarchive button if order is archived
    if ($archived_value == 1) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>📦 الطلب مؤرشف</h4>";
        echo "<p>هذا الطلب موجود في الأرشيف، لذلك لا يظهر في قائمة التتبع النشطة.</p>";
        echo "<a href='?unarchive=1' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📤 إلغاء الأرشفة</a>";
        echo "</div>";
    }

    echo "</div>";
}

echo "<h2>🛠️ إجراءات الإصلاح:</h2>";

// Create tracking record
if (isset($_GET['create_tracking']) && $_GET['create_tracking'] == '1') {
    $result = $wpdb->insert(
        $wpdb->prefix . 'hozi_order_tracking',
        array(
            'order_id' => 5244,
            'agent_id' => $agent->id,
            'status' => 'pending',
            'notes' => 'تم إنشاء سجل التتبع تلقائياً',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%s', '%s', '%s')
    );

    if ($result !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>✅ تم إنشاء سجل التتبع بنجاح!</h3>";
        echo "<p>الطلب رقم 5244 أصبح الآن في نظام التتبع.</p>";
        echo "<a href='?'>🔄 تحديث الصفحة لرؤية النتائج</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>❌ فشل في إنشاء سجل التتبع!</h3>";
        echo "<p>خطأ: " . $wpdb->last_error . "</p>";
        echo "</div>";
    }
}

// Fix the archive issue
if (isset($_GET['unarchive']) && $_GET['unarchive'] == '1') {
    $result = $wpdb->update(
        $wpdb->prefix . 'hozi_order_assignments',
        array('archived' => 0),
        array('order_id' => 5244, 'agent_id' => $agent->id),
        array('%d'),
        array('%d', '%d')
    );

    if ($result !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>✅ تم إلغاء الأرشفة بنجاح!</h3>";
        echo "<p>الطلب رقم 5244 تم إعادته للقائمة النشطة.</p>";
        echo "<a href='?'>🔄 تحديث الصفحة لرؤية النتائج</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>❌ فشل في إلغاء الأرشفة!</h3>";
        echo "</div>";
    }
}

if ($assignment && $tracking) {
    if ($assignment->agent_id != $agent->id) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>⚠️ مشكلة في تخصيص الوكيل:</h3>";
        echo "<p>الطلب مخصص للوكيل #{$assignment->agent_id} لكن المستخدم الحالي هو الوكيل #{$agent->id}</p>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='fix_agent' value='1'>";
        echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 15px; border: none; border-radius: 4px;'>🔧 إصلاح تخصيص الوكيل</button>";
        echo "</form>";
        echo "</div>";
    }

    if ($assignment->confirmation_status != 'confirmed') {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>⚠️ مشكلة في حالة التأكيد:</h3>";
        echo "<p>حالة التأكيد: {$assignment->confirmation_status} (يجب أن تكون 'confirmed')</p>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='fix_confirmation' value='1'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 15px; border: none; border-radius: 4px;'>✅ إصلاح حالة التأكيد</button>";
        echo "</form>";
        echo "</div>";
    }
}

// Handle fixes
if (isset($_POST['fix_agent'])) {
    $result = $wpdb->update(
        $wpdb->prefix . 'hozi_order_assignments',
        array('agent_id' => $agent->id),
        array('order_id' => 5244),
        array('%d'),
        array('%d')
    );

    if ($result !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<p>✅ تم إصلاح تخصيص الوكيل بنجاح!</p>";
        echo "</div>";
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
    }
}

if (isset($_POST['fix_confirmation'])) {
    $result = $wpdb->update(
        $wpdb->prefix . 'hozi_order_assignments',
        array('confirmation_status' => 'confirmed'),
        array('order_id' => 5244),
        array('%s'),
        array('%d')
    );

    if ($result !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<p>✅ تم إصلاح حالة التأكيد بنجاح!</p>";
        echo "</div>";
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
    }
}

echo "<p><a href='?' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 تحديث التشخيص</a></p>";
echo "<p><a href='/wp-admin/admin.php?page=hozi-akadly-delivery-tracking' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 الذهاب لصفحة متابعة التوصيل</a></p>";
?>
