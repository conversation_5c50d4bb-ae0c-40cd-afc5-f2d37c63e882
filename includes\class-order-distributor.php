<?php
/**
 * Order distribution class
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Order_Distributor {

    /**
     * Agent Manager instance
     */
    private $agent_manager;

    /**
     * Constructor
     */
    public function __construct() {
        $this->agent_manager = new Hozi_Akadly_Agent_Manager();
    }

    /**
     * Assign order to agent
     */
    public function assign_order($order_id, $agent_id = null, $method = 'auto') {
        global $wpdb;

        // Check if order is already assigned
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
            $order_id
        ));

        if ($existing) {
            return false; // Order already assigned
        }

        // Get agent
        if (!$agent_id) {
            $distribution_method = get_option('hozi_akadly_distribution_method', 'round_robin');

            if ($distribution_method === 'round_robin') {
                $agent = $this->agent_manager->get_next_agent_for_round_robin();
                if (!$agent) {
                    return false; // No available agents
                }
                $agent_id = $agent->id;
            } else {
                return false; // Manual distribution, no agent specified
            }
        }

        // Verify agent exists and is active
        $agent = $this->agent_manager->get_agent($agent_id);
        if (!$agent || !$agent->is_active) {
            return false;
        }

        // Create assignment
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'order_id' => $order_id,
                'agent_id' => $agent_id,
                'assignment_method' => $method,
                'confirmation_status' => 'pending_confirmation',
                'assigned_at' => current_time('mysql')
            )
        );

        if ($result) {
            // Update agent stats
            $this->agent_manager->update_last_assigned($agent_id);
            $this->agent_manager->increment_current_orders($agent_id);

            // Update order status and add assignment note
            $order = wc_get_order($order_id);
            if ($order) {
                // 🎯 تطبيق التسلسل الجديد: الطلب الجديد يصبح قيد التنفيذ
                $this->update_woocommerce_order_status($order_id, 'pending_confirmation');

                // Add assignment note with agent info
                global $wpdb;
                $agent_info = $wpdb->get_row($wpdb->prepare(
                    "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                    $agent_id
                ));

                if ($agent_info) {
                    $assignment_note = sprintf(
                        '👤 تم تخصيص الطلب للوكيل: %s%s%s',
                        $agent_info->name,
                        "\n📋 طريقة التخصيص: " . ($method === 'auto' ? 'تلقائي (دوري)' : 'يدوي'),
                        "\n⏰ تاريخ التخصيص: " . current_time('Y/m/d H:i')
                    );
                    $order->add_order_note($assignment_note, 0); // 0 = private note
                }
            }

            // Log the assignment
            Hozi_Akadly_Database::log_action(
                $order_id,
                $agent_id,
                'assigned',
                null,
                'pending_confirmation',
                sprintf(__('تم تخصيص الطلب بطريقة: %s', 'hozi-akadly'), $method)
            );

            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Reassign order to different agent
     */
    public function reassign_order($order_id, $new_agent_id, $reason = '') {
        global $wpdb;

        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
            $order_id
        ));

        if (!$assignment) {
            return false;
        }

        $old_agent_id = $assignment->agent_id;

        // Update assignment
        $result = $wpdb->update(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'agent_id' => $new_agent_id,
                'assignment_method' => 'manual',
                'confirmation_status' => 'pending_confirmation',
                'assigned_at' => current_time('mysql'),
                'notes' => $reason
            ),
            array('order_id' => $order_id)
        );

        if ($result) {
            // Add reassignment note
            $order = wc_get_order($order_id);
            if ($order) {
                global $wpdb;

                // Get agent names
                $old_agent = $wpdb->get_row($wpdb->prepare(
                    "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                    $old_agent_id
                ));

                $new_agent = $wpdb->get_row($wpdb->prepare(
                    "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                    $new_agent_id
                ));

                if ($old_agent && $new_agent) {
                    $reassignment_note = sprintf(
                        '🔄 تم إعادة تخصيص الطلب%s%s%s%s',
                        "\n📤 من الوكيل: " . $old_agent->name,
                        "\n📥 إلى الوكيل: " . $new_agent->name,
                        $reason ? "\n📝 السبب: " . $reason : '',
                        "\n⏰ التاريخ: " . current_time('Y/m/d H:i')
                    );
                    $order->add_order_note($reassignment_note, 0); // 0 = private note
                }
            }

            // Update old agent stats
            $this->agent_manager->decrement_current_orders($old_agent_id);

            // Update new agent stats
            $this->agent_manager->update_last_assigned($new_agent_id);
            $this->agent_manager->increment_current_orders($new_agent_id);

            // Log the reassignment
            Hozi_Akadly_Database::log_action(
                $order_id,
                $new_agent_id,
                'reassigned',
                null,
                'pending_confirmation',
                sprintf(__('تم إعادة تخصيص الطلب من الوكيل %d. السبب: %s', 'hozi-akadly'), $old_agent_id, $reason)
            );

            return true;
        }

        return false;
    }

    /**
     * Update order confirmation status
     */
    public function update_confirmation_status($order_id, $status, $notes = '') {
        global $wpdb;

        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
            $order_id
        ));

        if (!$assignment) {
            return false;
        }

        $old_status = $assignment->confirmation_status;

        // 🎯 AUTO-ARCHIVE LOGIC: If order is already confirmed and being confirmed again
        if ($old_status === 'confirmed' && $status === 'confirmed') {
            // Archive the order instead of updating status
            $update_data = array(
                'confirmation_status' => 'akadly_completed',
                'archived' => 1,
                'archived_at' => current_time('mysql'),
                'notes' => $notes ? $notes . ' (مؤرشف تلقائياً - تأكيد ثاني)' : 'مؤرشف تلقائياً - تأكيد ثاني'
            );

            $result = $wpdb->update(
                $wpdb->prefix . 'hozi_order_assignments',
                $update_data,
                array('order_id' => $order_id)
            );

            if ($result) {
                // Update WooCommerce order status to completed
                $order = wc_get_order($order_id);
                if ($order) {
                    $order->update_status('completed', __('تم إكمال الطلب - أكدلي مكتمل', 'hozi-akadly'));

                    // Add archive note
                    $agent_info = $wpdb->get_row($wpdb->prepare(
                        "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                        $assignment->agent_id
                    ));

                    $archive_note = sprintf(
                        '📦 تم أرشفة الطلب تلقائياً - أكدلي مكتمل%s%s%s',
                        "\n👤 الوكيل: " . ($agent_info->name ?? 'غير محدد'),
                        $notes ? "\n📝 ملاحظة: " . $notes : '',
                        "\n⏰ تاريخ الأرشفة: " . current_time('Y/m/d H:i')
                    );
                    $order->add_order_note($archive_note, 0); // Private note
                }

                // Log the archive action
                Hozi_Akadly_Database::log_action(
                    $order_id,
                    $assignment->agent_id,
                    'auto_archived',
                    $old_status,
                    'akadly_completed',
                    'تم الأرشفة تلقائياً - تأكيد ثاني'
                );

                return true;
            }

            return false;
        }

        // Normal confirmation status update
        $update_data = array(
            'confirmation_status' => $status,
            'notes' => $notes
        );

        if (in_array($status, array('confirmed', 'rejected', 'no_answer'))) {
            $update_data['confirmed_at'] = current_time('mysql');
            // Only decrement for the actual agent, not when admin is acting
            if (!isset($_POST['admin_acting_as_agent'])) {
                $this->agent_manager->decrement_current_orders($assignment->agent_id);
            }
        }

        $result = $wpdb->update(
            $wpdb->prefix . 'hozi_order_assignments',
            $update_data,
            array('order_id' => $order_id)
        );

        if ($result) {
            // Update order status in WooCommerce
            $this->update_woocommerce_order_status($order_id, $status);

            // 🎯 AUTOMATIC TRACKING TRANSFER - CORE FIX
            if ($status === 'confirmed') {
                $this->auto_transfer_to_tracking($order_id, $assignment->agent_id);

                // 🚀 BACKUP TRANSFER: Schedule a delayed transfer as backup
                wp_schedule_single_event(time() + 10, 'hozi_akadly_backup_transfer', array($order_id, $assignment->agent_id));
            }

            // Add detailed order notes
            $this->add_confirmation_order_notes($order_id, $assignment->agent_id, $status, $notes);

            // Update agent stats
            Hozi_Akadly_Database::update_agent_stats($assignment->agent_id);

            // Log the status change
            Hozi_Akadly_Database::log_action(
                $order_id,
                $assignment->agent_id,
                'status_update',
                $old_status,
                $status,
                $notes
            );

            return true;
        }

        return false;
    }

    /**
     * Automatically transfer confirmed order to tracking system
     * 🎯 CORE FIX: This ensures confirmed orders immediately move to delivery tracking
     */
    private function auto_transfer_to_tracking($order_id, $agent_id) {
        global $wpdb;

        try {
            // 🔍 Debug logging
            error_log("Hozi Akadly: Starting auto_transfer_to_tracking for order {$order_id}, agent {$agent_id}");

            // 🛠️ CRITICAL FIX: Ensure tracking table exists
            $this->ensure_tracking_table_exists();

            // Check if order is already in tracking
            $existing_tracking = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $order_id
            ));

            if ($existing_tracking) {
                error_log("Hozi Akadly: Order {$order_id} already in tracking, skipping");
                return; // Already in tracking
            }

            error_log("Hozi Akadly: Order {$order_id} not in tracking, proceeding with insert");

            // Add to tracking system
            $result = $wpdb->insert(
                $wpdb->prefix . 'hozi_order_tracking',
                array(
                    'order_id' => $order_id,
                    'agent_id' => $agent_id,
                    'status' => 'out_for_delivery',
                    'previous_status' => null,
                    'reason_category' => 'auto_confirmed',
                    'reason_details' => '',
                    'notes' => 'تم النقل تلقائياً إلى متابعة التوصيل فور تأكيد الطلب',
                    'updated_by' => get_current_user_id() ?: 1,
                    'updated_at' => current_time('mysql'),
                    'created_at' => current_time('mysql')
                ),
                array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
            );

            if ($result) {
                error_log("Hozi Akadly: ✅ Successfully inserted order {$order_id} into tracking table with ID: " . $wpdb->insert_id);

                // 🎯 لا نغير حالة WooCommerce هنا - نتركها on-hold كما هو مطلوب
                // فقط نضيف ملاحظة أن الطلب تم نقله للتتبع
                $order = wc_get_order($order_id);
                if ($order) {
                    // Get agent name for note
                    $agent_info = $wpdb->get_row($wpdb->prepare(
                        "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                        $agent_id
                    ));

                    $order->add_order_note(
                        sprintf(
                            '🚀 تم نقل الطلب تلقائياً إلى نظام متابعة التوصيل%s%s',
                            "\n👤 الوكيل: " . ($agent_info->name ?? 'غير محدد'),
                            "\n📦 متاح الآن في: متابعة التوصيل"
                        ),
                        0 // Private note
                    );

                    error_log("Hozi Akadly: ✅ Added order note for order {$order_id}");
                } else {
                    error_log("Hozi Akadly: ❌ Failed to get WooCommerce order {$order_id}");
                }

                // Log the auto transfer
                if (class_exists('Hozi_Akadly_Database')) {
                    Hozi_Akadly_Database::log_action(
                        $order_id,
                        $agent_id,
                        'auto_tracking_transfer',
                        'confirmed',
                        'out_for_delivery',
                        'تم النقل تلقائياً إلى متابعة التوصيل فور التأكيد'
                    );
                    error_log("Hozi Akadly: ✅ Logged auto transfer action for order {$order_id}");
                }

                // Clear cache
                if (function_exists('wp_cache_delete')) {
                    wp_cache_delete('hozi_agent_confirmed_orders_' . $agent_id, 'hozi_akadly');
                    wp_cache_delete('hozi_agent_tracking_stats_' . $agent_id, 'hozi_akadly');
                    error_log("Hozi Akadly: ✅ Cleared cache for agent {$agent_id}");
                }

                error_log("Hozi Akadly: ✅ Auto transfer completed successfully for order {$order_id}");
            } else {
                error_log("Hozi Akadly: ❌ Failed to insert order {$order_id} into tracking table. Error: " . $wpdb->last_error);
                error_log("Hozi Akadly: ❌ SQL Query: " . $wpdb->last_query);
            }
        } catch (Exception $e) {
            // Log error but don't break the confirmation process
            error_log('Hozi Akadly Auto Transfer Error: ' . $e->getMessage());
        }
    }

    /**
     * Update WooCommerce order status based on confirmation status
     */
    private function update_woocommerce_order_status($order_id, $confirmation_status) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }

        $status_mapping = array(
            'confirmed' => 'on-hold', // 🎯 الطلب المؤكد يصبح قيد الانتظار
            'rejected' => 'cancelled',
            'no_answer' => 'on-hold',
            'callback_later' => 'on-hold',
            'pending_confirmation' => 'processing', // 🎯 الطلب الجديد يكون قيد التنفيذ
            'akadly_completed' => 'completed' // 🎯 حالة الأرشفة الجديدة
        );

        $wc_status = $status_mapping[$confirmation_status] ?? 'pending';

        $status_messages = array(
            'confirmed' => __('تم تأكيد الطلب - قيد الانتظار للمراجعة النهائية', 'hozi-akadly'), // 🎯 رسالة محدثة
            'rejected' => __('تم رفض الطلب من قبل العميل', 'hozi-akadly'),
            'no_answer' => __('العميل لم يرد على مكالمة التأكيد', 'hozi-akadly'),
            'callback_later' => __('طلب العميل إعادة الاتصال لاحقاً', 'hozi-akadly'),
            'pending_confirmation' => __('الطلب قيد التنفيذ - في انتظار تأكيد الوكيل', 'hozi-akadly'), // 🎯 رسالة محدثة
            'akadly_completed' => __('تم إكمال الطلب - أكدلي مكتمل', 'hozi-akadly') // 🎯 رسالة الأرشفة
        );

        $message = $status_messages[$confirmation_status] ?? '';

        return $order->update_status($wc_status, $message);
    }

    /**
     * Add detailed order notes when confirmation status changes
     */
    private function add_confirmation_order_notes($order_id, $agent_id, $status, $notes) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }

        global $wpdb;

        // Get agent info
        $agent_info = $wpdb->get_row($wpdb->prepare(
            "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
            $agent_id
        ));

        if (!$agent_info) {
            return false;
        }

        // Customer notes (visible to customer)
        $customer_messages = array(
            'confirmed' => 'تم تأكيد طلبكم وهو الآن في انتظار المراجعة النهائية', // 🎯 رسالة محدثة
            'rejected' => 'تم إلغاء الطلب بناءً على طلبكم',
            'no_answer' => 'لم نتمكن من الوصول إليكم، سنحاول الاتصال مرة أخرى',
            'callback_later' => 'سيتم الاتصال بكم في وقت لاحق حسب طلبكم',
            'pending_confirmation' => 'طلبكم قيد المعالجة وسيتم الاتصال بكم قريباً للتأكيد', // 🎯 رسالة جديدة
            'akadly_completed' => 'تم إكمال طلبكم بنجاح - شكراً لتسوقكم معنا' // 🎯 رسالة العميل للأرشفة
        );

        if (isset($customer_messages[$status])) {
            $order->add_order_note($customer_messages[$status], 1); // 1 = customer note
        }

        // Admin notes (private, for store management) - Skip for confirmed orders to avoid duplication
        if ($status !== 'confirmed') {
            $admin_messages = array(
                'rejected' => 'تم رفض الطلب من قبل الوكيل',
                'no_answer' => 'العميل لم يرد على مكالمة الوكيل',
                'callback_later' => 'طلب العميل إعادة الاتصال لاحقاً',
                'akadly_completed' => 'تم أرشفة الطلب تلقائياً - أكدلي مكتمل' // 🎯 رسالة الإدارة للأرشفة
            );

            if (isset($admin_messages[$status])) {
                $admin_note = sprintf(
                    '🎯 %s: %s%s%s',
                    $admin_messages[$status],
                    $agent_info->name,
                    $notes ? "\n📝 ملاحظة الوكيل: " . $notes : '',
                    "\n⏰ التاريخ: " . current_time('Y/m/d H:i')
                );
                $order->add_order_note($admin_note, 0); // 0 = private note
            }
        } else {
            // For confirmed orders, add a simple confirmation note only
            $order->add_order_note(
                sprintf(
                    '✅ تم تأكيد الطلب بواسطة: %s%s%s',
                    $agent_info->name,
                    $notes ? "\n📝 ملاحظة: " . $notes : '',
                    "\n⏰ " . current_time('Y/m/d H:i')
                ),
                0 // Private note
            );
        }

        return true;
    }

    /**
     * Ensure tracking table exists - CRITICAL FIX
     */
    private function ensure_tracking_table_exists() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'hozi_order_tracking';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            error_log("Hozi Akadly: Tracking table missing, creating it now...");

            // Create the table
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                order_id bigint(20) NOT NULL,
                agent_id int(11) NOT NULL,
                status varchar(50) NOT NULL,
                previous_status varchar(50) DEFAULT NULL,
                reason_category varchar(100) DEFAULT NULL,
                reason_details text DEFAULT NULL,
                notes text DEFAULT NULL,
                updated_by int(11) NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY order_id (order_id),
                KEY agent_id (agent_id),
                KEY status (status),
                KEY updated_at (updated_at)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);

            // Verify table was created
            $table_exists_after = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
            if ($table_exists_after) {
                error_log("Hozi Akadly: Tracking table created successfully");
            } else {
                error_log("Hozi Akadly: Failed to create tracking table");
            }
        }
    }

    /**
     * Get order assignment
     */
    public function get_order_assignment($order_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT a.*, ag.name as agent_name, ag.phone as agent_phone
             FROM {$wpdb->prefix}hozi_order_assignments a
             LEFT JOIN {$wpdb->prefix}hozi_agents ag ON a.agent_id = ag.id
             WHERE a.order_id = %d",
            $order_id
        ));
    }

    /**
     * Get agent assignments
     */
    public function get_agent_assignments($agent_id, $status = null, $limit = 20, $offset = 0) {
        global $wpdb;

        $where = $wpdb->prepare("WHERE a.agent_id = %d", $agent_id);

        if ($status) {
            $where .= $wpdb->prepare(" AND a.confirmation_status = %s", $status);
        }

        return $wpdb->get_results($wpdb->prepare(
            "SELECT a.*, p.post_date as order_date
             FROM {$wpdb->prefix}hozi_order_assignments a
             LEFT JOIN {$wpdb->posts} p ON a.order_id = p.ID
             $where
             ORDER BY a.assigned_at DESC
             LIMIT %d OFFSET %d",
            $limit,
            $offset
        ));
    }

    /**
     * Get pending assignments for agent
     */
    public function get_pending_assignments($agent_id, $limit = 20, $offset = 0) {
        return $this->get_agent_assignments($agent_id, 'pending_confirmation', $limit, $offset);
    }

    /**
     * Get count of pending assignments for agent
     */
    public function get_pending_assignments_count($agent_id) {
        global $wpdb;

        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*)
             FROM {$wpdb->prefix}hozi_order_assignments a
             WHERE a.agent_id = %d AND a.confirmation_status = %s",
            $agent_id,
            'pending_confirmation'
        ));
    }

    /**
     * Get count of agent assignments
     */
    public function get_agent_assignments_count($agent_id, $status = null) {
        global $wpdb;

        $where = $wpdb->prepare("WHERE a.agent_id = %d", $agent_id);

        if ($status) {
            $where .= $wpdb->prepare(" AND a.confirmation_status = %s", $status);
        }

        return $wpdb->get_var(
            "SELECT COUNT(*)
             FROM {$wpdb->prefix}hozi_order_assignments a
             $where"
        );
    }

    /**
     * Bulk assign orders
     */
    public function bulk_assign_orders($order_ids, $agent_id = null) {
        $results = array();

        foreach ($order_ids as $order_id) {
            $results[$order_id] = $this->assign_order($order_id, $agent_id, 'manual');
        }

        return $results;
    }

    /**
     * Get unassigned orders (HPOS Compatible)
     */
    public function get_unassigned_orders($limit = 50) {
        global $wpdb;

        $statuses_to_assign = get_option('hozi_akadly_order_statuses_to_assign', array('pending', 'processing'));

        // Use WooCommerce HPOS compatible method
        $orders = wc_get_orders(array(
            'status' => $statuses_to_assign,
            'limit' => $limit * 2, // Get more to filter out assigned ones
            'orderby' => 'date',
            'order' => 'DESC',
            'return' => 'ids'
        ));

        if (empty($orders)) {
            return array();
        }

        // Filter out already assigned orders
        $order_ids_placeholders = implode(',', array_fill(0, count($orders), '%d'));
        $assigned_orders = $wpdb->get_col($wpdb->prepare(
            "SELECT order_id FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id IN ($order_ids_placeholders)",
            $orders
        ));

        $unassigned_orders = array();
        $count = 0;

        foreach ($orders as $order_id) {
            if ($count >= $limit) break;

            if (!in_array($order_id, $assigned_orders)) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $unassigned_orders[] = (object) array(
                        'order_id' => $order_id,
                        'order_date' => $order->get_date_created()->date('Y-m-d H:i:s'),
                        'post_status' => 'wc-' . $order->get_status()
                    );
                    $count++;
                }
            }
        }

        return $unassigned_orders;
    }
}
