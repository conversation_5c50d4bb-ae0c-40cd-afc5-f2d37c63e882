<?php
/**
 * Check Current User and Agent Status
 * فحص المستخدم الحالي وحالة الوكيل
 */

// WordPress environment
require_once('wp-config.php');

if (!current_user_can('manage_options')) {
    die('غير مصرح لك بالوصول');
}

echo "<h1>👤 فحص المستخدم الحالي وحالة الوكيل</h1>";

global $wpdb;

// Get current user info
$current_user = wp_get_current_user();
echo "<h2>📋 معلومات المستخدم الحالي:</h2>";
echo "<p><strong>ID المستخدم:</strong> {$current_user->ID}</p>";
echo "<p><strong>اسم المستخدم:</strong> {$current_user->user_login}</p>";
echo "<p><strong>البريد الإلكتروني:</strong> {$current_user->user_email}</p>";
echo "<p><strong>الأدوار:</strong> " . implode(', ', $current_user->roles) . "</p>";

// Check if current user is an agent
$current_agent = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d",
    $current_user->ID
));

echo "<h2>🎯 حالة الوكيل:</h2>";
if ($current_agent) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ المستخدم الحالي هو وكيل!</h3>";
    echo "<p><strong>ID الوكيل:</strong> {$current_agent->id}</p>";
    echo "<p><strong>اسم الوكيل:</strong> {$current_agent->name}</p>";
    echo "<p><strong>الحالة:</strong> {$current_agent->status}</p>";
    echo "<p><strong>نشط:</strong> " . ($current_agent->is_active ? 'نعم' : 'لا') . "</p>";
    echo "</div>";
    
    // Check orders for this agent
    $agent_orders = $wpdb->get_results($wpdb->prepare(
        "SELECT
            oa.order_id,
            oa.confirmation_status,
            oa.confirmed_at,
            oa.archived,
            p.post_status
        FROM {$wpdb->prefix}hozi_order_assignments oa
        LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
        WHERE oa.agent_id = %d
        ORDER BY oa.assigned_at DESC
        LIMIT 10",
        $current_agent->id
    ));
    
    echo "<h3>📋 طلبات هذا الوكيل:</h3>";
    if ($agent_orders) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم الطلب</th><th>حالة التأكيد</th><th>تاريخ التأكيد</th><th>حالة WC</th><th>مؤرشف</th></tr>";
        
        foreach ($agent_orders as $order) {
            $confirmed_color = ($order->confirmation_status === 'confirmed') ? 'green' : 'orange';
            $archived_status = ($order->archived == 1) ? 'نعم' : 'لا';
            
            echo "<tr>";
            echo "<td>#{$order->order_id}</td>";
            echo "<td style='color: {$confirmed_color};'>{$order->confirmation_status}</td>";
            echo "<td>" . ($order->confirmed_at ? date('Y/m/d H:i', strtotime($order->confirmed_at)) : 'غير مؤكد') . "</td>";
            echo "<td>{$order->post_status}</td>";
            echo "<td>{$archived_status}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Count confirmed non-archived orders
        $confirmed_count = 0;
        foreach ($agent_orders as $order) {
            if ($order->confirmation_status === 'confirmed' && ($order->archived === null || $order->archived == 0)) {
                $confirmed_count++;
            }
        }
        
        echo "<div style='background: #cce5ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h4>📊 الملخص:</h4>";
        echo "<p><strong>إجمالي الطلبات:</strong> " . count($agent_orders) . "</p>";
        echo "<p><strong>الطلبات المؤكدة غير المؤرشفة:</strong> {$confirmed_count}</p>";
        echo "<p><strong>يجب أن تظهر في متابعة التوصيل:</strong> {$confirmed_count}</p>";
        echo "</div>";
        
    } else {
        echo "<p>لا توجد طلبات لهذا الوكيل.</p>";
    }
    
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>⚠️ المستخدم الحالي ليس وكيل!</h3>";
    echo "<p>هذا يفسر لماذا لا تظهر الطلبات في صفحة متابعة التوصيل.</p>";
    echo "</div>";
}

// Show all agents and their orders
echo "<h2>👥 جميع الوكلاء وطلباتهم:</h2>";

$all_agents = $wpdb->get_results("
    SELECT
        a.id,
        a.name,
        a.user_id,
        a.status,
        a.is_active,
        COUNT(oa.order_id) as total_orders,
        SUM(CASE WHEN oa.confirmation_status = 'confirmed' AND (oa.archived IS NULL OR oa.archived = 0) THEN 1 ELSE 0 END) as confirmed_orders
    FROM {$wpdb->prefix}hozi_agents a
    LEFT JOIN {$wpdb->prefix}hozi_order_assignments oa ON a.id = oa.agent_id
    GROUP BY a.id, a.name, a.user_id, a.status, a.is_active
    ORDER BY confirmed_orders DESC
");

if ($all_agents) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID الوكيل</th><th>اسم الوكيل</th><th>ID المستخدم</th><th>الحالة</th><th>نشط</th><th>إجمالي الطلبات</th><th>الطلبات المؤكدة</th><th>إجراء</th></tr>";
    
    foreach ($all_agents as $agent) {
        $status_color = ($agent->status === 'active') ? 'green' : 'red';
        $active_color = ($agent->is_active == 1) ? 'green' : 'red';
        $is_current = ($agent->user_id == $current_user->ID) ? ' (أنت)' : '';
        
        echo "<tr>";
        echo "<td>{$agent->id}</td>";
        echo "<td>{$agent->name}{$is_current}</td>";
        echo "<td>{$agent->user_id}</td>";
        echo "<td style='color: {$status_color};'>{$agent->status}</td>";
        echo "<td style='color: {$active_color};'>" . ($agent->is_active ? 'نعم' : 'لا') . "</td>";
        echo "<td>{$agent->total_orders}</td>";
        echo "<td><strong>{$agent->confirmed_orders}</strong></td>";
        echo "<td>";
        
        if ($agent->confirmed_orders > 0) {
            echo "<a href='?switch_to_agent={$agent->id}' style='background: #007cba; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>عرض طلباته</a>";
        } else {
            echo "<span style='color: #999;'>لا توجد طلبات</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا يوجد وكلاء.</p>";
}

// Handle agent switching for testing
if (isset($_GET['switch_to_agent'])) {
    $agent_id = intval($_GET['switch_to_agent']);
    $selected_agent = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
        $agent_id
    ));
    
    if ($selected_agent) {
        echo "<h2>🔍 عرض طلبات الوكيل: {$selected_agent->name}</h2>";
        
        $agent_orders = $wpdb->get_results($wpdb->prepare(
            "SELECT
                oa.order_id,
                oa.confirmation_status,
                oa.confirmed_at,
                oa.archived,
                p.post_status
            FROM {$wpdb->prefix}hozi_order_assignments oa
            LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
            WHERE oa.agent_id = %d
            AND oa.confirmation_status = 'confirmed'
            AND (oa.archived IS NULL OR oa.archived = 0)
            ORDER BY oa.confirmed_at DESC",
            $agent_id
        ));
        
        if ($agent_orders) {
            echo "<p><strong>الطلبات المؤكدة غير المؤرشفة:</strong> " . count($agent_orders) . "</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>حالة WC</th><th>في التتبع؟</th></tr>";
            
            foreach ($agent_orders as $order) {
                // Check if order is in tracking
                $in_tracking = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                    $order->order_id
                ));
                
                echo "<tr>";
                echo "<td>#{$order->order_id}</td>";
                echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
                echo "<td>{$order->post_status}</td>";
                echo "<td>" . ($in_tracking ? '✅ نعم' : '❌ لا') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h4>🎯 لعرض هذه الطلبات في متابعة التوصيل:</h4>";
            echo "<p>1. يجب تسجيل الدخول كمستخدم مرتبط بالوكيل {$selected_agent->name}</p>";
            echo "<p>2. أو تعديل صفحة متابعة التوصيل لتظهر طلبات جميع الوكلاء للمدراء</p>";
            echo "</div>";
            
        } else {
            echo "<p>لا توجد طلبات مؤكدة غير مؤرشفة لهذا الوكيل.</p>";
        }
    }
}

echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 صفحة متابعة التوصيل</a></p>";
echo "<p><a href='admin.php?page=hozi-akadly-agents' target='_blank' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>👥 إدارة الوكلاء</a></p>";
echo "<p><a href='debug-delivery-new.php' target='_blank' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 أداة التشخيص</a></p>";

?>
