<?php
/**
 * Order Tracker Class
 * Handles post-confirmation order tracking and status management
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Order_Tracker {

    /**
     * Constructor
     */
    public function __construct() {
        // Hook to monitor WooCommerce order status changes
        add_action('woocommerce_order_status_changed', array($this, 'handle_woocommerce_status_change'), 10, 4);
    }

    /**
     * Handle WooCommerce order status changes
     * This ensures orders automatically move to tracking when status changes to 'completed'
     */
    public function handle_woocommerce_status_change($order_id, $old_status, $new_status, $order) {
        // Only process if order status changes to 'completed'
        if ($new_status !== 'completed') {
            return;
        }

        global $wpdb;

        // Check if this order is confirmed by an agent but not yet in tracking
        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT oa.*, a.name as agent_name
             FROM {$wpdb->prefix}hozi_order_assignments oa
             LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
             WHERE oa.order_id = %d
             AND oa.confirmation_status = 'confirmed'
             AND (oa.archived IS NULL OR oa.archived = 0)",
            $order_id
        ));

        if (!$assignment) {
            return; // Order not confirmed by agent or already archived
        }

        // Check if order already has tracking status
        $existing_tracking = $this->get_latest_tracking_status($order_id);
        if ($existing_tracking) {
            return; // Already has tracking status
        }

        // Auto-add to tracking with 'out_for_delivery' status (not delivered yet)
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_tracking',
            array(
                'order_id' => $order_id,
                'agent_id' => $assignment->agent_id,
                'status' => 'out_for_delivery',
                'previous_status' => null,
                'reason_category' => 'auto_completed',
                'reason_details' => '',
                'notes' => 'تم إضافة الطلب تلقائياً للتتبع عند تغيير حالة WooCommerce إلى مكتمل - جاهز للتوصيل',
                'updated_by' => get_current_user_id() ?: 1,
                'updated_at' => current_time('mysql'),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );

        if ($result) {
            // Log the action
            if (class_exists('Hozi_Akadly_Database')) {
                Hozi_Akadly_Database::log_action(
                    $order_id,
                    $assignment->agent_id,
                    'auto_tracking_added',
                    'confirmed',
                    'delivered',
                    'تم إضافة الطلب تلقائياً للتتبع عند تغيير حالة WooCommerce إلى مكتمل'
                );
            }

            // Add order note
            $order->add_order_note(
                sprintf(
                    '🎯 تم إضافة الطلب تلقائياً لنظام تتبع أكدلي%s%s',
                    "\n👤 الوكيل: " . ($assignment->agent_name ?? 'غير محدد'),
                    "\n📊 الحالة: في الطريق للتوصيل (تلقائي)"
                ),
                0 // Private note
            );

            // Force refresh of agent tracking page data
            if (function_exists('wp_cache_delete')) {
                wp_cache_delete('hozi_agent_confirmed_orders_' . $assignment->agent_id, 'hozi_akadly');
                wp_cache_delete('hozi_agent_tracking_stats_' . $assignment->agent_id, 'hozi_akadly');
            }
        }
    }

    /**
     * Available tracking statuses
     */
    public static function get_tracking_statuses() {
        return array(
            // Success statuses
            'delivered' => array(
                'label' => __('تم التوصيل بنجاح', 'hozi-akadly'),
                'color' => '#4CAF50',
                'icon' => 'yes-alt',
                'type' => 'success',
                'category' => 'success'
            ),
            'delivered_partial' => array(
                'label' => __('تم التوصيل الجزئي', 'hozi-akadly'),
                'color' => '#8BC34A',
                'icon' => 'yes',
                'type' => 'success',
                'category' => 'success'
            ),
            'exchange_completed' => array(
                'label' => __('تم الاستبدال بنجاح', 'hozi-akadly'),
                'color' => '#4CAF50',
                'icon' => 'update-alt',
                'type' => 'success',
                'category' => 'success'
            ),

            // In progress statuses
            'out_for_delivery' => array(
                'label' => __('في الطريق للتوصيل', 'hozi-akadly'),
                'color' => '#2196F3',
                'icon' => 'car',
                'type' => 'info',
                'category' => 'progress'
            ),
            'exchange_requested' => array(
                'label' => __('طلب استبدال', 'hozi-akadly'),
                'color' => '#2196F3',
                'icon' => 'update',
                'type' => 'info',
                'category' => 'progress'
            ),

            // Postponed statuses
            'postponed_customer' => array(
                'label' => __('مؤجلة بطلب العميل', 'hozi-akadly'),
                'color' => '#FF9800',
                'icon' => 'clock',
                'type' => 'warning',
                'category' => 'postponed'
            ),
            'postponed_delivery' => array(
                'label' => __('مؤجلة لأسباب التوصيل', 'hozi-akadly'),
                'color' => '#FF9800',
                'icon' => 'clock',
                'type' => 'warning',
                'category' => 'postponed'
            ),
            'postponed_address' => array(
                'label' => __('مؤجلة لمشكلة في العنوان', 'hozi-akadly'),
                'color' => '#FF9800',
                'icon' => 'location-alt',
                'type' => 'warning',
                'category' => 'postponed'
            ),

            // Rejected statuses
            'rejected_customer' => array(
                'label' => __('رفض العميل للطلب', 'hozi-akadly'),
                'color' => '#f44336',
                'icon' => 'no',
                'type' => 'critical',
                'category' => 'rejected'
            ),
            'rejected_quality' => array(
                'label' => __('رفض لمشكلة في الجودة', 'hozi-akadly'),
                'color' => '#f44336',
                'icon' => 'no-alt',
                'type' => 'critical',
                'category' => 'rejected'
            ),
            'rejected_delivery' => array(
                'label' => __('رفض لمشاكل التوصيل', 'hozi-akadly'),
                'color' => '#f44336',
                'icon' => 'no',
                'type' => 'critical',
                'category' => 'rejected'
            ),
            'rejected_payment' => array(
                'label' => __('رفض لمشاكل الدفع', 'hozi-akadly'),
                'color' => '#9C27B0',
                'icon' => 'dismiss',
                'type' => 'critical',
                'category' => 'rejected'
            ),

            // Customer not found statuses
            'customer_not_found' => array(
                'label' => __('لم يتم العثور على العميل', 'hozi-akadly'),
                'color' => '#607D8B',
                'icon' => 'search',
                'type' => 'warning',
                'category' => 'not_found'
            ),
            'wrong_address' => array(
                'label' => __('عنوان خاطئ', 'hozi-akadly'),
                'color' => '#607D8B',
                'icon' => 'location',
                'type' => 'warning',
                'category' => 'not_found'
            ),
            'customer_unreachable' => array(
                'label' => __('العميل غير متاح', 'hozi-akadly'),
                'color' => '#607D8B',
                'icon' => 'phone',
                'type' => 'warning',
                'category' => 'not_found'
            )
        );
    }

    /**
     * Get reason categories for each status
     */
    public static function get_reason_categories() {
        return array(
            // Customer postponement reasons
            'postponed_customer' => array(
                'not_available' => __('العميل غير متواجد', 'hozi-akadly'),
                'reschedule_request' => __('طلب إعادة جدولة', 'hozi-akadly'),
                'family_emergency' => __('ظروف عائلية طارئة', 'hozi-akadly'),
                'travel' => __('العميل مسافر', 'hozi-akadly'),
                'work_schedule' => __('ظروف العمل', 'hozi-akadly'),
                'financial_issue' => __('مشكلة مالية مؤقتة', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),

            // Delivery postponement reasons
            'postponed_delivery' => array(
                'weather' => __('أحوال جوية سيئة', 'hozi-akadly'),
                'traffic' => __('ازدحام مروري', 'hozi-akadly'),
                'vehicle_issue' => __('مشكلة في المركبة', 'hozi-akadly'),
                'road_closure' => __('إغلاق طرق', 'hozi-akadly'),
                'high_demand' => __('ضغط في الطلبات', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),

            // Address postponement reasons
            'postponed_address' => array(
                'incomplete_address' => __('عنوان غير مكتمل', 'hozi-akadly'),
                'wrong_area' => __('منطقة خاطئة', 'hozi-akadly'),
                'no_landmarks' => __('لا توجد معالم واضحة', 'hozi-akadly'),
                'address_change' => __('العميل غير العنوان', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),
            'exchange_requested' => array(
                'size_issue' => __('مشكلة في المقاس', 'hozi-akadly'),
                'color_issue' => __('مشكلة في اللون', 'hozi-akadly'),
                'quality_issue' => __('مشكلة في الجودة', 'hozi-akadly'),
                'different_product' => __('منتج مختلف عن المطلوب', 'hozi-akadly'),
                'customer_preference' => __('تغيير رأي العميل', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),
            'rejected_customer' => array(
                'changed_mind' => __('غير رأيه', 'hozi-akadly'),
                'financial_issue' => __('مشكلة مالية', 'hozi-akadly'),
                'found_alternative' => __('وجد بديل أفضل', 'hozi-akadly'),
                'delivery_delay' => __('تأخر في التوصيل', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),
            'rejected_quality' => array(
                'damaged' => __('منتج تالف', 'hozi-akadly'),
                'defective' => __('عيب في التصنيع', 'hozi-akadly'),
                'not_as_described' => __('لا يطابق الوصف', 'hozi-akadly'),
                'poor_quality' => __('جودة رديئة', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),
            'rejected_delivery' => array(
                'late_delivery' => __('تأخر في التوصيل', 'hozi-akadly'),
                'wrong_address' => __('عنوان خاطئ', 'hozi-akadly'),
                'delivery_person_issue' => __('مشكلة مع المندوب', 'hozi-akadly'),
                'packaging_issue' => __('مشكلة في التغليف', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),
            'rejected_payment' => array(
                'payment_failed' => __('فشل في الدفع', 'hozi-akadly'),
                'card_declined' => __('رفض البطاقة', 'hozi-akadly'),
                'insufficient_funds' => __('رصيد غير كافي', 'hozi-akadly'),
                'payment_dispute' => __('نزاع على الدفع', 'hozi-akadly'),
                'cash_not_available' => __('لا يتوفر نقد', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),

            // Customer not found reasons
            'customer_not_found' => array(
                'phone_not_answering' => __('الهاتف لا يرد', 'hozi-akadly'),
                'wrong_phone' => __('رقم هاتف خاطئ', 'hozi-akadly'),
                'phone_switched_off' => __('الهاتف مغلق', 'hozi-akadly'),
                'no_response' => __('لا يوجد رد', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),

            'wrong_address' => array(
                'address_not_found' => __('العنوان غير موجود', 'hozi-akadly'),
                'incomplete_address' => __('عنوان غير مكتمل', 'hozi-akadly'),
                'moved_address' => __('العميل انتقل', 'hozi-akadly'),
                'wrong_building' => __('رقم مبنى خاطئ', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),

            'customer_unreachable' => array(
                'busy_schedule' => __('العميل مشغول', 'hozi-akadly'),
                'not_home' => __('غير موجود في المنزل', 'hozi-akadly'),
                'at_work' => __('في العمل', 'hozi-akadly'),
                'traveling' => __('مسافر', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            ),

            // Success reasons (for partial delivery)
            'delivered_partial' => array(
                'customer_request' => __('بطلب من العميل', 'hozi-akadly'),
                'stock_issue' => __('مشكلة في المخزون', 'hozi-akadly'),
                'size_unavailable' => __('المقاس غير متوفر', 'hozi-akadly'),
                'quality_issue' => __('مشكلة في جودة جزء من الطلب', 'hozi-akadly'),
                'other' => __('أسباب أخرى', 'hozi-akadly')
            )
        );
    }

    /**
     * Update order tracking status
     */
    public function update_tracking_status($order_id, $status, $reason_category = '', $reason_details = '', $notes = '') {
        global $wpdb;

        // Validate status
        $valid_statuses = array_keys(self::get_tracking_statuses());
        if (!in_array($status, $valid_statuses)) {
            return false;
        }

        // Get current user and agent info
        $current_user_id = get_current_user_id();
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $current_agent = $agent_manager->get_current_agent();

        if (!$current_agent) {
            return false;
        }

        // Get previous status
        $previous_status = $this->get_latest_tracking_status($order_id);

        // Insert tracking record
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_tracking',
            array(
                'order_id' => $order_id,
                'agent_id' => $current_agent->id,
                'status' => $status,
                'previous_status' => $previous_status ? $previous_status->status : null,
                'reason_category' => $reason_category,
                'reason_details' => $reason_details,
                'notes' => $notes,
                'updated_by' => $current_user_id,
                'updated_at' => current_time('mysql'),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );

        if ($result) {
            // Update WooCommerce order status if needed
            $this->sync_with_woocommerce($order_id, $status);

            // Send notifications for critical statuses
            $this->send_status_notifications($order_id, $status, $reason_category, $notes);

            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Get latest tracking status for an order
     */
    public function get_latest_tracking_status($order_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_tracking
             WHERE order_id = %d
             ORDER BY updated_at DESC
             LIMIT 1",
            $order_id
        ));
    }

    /**
     * Get tracking history for an order
     */
    public function get_tracking_history($order_id) {
        global $wpdb;

        return $wpdb->get_results($wpdb->prepare(
            "SELECT ot.*, a.name as agent_name, u.display_name as updated_by_name
             FROM {$wpdb->prefix}hozi_order_tracking ot
             LEFT JOIN {$wpdb->prefix}hozi_agents a ON ot.agent_id = a.id
             LEFT JOIN {$wpdb->prefix}users u ON ot.updated_by = u.ID
             WHERE ot.order_id = %d
             ORDER BY ot.updated_at DESC",
            $order_id
        ));
    }

    /**
     * Sync status with WooCommerce
     */
    private function sync_with_woocommerce($order_id, $status) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }

        // Map tracking statuses to WooCommerce statuses
        $status_mapping = array(
            'delivered' => 'completed',
            'rejected_customer' => 'cancelled',
            'rejected_quality' => 'cancelled',
            'rejected_delivery' => 'cancelled',
            'cancelled_payment' => 'cancelled'
        );

        if (isset($status_mapping[$status])) {
            $order->update_status($status_mapping[$status],
                sprintf(__('تم تحديث الحالة تلقائياً من نظام أكدلي إلى: %s', 'hozi-akadly'),
                self::get_tracking_statuses()[$status]['label'])
            );
        }

        return true;
    }

    /**
     * Send notifications for critical statuses
     */
    private function send_status_notifications($order_id, $status, $reason_category, $notes) {
        $critical_statuses = array(
            'rejected_customer',
            'rejected_quality',
            'rejected_delivery',
            'rejected_payment',
            'exchange_requested',
            'customer_not_found',
            'wrong_address',
            'customer_unreachable'
        );

        if (!in_array($status, $critical_statuses)) {
            return;
        }

        // Get admin email
        $admin_email = get_option('admin_email');
        $status_info = self::get_tracking_statuses()[$status];

        $subject = sprintf(__('[أكدلي] تحديث حالة حرجة للطلب #%d', 'hozi-akadly'), $order_id);

        $message = sprintf(
            __('تم تحديث حالة الطلب #%d إلى: %s', 'hozi-akadly') . "\n\n" .
            __('السبب: %s', 'hozi-akadly') . "\n" .
            __('ملاحظات: %s', 'hozi-akadly') . "\n\n" .
            __('يرجى المراجعة والمتابعة.', 'hozi-akadly'),
            $order_id,
            $status_info['label'],
            $reason_category,
            $notes
        );

        wp_mail($admin_email, $subject, $message);
    }

    /**
     * Get orders requiring tracking (confirmed orders without tracking status)
     */
    public function get_orders_for_tracking($limit = 50, $offset = 0) {
        global $wpdb;

        return $wpdb->get_results($wpdb->prepare(
            "SELECT oa.order_id, oa.agent_id, oa.confirmed_at, a.name as agent_name,
                    p.post_date as order_date
             FROM {$wpdb->prefix}hozi_order_assignments oa
             LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
             LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
             LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
             WHERE oa.confirmation_status = 'confirmed'
             AND ot.order_id IS NULL
             AND p.post_type = 'shop_order'
             ORDER BY oa.confirmed_at DESC
             LIMIT %d OFFSET %d",
            $limit, $offset
        ));
    }

    /**
     * Get tracking statistics
     */
    public function get_tracking_statistics($start_date = null, $end_date = null) {
        global $wpdb;

        if (!$start_date) {
            $start_date = date('Y-m-d', strtotime('-30 days'));
        }
        if (!$end_date) {
            $end_date = date('Y-m-d');
        }

        $stats = $wpdb->get_results($wpdb->prepare(
            "SELECT status, COUNT(*) as count
             FROM {$wpdb->prefix}hozi_order_tracking
             WHERE DATE(updated_at) BETWEEN %s AND %s
             GROUP BY status
             ORDER BY count DESC",
            $start_date, $end_date
        ));

        $formatted_stats = array();
        $total = 0;

        foreach ($stats as $stat) {
            $status_info = self::get_tracking_statuses()[$stat->status] ?? null;
            if ($status_info) {
                $formatted_stats[] = array(
                    'status' => $stat->status,
                    'label' => $status_info['label'],
                    'count' => $stat->count,
                    'color' => $status_info['color'],
                    'type' => $status_info['type']
                );
                $total += $stat->count;
            }
        }

        // Calculate percentages
        foreach ($formatted_stats as &$stat) {
            $stat['percentage'] = $total > 0 ? round(($stat['count'] / $total) * 100, 2) : 0;
        }

        return array(
            'stats' => $formatted_stats,
            'total' => $total,
            'period' => array(
                'start' => $start_date,
                'end' => $end_date
            )
        );
    }
}
