<?php
/**
 * Check Error Logs - Debug Tool
 * فحص سجلات الأخطاء - أداة التشخيص
 */

// WordPress environment
require_once('wp-config.php');
require_once(ABSPATH . 'wp-admin/includes/admin.php');

// Security check
if (!current_user_can('manage_options')) {
    die('غير مصرح لك بالوصول');
}

echo "<h1>📋 فحص سجلات الأخطاء - أكدلي</h1>";

// Get WordPress debug log
$debug_log_path = WP_CONTENT_DIR . '/debug.log';

if (file_exists($debug_log_path)) {
    echo "<h2>📄 سجل الأخطاء WordPress:</h2>";
    
    // Read last 50 lines
    $lines = file($debug_log_path);
    $recent_lines = array_slice($lines, -50);
    
    // Filter for Hozi Akadly related logs
    $hozi_logs = array_filter($recent_lines, function($line) {
        return strpos($line, 'Hozi Akadly') !== false;
    });
    
    if (!empty($hozi_logs)) {
        echo "<h3>🎯 سجلات أكدلي الأخيرة:</h3>";
        echo "<div style='background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;'>";
        foreach ($hozi_logs as $log) {
            $log = htmlspecialchars($log);
            if (strpos($log, 'Error') !== false) {
                echo "<div style='color: red;'>{$log}</div>";
            } elseif (strpos($log, 'Successfully') !== false) {
                echo "<div style='color: green;'>{$log}</div>";
            } else {
                echo "<div>{$log}</div>";
            }
        }
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد سجلات أكدلي في آخر 50 سطر</p>";
    }
    
    echo "<h3>📊 إحصائيات السجل:</h3>";
    echo "<p><strong>حجم الملف:</strong> " . number_format(filesize($debug_log_path) / 1024, 2) . " KB</p>";
    echo "<p><strong>آخر تعديل:</strong> " . date('Y-m-d H:i:s', filemtime($debug_log_path)) . "</p>";
    
} else {
    echo "<p style='color: red;'>❌ ملف debug.log غير موجود</p>";
    echo "<p>تأكد من تفعيل WP_DEBUG_LOG في wp-config.php</p>";
}

// Check PHP error log
$php_error_log = ini_get('error_log');
if ($php_error_log && file_exists($php_error_log)) {
    echo "<h2>🐘 سجل أخطاء PHP:</h2>";
    echo "<p><strong>مسار الملف:</strong> {$php_error_log}</p>";
    
    if (filesize($php_error_log) > 0) {
        $php_lines = file($php_error_log);
        $recent_php_lines = array_slice($php_lines, -20);
        
        $hozi_php_logs = array_filter($recent_php_lines, function($line) {
            return strpos($line, 'Hozi') !== false || strpos($line, 'akadly') !== false;
        });
        
        if (!empty($hozi_php_logs)) {
            echo "<h3>🎯 سجلات PHP المتعلقة بأكدلي:</h3>";
            echo "<div style='background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;'>";
            foreach ($hozi_php_logs as $log) {
                echo "<div style='color: red;'>" . htmlspecialchars($log) . "</div>";
            }
            echo "</div>";
        } else {
            echo "<p>✅ لا توجد أخطاء PHP متعلقة بأكدلي</p>";
        }
    } else {
        echo "<p>✅ ملف سجل PHP فارغ</p>";
    }
}

// Test database connection and table
echo "<h2>🗄️ فحص قاعدة البيانات:</h2>";

global $wpdb;

// Check if tracking table exists
$table_name = $wpdb->prefix . 'hozi_order_tracking';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "<p style='color: green;'>✅ جدول hozi_order_tracking موجود</p>";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo "<h3>📋 هيكل الجدول:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check recent records
    $recent_records = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC LIMIT 5");
    echo "<h3>📊 آخر 5 سجلات:</h3>";
    if ($recent_records) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Order ID</th><th>Agent ID</th><th>Status</th><th>Created At</th></tr>";
        foreach ($recent_records as $record) {
            echo "<tr>";
            echo "<td>{$record->id}</td>";
            echo "<td>{$record->order_id}</td>";
            echo "<td>{$record->agent_id}</td>";
            echo "<td>{$record->status}</td>";
            echo "<td>{$record->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد سجلات في جدول التتبع</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ جدول hozi_order_tracking غير موجود</p>";
    
    // Try to create the table
    echo "<h3>🔧 محاولة إنشاء الجدول:</h3>";
    
    $sql = "CREATE TABLE $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        order_id bigint(20) NOT NULL,
        agent_id int(11) NOT NULL,
        status varchar(50) NOT NULL,
        previous_status varchar(50) DEFAULT NULL,
        reason_category varchar(100) DEFAULT NULL,
        reason_details text DEFAULT NULL,
        notes text DEFAULT NULL,
        updated_by int(11) NOT NULL,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY order_id (order_id),
        KEY agent_id (agent_id),
        KEY status (status),
        KEY updated_at (updated_at)
    ) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    $result = $wpdb->query($sql);
    
    if ($result !== false) {
        echo "<p style='color: green;'>✅ تم إنشاء الجدول بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء الجدول: " . $wpdb->last_error . "</p>";
    }
}

echo "<hr>";
echo "<h2>🧪 اختبار سريع:</h2>";
echo "<p><a href='test-confirmation-flow.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🧪 اختبار تدفق التأكيد</a></p>";
echo "<p><a href='debug-delivery-new.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 أداة التشخيص الرئيسية</a></p>";

// Clear logs button
if (isset($_GET['clear_logs']) && $_GET['clear_logs'] === '1') {
    if (file_exists($debug_log_path)) {
        file_put_contents($debug_log_path, '');
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ تم مسح سجل الأخطاء";
        echo "</div>";
    }
}

echo "<p><a href='?clear_logs=1' style='background: #dc3545; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;' onclick='return confirm(\"هل تريد مسح سجل الأخطاء؟\")'>🗑️ مسح سجل الأخطاء</a></p>";
?>
