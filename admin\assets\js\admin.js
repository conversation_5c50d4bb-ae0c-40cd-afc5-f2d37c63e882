/**
 * <PERSON><PERSON>dly Admin JavaScript
 */

(function($) {
    'use strict';

    // Global variables
    var hoziAdmin = {
        init: function() {
            this.bindEvents();
            this.initTooltips();
            this.initFormValidation();
            this.initAutoRefresh();
        },

        bindEvents: function() {
            // Quick assignment
            $(document).on('change', '.quick-agent-select', this.handleQuickAssignment);
            
            // Bulk actions
            $(document).on('click', '#select-all-orders', this.selectAllOrders);
            $(document).on('click', '#deselect-all-orders', this.deselectAllOrders);
            
            // Confirmation forms
            $(document).on('submit', '.hozi-confirmation-form', this.handleConfirmationSubmit);
            
            // Agent status toggle
            $(document).on('click', '.hozi-toggle-agent-status', this.toggleAgentStatus);
            
            // Real-time search
            $(document).on('keyup', '.hozi-search-input', this.handleSearch);
            
            // Phone number formatting
            $(document).on('input', 'input[type="tel"]', this.formatPhoneNumber);
        },

        handleQuickAssignment: function(e) {
            var $select = $(this);
            var orderId = $select.data('order-id');
            var agentId = $select.val();
            
            if (!agentId || !orderId) return;
            
            var agentName = $select.find('option:selected').text();
            var confirmMessage = hoziAjax.strings.confirm_assignment || 'هل تريد تخصيص هذا الطلب للوكيل المحدد؟';
            
            if (!confirm(confirmMessage.replace('%s', agentName))) {
                $select.val('');
                return;
            }
            
            hoziAdmin.showLoading($select.closest('tr'));
            
            $.ajax({
                url: hoziAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'hozi_assign_order',
                    order_id: orderId,
                    agent_id: agentId,
                    nonce: hoziAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        hoziAdmin.showMessage(response.data.message, 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        hoziAdmin.showMessage(response.data.message, 'error');
                        $select.val('');
                    }
                },
                error: function() {
                    hoziAdmin.showMessage(hoziAjax.strings.error, 'error');
                    $select.val('');
                },
                complete: function() {
                    hoziAdmin.hideLoading($select.closest('tr'));
                }
            });
        },

        selectAllOrders: function(e) {
            e.preventDefault();
            $('.order-checkbox').prop('checked', true);
            $('#cb-select-all').prop('checked', true);
            hoziAdmin.updateBulkActionButton();
        },

        deselectAllOrders: function(e) {
            e.preventDefault();
            $('.order-checkbox').prop('checked', false);
            $('#cb-select-all').prop('checked', false);
            hoziAdmin.updateBulkActionButton();
        },

        updateBulkActionButton: function() {
            var selectedCount = $('.order-checkbox:checked').length;
            var hasAgent = $('#agent_id').val() !== '';
            var $button = $('#assign-selected');
            
            $button.prop('disabled', !(selectedCount > 0 && hasAgent));
            
            if (selectedCount > 0) {
                $button.text($button.data('text-template').replace('%d', selectedCount));
            } else {
                $button.text($button.data('text-default'));
            }
        },

        handleConfirmationSubmit: function(e) {
            var $form = $(this);
            var $button = $(e.originalEvent.submitter);
            var status = $button.val();
            var orderId = $form.find('input[name="order_id"]').val();
            
            var confirmMessages = {
                'confirmed': 'هل أنت متأكد من تأكيد هذا الطلب؟',
                'rejected': 'هل أنت متأكد من رفض هذا الطلب؟',
                'no_answer': 'هل تأكدت من عدم رد العميل؟',
                'callback_later': 'هل تريد تأجيل هذا الطلب لإعادة الاتصال لاحقاً؟'
            };
            
            if (confirmMessages[status] && !confirm(confirmMessages[status])) {
                e.preventDefault();
                return false;
            }
            
            // Add loading state
            hoziAdmin.showLoading($form.closest('.hozi-order-card'));
            
            // AJAX submission
            e.preventDefault();
            
            $.ajax({
                url: hoziAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'hozi_update_confirmation',
                    order_id: orderId,
                    status: status,
                    notes: $form.find('textarea[name="notes"]').val(),
                    nonce: hoziAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        hoziAdmin.showMessage(response.data.message, 'success');
                        
                        // Remove the order card with animation
                        $form.closest('.hozi-order-card').fadeOut(500, function() {
                            $(this).remove();
                            
                            // Check if no more orders
                            if ($('.hozi-order-card').length === 0) {
                                $('.hozi-orders-grid').html(
                                    '<div class="hozi-no-orders">' +
                                    '<div class="hozi-no-orders-icon"><span class="dashicons dashicons-cart"></span></div>' +
                                    '<h3>لا توجد طلبات مخصصة لك حالياً</h3>' +
                                    '<p>ستظهر الطلبات الجديدة هنا عند تخصيصها لك</p>' +
                                    '</div>'
                                );
                            }
                        });
                        
                        // Update stats
                        hoziAdmin.updateAgentStats();
                        
                    } else {
                        hoziAdmin.showMessage(response.data.message, 'error');
                    }
                },
                error: function() {
                    hoziAdmin.showMessage(hoziAjax.strings.error, 'error');
                },
                complete: function() {
                    hoziAdmin.hideLoading($form.closest('.hozi-order-card'));
                }
            });
        },

        toggleAgentStatus: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var agentId = $button.data('agent-id');
            var agentName = $button.data('agent-name');
            
            if (!confirm('هل تريد تغيير حالة الوكيل "' + agentName + '"؟')) {
                return;
            }
            
            hoziAdmin.showLoading($button.closest('tr'));
            
            $.ajax({
                url: hoziAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'hozi_toggle_agent_status',
                    agent_id: agentId,
                    nonce: hoziAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        hoziAdmin.showMessage(response.data.message, 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        hoziAdmin.showMessage(response.data.message, 'error');
                    }
                },
                error: function() {
                    hoziAdmin.showMessage(hoziAjax.strings.error, 'error');
                },
                complete: function() {
                    hoziAdmin.hideLoading($button.closest('tr'));
                }
            });
        },

        handleSearch: function() {
            var searchTerm = $(this).val().toLowerCase();
            var $items = $('.hozi-searchable-item');
            
            $items.each(function() {
                var $item = $(this);
                var text = $item.text().toLowerCase();
                
                if (text.indexOf(searchTerm) !== -1) {
                    $item.show().addClass('hozi-fade-in');
                } else {
                    $item.hide().removeClass('hozi-fade-in');
                }
            });
        },

        formatPhoneNumber: function() {
            var value = $(this).val().replace(/\D/g, '');
            var formattedValue = value;
            
            // Format for Saudi numbers
            if (value.startsWith('966')) {
                formattedValue = value.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
            } else if (value.startsWith('05')) {
                formattedValue = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
            }
            
            $(this).val(formattedValue);
        },

        updateAgentStats: function() {
            // Update agent statistics in real-time
            $.ajax({
                url: hoziAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'hozi_get_agent_stats',
                    nonce: hoziAjax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.stats) {
                        var stats = response.data.stats;
                        $('.hozi-stat-card').each(function() {
                            var $card = $(this);
                            var statType = $card.data('stat-type');
                            if (stats[statType] !== undefined) {
                                $card.find('h3').text(stats[statType]);
                            }
                        });
                    }
                }
            });
        },

        showLoading: function($element) {
            $element.addClass('hozi-loading');
        },

        hideLoading: function($element) {
            $element.removeClass('hozi-loading');
        },

        showMessage: function(message, type) {
            var $message = $('<div class="hozi-message ' + type + '">' +
                '<span class="dashicons dashicons-' + (type === 'success' ? 'yes-alt' : 'warning') + '"></span>' +
                '<span>' + message + '</span>' +
                '</div>');
            
            $message.prependTo('.wrap').addClass('hozi-fade-in');
            
            setTimeout(function() {
                $message.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 5000);
        },

        initTooltips: function() {
            // Initialize tooltips for elements with data-tooltip attribute
            $('[data-tooltip]').addClass('hozi-tooltip');
        },

        initFormValidation: function() {
            // Real-time form validation
            $('form').on('submit', function(e) {
                var $form = $(this);
                var isValid = true;
                
                $form.find('[required]').each(function() {
                    var $field = $(this);
                    if (!$field.val().trim()) {
                        $field.addClass('error');
                        isValid = false;
                    } else {
                        $field.removeClass('error');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    hoziAdmin.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                }
            });
        },

        initAutoRefresh: function() {
            // Auto-refresh for agent dashboard every 30 seconds
            if ($('.hozi-orders-section').length > 0) {
                setInterval(function() {
                    hoziAdmin.updateAgentStats();
                }, 30000);
            }
        },

        // Utility functions
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        formatCurrency: function(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        },

        formatDate: function(dateString) {
            return new Intl.DateTimeFormat('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }).format(new Date(dateString));
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        hoziAdmin.init();
        
        // Add RTL support
        if ($('html').attr('dir') === 'rtl') {
            $('body').addClass('rtl');
        }
        
        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + S to save
            if ((e.ctrlKey || e.metaKey) && e.which === 83) {
                e.preventDefault();
                $('form input[type="submit"], form button[type="submit"]').first().click();
            }
            
            // Escape to close modals/cancel actions
            if (e.which === 27) {
                $('.hozi-modal, .hozi-overlay').fadeOut();
            }
        });
    });

    // Expose hoziAdmin globally for external use
    window.hoziAdmin = hoziAdmin;

})(jQuery);
