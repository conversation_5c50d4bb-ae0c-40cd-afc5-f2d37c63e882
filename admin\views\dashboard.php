<?php
/**
 * Dashboard view
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap hozi-dashboard-wrap">
    <!-- Beautiful Header -->
    <div class="hozi-dashboard-header">
        <div class="hozi-header-content">
            <div class="hozi-header-icon">
                <span class="dashicons dashicons-admin-network"></span>
            </div>
            <div class="hozi-header-text">
                <h1 class="hozi-main-title">أكدلي - Akadly</h1>
                <p class="hozi-subtitle">نظام إدارة وتأكيد الطلبات المتقدم</p>
            </div>
        </div>
        <div class="hozi-header-decoration">
            <div class="hozi-decoration-circle"></div>
            <div class="hozi-decoration-circle"></div>
            <div class="hozi-decoration-circle"></div>
        </div>
    </div>

    <?php if ($total_agents == 0) : ?>
    <!-- Welcome Message for New Users -->
    <div class="notice notice-info" style="border-right: 4px solid #00a0d2; padding: 20px; margin: 20px 0;">
        <h2 style="margin-top: 0;">🎉 مرحباً بك في Hozi Akadly!</h2>
        <p><strong>تم تفعيل النظام بنجاح!</strong> للبدء في استخدام نظام تأكيد الطلبات، اتبع الخطوات التالية:</p>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;">
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-right: 3px solid #28a745;">
                <h4 style="margin-top: 0; color: #28a745;">1️⃣ إضافة وكيل تأكيد</h4>
                <p>أنشئ حساب وكيل جديد لتأكيد الطلبات</p>
                <a href="<?php echo admin_url('admin.php?page=hozi-akadly-agents'); ?>" class="button button-primary">
                    👥 إضافة وكيل
                </a>
            </div>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-right: 3px solid #007cba;">
                <h4 style="margin-top: 0; color: #007cba;">2️⃣ إعداد التوزيع</h4>
                <p>اختر طريقة توزيع الطلبات على الوكلاء</p>
                <a href="<?php echo admin_url('admin.php?page=hozi-akadly-settings'); ?>" class="button">
                    ⚙️ الإعدادات
                </a>
            </div>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-right: 3px solid #dc3545;">
                <h4 style="margin-top: 0; color: #dc3545;">3️⃣ اختبار النظام</h4>
                <p>أنشئ طلب تجريبي لاختبار النظام</p>
                <a href="<?php echo admin_url('post-new.php?post_type=shop_order'); ?>" class="button">
                    🛒 طلب جديد
                </a>
            </div>
        </div>

        <p><em>💡 <strong>نصيحة:</strong> بعد إضافة وكيل، ستظهر الطلبات الجديدة تلقائياً في لوحة تحكم الوكيل لتأكيدها.</em></p>
    </div>
    <?php endif; ?>

    <div class="hozi-dashboard">
        <!-- Beautiful Statistics Cards -->
        <div class="hozi-stats-container">
            <div class="hozi-stats-grid">
                <div class="hozi-stat-card hozi-stat-total" data-count="<?php echo esc_attr($total_agents); ?>">
                    <div class="hozi-stat-background">
                        <div class="hozi-stat-icon">
                            <i class="dashicons dashicons-groups"></i>
                        </div>
                    </div>
                    <div class="hozi-stat-content">
                        <div class="hozi-stat-number" data-target="<?php echo esc_attr($total_agents); ?>">0</div>
                        <div class="hozi-stat-label">إجمالي الوكلاء</div>
                        <div class="hozi-stat-description">العدد الكلي للوكلاء المسجلين</div>
                    </div>
                    <div class="hozi-stat-decoration">
                        <div class="hozi-pulse-dot"></div>
                    </div>
                </div>

                <div class="hozi-stat-card hozi-stat-active" data-count="<?php echo esc_attr($active_agents); ?>">
                    <div class="hozi-stat-background">
                        <div class="hozi-stat-icon">
                            <i class="dashicons dashicons-admin-users"></i>
                        </div>
                    </div>
                    <div class="hozi-stat-content">
                        <div class="hozi-stat-number" data-target="<?php echo esc_attr($active_agents); ?>">0</div>
                        <div class="hozi-stat-label">الوكلاء النشطون</div>
                        <div class="hozi-stat-description">الوكلاء المتاحون حالياً</div>
                    </div>
                    <div class="hozi-stat-decoration">
                        <div class="hozi-pulse-dot"></div>
                    </div>
                </div>

                <div class="hozi-stat-card hozi-stat-pending" data-count="<?php echo esc_attr($unassigned_orders); ?>">
                    <div class="hozi-stat-background">
                        <div class="hozi-stat-icon">
                            <i class="dashicons dashicons-cart"></i>
                        </div>
                    </div>
                    <div class="hozi-stat-content">
                        <div class="hozi-stat-number" data-target="<?php echo esc_attr($unassigned_orders); ?>">0</div>
                        <div class="hozi-stat-label">طلبات غير مخصصة</div>
                        <div class="hozi-stat-description">في انتظار التخصيص</div>
                    </div>
                    <div class="hozi-stat-decoration">
                        <div class="hozi-pulse-dot"></div>
                    </div>
                </div>

                <div class="hozi-stat-card hozi-stat-confirmed" data-count="<?php
                    global $wpdb;
                    $confirmed_today = $wpdb->get_var(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments
                         WHERE confirmation_status = 'confirmed'
                         AND DATE(confirmed_at) = CURDATE()"
                    );
                    echo esc_attr($confirmed_today ?: 0);
                    ?>">
                    <div class="hozi-stat-background">
                        <div class="hozi-stat-icon">
                            <i class="dashicons dashicons-yes-alt"></i>
                        </div>
                    </div>
                    <div class="hozi-stat-content">
                        <div class="hozi-stat-number" data-target="<?php echo esc_attr($confirmed_today ?: 0); ?>">0</div>
                        <div class="hozi-stat-label">طلبات مؤكدة اليوم</div>
                        <div class="hozi-stat-description">تم تأكيدها اليوم</div>
                    </div>
                    <div class="hozi-stat-decoration">
                        <div class="hozi-pulse-dot"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Beautiful Quick Actions -->
        <div class="hozi-quick-actions-section">
            <div class="hozi-section-header">
                <h2 class="hozi-section-title">
                    <span class="hozi-title-icon">⚡</span>
                    إجراءات سريعة
                </h2>
                <p class="hozi-section-subtitle">الوصول السريع للوظائف الأساسية</p>
            </div>

            <div class="hozi-actions-container">
                <div class="hozi-actions-grid">
                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-agents'); ?>" class="hozi-action-card hozi-action-agents">
                        <div class="hozi-action-icon">
                            <i class="dashicons dashicons-admin-users"></i>
                        </div>
                        <div class="hozi-action-content">
                            <h3>إدارة الوكلاء</h3>
                            <p>إضافة وتعديل وإدارة الوكلاء</p>
                        </div>
                        <div class="hozi-action-arrow">
                            <i class="dashicons dashicons-arrow-left-alt"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-assignments'); ?>" class="hozi-action-card hozi-action-distribute">
                        <div class="hozi-action-icon">
                            <i class="dashicons dashicons-randomize"></i>
                        </div>
                        <div class="hozi-action-content">
                            <h3>توزيع الطلبات</h3>
                            <p>تخصيص الطلبات للوكلاء</p>
                        </div>
                        <div class="hozi-action-arrow">
                            <i class="dashicons dashicons-arrow-left-alt"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-analytics'); ?>" class="hozi-action-card hozi-action-analytics">
                        <div class="hozi-action-icon">
                            <i class="dashicons dashicons-chart-bar"></i>
                        </div>
                        <div class="hozi-action-content">
                            <h3>التحليلات</h3>
                            <p>تقارير وإحصائيات مفصلة</p>
                        </div>
                        <div class="hozi-action-arrow">
                            <i class="dashicons dashicons-arrow-left-alt"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('edit.php?post_type=shop_order'); ?>" class="hozi-action-card hozi-action-orders">
                        <div class="hozi-action-icon">
                            <i class="dashicons dashicons-cart"></i>
                        </div>
                        <div class="hozi-action-content">
                            <h3>عرض الطلبات</h3>
                            <p>جميع طلبات المتجر</p>
                        </div>
                        <div class="hozi-action-arrow">
                            <i class="dashicons dashicons-arrow-left-alt"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-settings'); ?>" class="hozi-action-card hozi-action-settings">
                        <div class="hozi-action-icon">
                            <i class="dashicons dashicons-admin-settings"></i>
                        </div>
                        <div class="hozi-action-content">
                            <h3>الإعدادات</h3>
                            <p>تخصيص إعدادات النظام</p>
                        </div>
                        <div class="hozi-action-arrow">
                            <i class="dashicons dashicons-arrow-left-alt"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-user-guide'); ?>" class="hozi-action-card hozi-action-guide">
                        <div class="hozi-action-icon">
                            <i class="dashicons dashicons-book"></i>
                        </div>
                        <div class="hozi-action-content">
                            <h3>دليل الاستخدام</h3>
                            <p>شرح شامل لجميع الميزات</p>
                        </div>
                        <div class="hozi-action-arrow">
                            <i class="dashicons dashicons-arrow-left-alt"></i>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Beautiful Recent Activity -->
        <div class="hozi-recent-activity-section">
            <div class="hozi-section-header">
                <h2 class="hozi-section-title">
                    <span class="hozi-title-icon">📊</span>
                    النشاط الأخير
                </h2>
                <p class="hozi-section-subtitle">آخر العمليات والتحديثات في النظام</p>
            </div>

            <div class="hozi-activity-container">
                <?php
                global $wpdb;
                $recent_logs = $wpdb->get_results(
                    "SELECT l.*, a.name as agent_name, p.post_title as order_title
                     FROM {$wpdb->prefix}hozi_confirmation_logs l
                     LEFT JOIN {$wpdb->prefix}hozi_agents a ON l.agent_id = a.id
                     LEFT JOIN {$wpdb->posts} p ON l.order_id = p.ID
                     ORDER BY l.created_at DESC
                     LIMIT 8"
                );

                if ($recent_logs) {
                    foreach ($recent_logs as $index => $log) {
                        $action_text = '';
                        $action_icon = '';
                        $action_class = '';

                        switch ($log->action) {
                            case 'assigned':
                                $action_text = 'تم تخصيص الطلب';
                                $action_icon = 'dashicons-admin-users';
                                $action_class = 'assigned';
                                break;
                            case 'confirmed':
                                $action_text = 'تم تحديث الحالة إلى: مؤكد';
                                $action_icon = 'dashicons-yes-alt';
                                $action_class = 'confirmed';
                                break;
                            case 'status_change':
                                $action_text = sprintf('تم تحديث الحالة إلى: %s', $log->new_status);
                                $action_icon = 'dashicons-update';
                                $action_class = 'status_change';
                                break;
                            case 'no_answer':
                                $action_text = 'تم تحديث الحالة إلى: لم يرد';
                                $action_icon = 'dashicons-phone';
                                $action_class = 'no_answer';
                                break;
                            case 'reassigned':
                                $action_text = 'تم إعادة تخصيص الطلب';
                                $action_icon = 'dashicons-randomize';
                                $action_class = 'reassigned';
                                break;
                            default:
                                $action_text = $log->action;
                                $action_icon = 'dashicons-admin-generic';
                                $action_class = 'default';
                        }
                        ?>
                        <div class="hozi-activity-item hozi-activity-<?php echo esc_attr($action_class); ?>" style="animation-delay: <?php echo ($index * 0.1); ?>s">
                            <div class="hozi-activity-timeline">
                                <div class="hozi-activity-dot"></div>
                                <?php if ($index < count($recent_logs) - 1): ?>
                                <div class="hozi-activity-line"></div>
                                <?php endif; ?>
                            </div>
                            <div class="hozi-activity-card">
                                <div class="hozi-activity-icon">
                                    <i class="dashicons <?php echo esc_attr($action_icon); ?>"></i>
                                </div>
                                <div class="hozi-activity-content">
                                    <div class="hozi-activity-header">
                                        <strong class="hozi-agent-name"><?php echo esc_html($log->agent_name ?: 'النظام'); ?></strong>
                                        <span class="hozi-activity-time">
                                            <?php echo human_time_diff(strtotime($log->created_at), current_time('timestamp')); ?> منذ
                                        </span>
                                    </div>
                                    <div class="hozi-activity-description">
                                        <?php echo esc_html($action_text); ?>
                                        <a href="<?php echo admin_url('post.php?post=' . $log->order_id . '&action=edit'); ?>" class="hozi-order-link">
                                            #<?php echo esc_html($log->order_id); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div class="hozi-no-activity">
                        <div class="hozi-no-activity-icon">
                            <i class="dashicons dashicons-clock"></i>
                        </div>
                        <h3>لا يوجد نشاط حتى الآن</h3>
                        <p>ستظهر هنا آخر العمليات والتحديثات عند بدء استخدام النظام</p>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>

        <!-- Distribution Settings Summary -->
        <div class="hozi-settings-summary">
            <h2><?php _e('ملخص الإعدادات', 'hozi-akadly'); ?></h2>
            <div class="hozi-settings-grid">
                <div class="hozi-setting-item">
                    <label><?php _e('طريقة التوزيع:', 'hozi-akadly'); ?></label>
                    <span class="hozi-setting-value">
                        <?php
                        $method = get_option('hozi_akadly_distribution_method', 'round_robin');
                        echo $method === 'round_robin' ? __('دوري تلقائي', 'hozi-akadly') : __('يدوي', 'hozi-akadly');
                        ?>
                    </span>
                </div>

                <div class="hozi-setting-item">
                    <label><?php _e('تخصيص تلقائي للطلبات الجديدة:', 'hozi-akadly'); ?></label>
                    <span class="hozi-setting-value">
                        <?php
                        $auto_assign = get_option('hozi_akadly_auto_assign_new_orders', 'yes');
                        echo $auto_assign === 'yes' ? __('مفعل', 'hozi-akadly') : __('معطل', 'hozi-akadly');
                        ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* ===== MAIN DASHBOARD STYLES ===== */
.hozi-dashboard-wrap {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: -20px -20px -20px -2px;
    padding: 0;
}

/* ===== BEAUTIFUL HEADER ===== */
.hozi-dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;
}

.hozi-header-content {
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.hozi-header-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 25px;
    backdrop-filter: blur(10px);
    animation: float 3s ease-in-out infinite;
}

.hozi-header-icon .dashicons {
    font-size: 40px;
    color: white;
}

.hozi-main-title {
    font-size: 3em;
    margin: 0;
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hozi-subtitle {
    font-size: 1.2em;
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-weight: 300;
}

.hozi-header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.hozi-decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 4s ease-in-out infinite;
}

.hozi-decoration-circle:nth-child(1) {
    width: 100px;
    height: 100px;
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.hozi-decoration-circle:nth-child(2) {
    width: 60px;
    height: 60px;
    top: 60%;
    right: 20%;
    animation-delay: 1s;
}

.hozi-decoration-circle:nth-child(3) {
    width: 80px;
    height: 80px;
    top: 40%;
    right: 5%;
    animation-delay: 2s;
}

/* ===== DASHBOARD CONTENT ===== */
.hozi-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px 30px;
}

/* ===== BEAUTIFUL STATISTICS CARDS ===== */
.hozi-stats-container {
    margin-bottom: 40px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.hozi-stat-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.hozi-stat-card:nth-child(1) { animation-delay: 0.1s; }
.hozi-stat-card:nth-child(2) { animation-delay: 0.2s; }
.hozi-stat-card:nth-child(3) { animation-delay: 0.3s; }
.hozi-stat-card:nth-child(4) { animation-delay: 0.4s; }

.hozi-stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.hozi-stat-background {
    position: absolute;
    top: -50px;
    left: -50px;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    opacity: 0.1;
}

.hozi-stat-total .hozi-stat-background { background: #667eea; }
.hozi-stat-active .hozi-stat-background { background: #4CAF50; }
.hozi-stat-pending .hozi-stat-background { background: #FF9800; }
.hozi-stat-confirmed .hozi-stat-background { background: #2196F3; }

.hozi-stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.hozi-stat-total .hozi-stat-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.hozi-stat-active .hozi-stat-icon { background: linear-gradient(135deg, #4CAF50, #45a049); }
.hozi-stat-pending .hozi-stat-icon { background: linear-gradient(135deg, #FF9800, #f57c00); }
.hozi-stat-confirmed .hozi-stat-icon { background: linear-gradient(135deg, #2196F3, #1976d2); }

.hozi-stat-icon i {
    font-size: 30px;
    color: white;
}

.hozi-stat-number {
    font-size: 3em;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #333, #666);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hozi-stat-label {
    font-size: 1.2em;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.hozi-stat-description {
    font-size: 0.9em;
    color: #666;
    opacity: 0.8;
}

.hozi-pulse-dot {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.hozi-stat-total .hozi-pulse-dot { background: #667eea; }
.hozi-stat-active .hozi-pulse-dot { background: #4CAF50; }
.hozi-stat-pending .hozi-pulse-dot { background: #FF9800; }
.hozi-stat-confirmed .hozi-pulse-dot { background: #2196F3; }

/* ===== SECTION HEADERS ===== */
.hozi-section-header {
    text-align: center;
    margin-bottom: 30px;
}

.hozi-section-title {
    font-size: 2.2em;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.hozi-title-icon {
    font-size: 1.2em;
    animation: bounce 2s infinite;
}

.hozi-section-subtitle {
    font-size: 1.1em;
    color: #666;
    margin: 0;
    opacity: 0.8;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes countUp {
    from { opacity: 0; transform: scale(0.5); }
    to { opacity: 1; transform: scale(1); }
}

/* ===== QUICK ACTIONS SECTION ===== */
.hozi-quick-actions-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.hozi-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.hozi-action-card {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.hozi-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.hozi-action-card:hover::before {
    left: 100%;
}

.hozi-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.hozi-action-agents:hover { border-color: #667eea; }
.hozi-action-distribute:hover { border-color: #4CAF50; }
.hozi-action-analytics:hover { border-color: #FF9800; }
.hozi-action-orders:hover { border-color: #2196F3; }
.hozi-action-settings:hover { border-color: #9C27B0; }
.hozi-action-guide:hover { border-color: #FF5722; }

.hozi-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    flex-shrink: 0;
}

.hozi-action-agents .hozi-action-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.hozi-action-distribute .hozi-action-icon { background: linear-gradient(135deg, #4CAF50, #45a049); }
.hozi-action-analytics .hozi-action-icon { background: linear-gradient(135deg, #FF9800, #f57c00); }
.hozi-action-orders .hozi-action-icon { background: linear-gradient(135deg, #2196F3, #1976d2); }
.hozi-action-settings .hozi-action-icon { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }
.hozi-action-guide .hozi-action-icon { background: linear-gradient(135deg, #FF5722, #d84315); }

.hozi-action-icon i {
    font-size: 24px;
    color: white;
}

.hozi-action-content {
    flex: 1;
}

.hozi-action-content h3 {
    margin: 0 0 8px 0;
    font-size: 1.3em;
    font-weight: 600;
    color: #333;
}

.hozi-action-content p {
    margin: 0;
    color: #666;
    font-size: 0.95em;
}

.hozi-action-arrow {
    margin-right: 15px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.hozi-action-card:hover .hozi-action-arrow {
    opacity: 1;
    transform: translateX(-5px);
}

.hozi-action-arrow i {
    font-size: 20px;
    color: #667eea;
}

/* ===== RECENT ACTIVITY SECTION ===== */
.hozi-recent-activity-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.hozi-activity-container {
    max-height: 600px;
    overflow-y: auto;
}

.hozi-activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    opacity: 0;
    animation: slideInLeft 0.6s ease forwards;
}

.hozi-activity-item:nth-child(1) { animation-delay: 0.1s; }
.hozi-activity-item:nth-child(2) { animation-delay: 0.2s; }
.hozi-activity-item:nth-child(3) { animation-delay: 0.3s; }
.hozi-activity-item:nth-child(4) { animation-delay: 0.4s; }
.hozi-activity-item:nth-child(5) { animation-delay: 0.5s; }

.hozi-activity-timeline {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 20px;
}

.hozi-activity-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
    position: relative;
    z-index: 2;
}

.hozi-activity-assigned .hozi-activity-dot { background: #667eea; }
.hozi-activity-confirmed .hozi-activity-dot { background: #4CAF50; }
.hozi-activity-status_change .hozi-activity-dot { background: #FF9800; }
.hozi-activity-no_answer .hozi-activity-dot { background: #f44336; }
.hozi-activity-reassigned .hozi-activity-dot { background: #9C27B0; }

.hozi-activity-line {
    width: 2px;
    height: 40px;
    background: #e0e0e0;
    margin-top: 5px;
}

.hozi-activity-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    flex: 1;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.hozi-activity-card:hover {
    background: #f0f8ff;
    transform: translateX(5px);
}

.hozi-activity-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    flex-shrink: 0;
}

.hozi-activity-assigned .hozi-activity-icon { background: rgba(102, 126, 234, 0.1); color: #667eea; }
.hozi-activity-confirmed .hozi-activity-icon { background: rgba(76, 175, 80, 0.1); color: #4CAF50; }
.hozi-activity-status_change .hozi-activity-icon { background: rgba(255, 152, 0, 0.1); color: #FF9800; }
.hozi-activity-no_answer .hozi-activity-icon { background: rgba(244, 67, 54, 0.1); color: #f44336; }
.hozi-activity-reassigned .hozi-activity-icon { background: rgba(156, 39, 176, 0.1); color: #9C27B0; }

.hozi-activity-content {
    flex: 1;
}

.hozi-activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.hozi-agent-name {
    color: #333;
    font-weight: 600;
}

.hozi-activity-time {
    font-size: 0.85em;
    color: #999;
    background: rgba(0,0,0,0.05);
    padding: 4px 8px;
    border-radius: 12px;
}

.hozi-activity-description {
    color: #666;
    font-size: 0.95em;
    line-height: 1.4;
}

.hozi-order-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    padding: 2px 6px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.hozi-order-link:hover {
    background: rgba(102, 126, 234, 0.2);
    color: #5a67d8;
}

.hozi-no-activity {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.hozi-no-activity-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.hozi-no-activity-icon i {
    font-size: 35px;
    color: #ccc;
}

.hozi-no-activity h3 {
    margin: 0 0 10px 0;
    color: #999;
}

.hozi-no-activity p {
    margin: 0;
    opacity: 0.8;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>

<script>
// Counter Animation
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.hozi-stat-number');

    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const step = target / (duration / 16); // 60fps
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 16);
    };

    // Intersection Observer for animation trigger
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target.querySelector('.hozi-stat-number');
                if (counter && !counter.classList.contains('animated')) {
                    counter.classList.add('animated');
                    animateCounter(counter);
                }
            }
        });
    });

    document.querySelectorAll('.hozi-stat-card').forEach(card => {
        observer.observe(card);
    });
});
</script>
