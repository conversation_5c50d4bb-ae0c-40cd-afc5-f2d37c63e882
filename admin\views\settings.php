<?php
/**
 * Settings view
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$distribution_method = get_option('hozi_akadly_distribution_method', 'round_robin');
$auto_assign_new_orders = get_option('hozi_akadly_auto_assign_new_orders', 'yes');
$order_statuses_to_assign = get_option('hozi_akadly_order_statuses_to_assign', array('pending', 'processing'));
?>

<div class="wrap">
    <h1><?php _e('إعدادات Hozi Akadly', 'hozi-akadly'); ?></h1>

    <form method="post" action="">
        <?php wp_nonce_field('hozi_settings'); ?>
        <input type="hidden" name="save_settings" value="1">

        <table class="form-table">
            <!-- Distribution Method -->
            <tr>
                <th scope="row">
                    <label for="distribution_method"><?php _e('طريقة توزيع الطلبات', 'hozi-akadly'); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text">
                            <span><?php _e('طريقة توزيع الطلبات', 'hozi-akadly'); ?></span>
                        </legend>
                        
                        <label>
                            <input type="radio" name="distribution_method" value="round_robin" 
                                   <?php checked($distribution_method, 'round_robin'); ?>>
                            <strong><?php _e('توزيع دوري تلقائي (Round Robin)', 'hozi-akadly'); ?></strong>
                            <p class="description">
                                <?php _e('يتم توزيع الطلبات تلقائياً بالتساوي بين الوكلاء النشطين حسب آخر تخصيص', 'hozi-akadly'); ?>
                            </p>
                        </label>
                        <br><br>
                        
                        <label>
                            <input type="radio" name="distribution_method" value="manual" 
                                   <?php checked($distribution_method, 'manual'); ?>>
                            <strong><?php _e('توزيع يدوي', 'hozi-akadly'); ?></strong>
                            <p class="description">
                                <?php _e('يتم تخصيص الطلبات يدوياً من قبل المشرف فقط', 'hozi-akadly'); ?>
                            </p>
                        </label>
                    </fieldset>
                </td>
            </tr>

            <!-- Auto Assignment -->
            <tr>
                <th scope="row">
                    <label for="auto_assign_new_orders"><?php _e('التخصيص التلقائي للطلبات الجديدة', 'hozi-akadly'); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text">
                            <span><?php _e('التخصيص التلقائي للطلبات الجديدة', 'hozi-akadly'); ?></span>
                        </legend>
                        
                        <label>
                            <input type="radio" name="auto_assign_new_orders" value="yes" 
                                   <?php checked($auto_assign_new_orders, 'yes'); ?>>
                            <?php _e('مفعل', 'hozi-akadly'); ?>
                        </label>
                        <br>
                        
                        <label>
                            <input type="radio" name="auto_assign_new_orders" value="no" 
                                   <?php checked($auto_assign_new_orders, 'no'); ?>>
                            <?php _e('معطل', 'hozi-akadly'); ?>
                        </label>
                        
                        <p class="description">
                            <?php _e('عند التفعيل، سيتم تخصيص الطلبات الجديدة تلقائياً للوكلاء حسب طريقة التوزيع المحددة', 'hozi-akadly'); ?>
                        </p>
                    </fieldset>
                </td>
            </tr>

            <!-- Order Statuses to Assign -->
            <tr>
                <th scope="row">
                    <label for="order_statuses_to_assign"><?php _e('حالات الطلبات المراد تخصيصها', 'hozi-akadly'); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text">
                            <span><?php _e('حالات الطلبات المراد تخصيصها', 'hozi-akadly'); ?></span>
                        </legend>
                        
                        <?php
                        $order_statuses = wc_get_order_statuses();
                        foreach ($order_statuses as $status_key => $status_name) :
                            $status_key = str_replace('wc-', '', $status_key);
                        ?>
                            <label>
                                <input type="checkbox" name="order_statuses_to_assign[]" value="<?php echo esc_attr($status_key); ?>"
                                       <?php checked(in_array($status_key, $order_statuses_to_assign)); ?>>
                                <?php echo esc_html($status_name); ?>
                            </label>
                            <br>
                        <?php endforeach; ?>
                        
                        <p class="description">
                            <?php _e('حدد حالات الطلبات التي تريد تخصيصها للوكلاء تلقائياً', 'hozi-akadly'); ?>
                        </p>
                    </fieldset>
                </td>
            </tr>
        </table>

        <!-- Delivery Tracking Settings Section -->
        <h2><?php _e('إعدادات متابعة التوصيل', 'hozi-akadly'); ?></h2>

        <div class="hozi-settings-info">
            <div class="hozi-info-box">
                <h4>📋 كيف يعمل نظام متابعة التوصيل؟</h4>
                <ul>
                    <li><strong>الوكيل المخصص فقط:</strong> كل وكيل يرى الطلبات التي أكدها فقط</li>
                    <li><strong>أي وكيل:</strong> جميع الوكلاء يمكنهم رؤية ومتابعة جميع الطلبات المؤكدة</li>
                    <li><strong>المشرفين فقط:</strong> الوكلاء لا يمكنهم الوصول، المشرفين فقط يتابعون التوصيل</li>
                    <li><strong>إسناد مخصص:</strong> تحديد شخص واحد مسؤول عن متابعة جميع عمليات التوصيل</li>
                </ul>
                <p><strong>ملاحظة:</strong> عند تعطيل النظام، لن يظهر تبويب "متابعة التوصيل" للوكلاء.</p>
            </div>
        </div>

        <table class="form-table">
            <!-- Enable/Disable Delivery Tracking -->
            <tr>
                <th scope="row">
                    <label for="enable_delivery_tracking"><?php _e('تفعيل نظام متابعة التوصيل', 'hozi-akadly'); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text">
                            <span><?php _e('تفعيل نظام متابعة التوصيل', 'hozi-akadly'); ?></span>
                        </legend>

                        <label>
                            <input type="radio" name="enable_delivery_tracking" value="yes"
                                   <?php checked(get_option('hozi_akadly_enable_delivery_tracking', 'yes'), 'yes'); ?>>
                            <?php _e('مفعل', 'hozi-akadly'); ?>
                        </label>
                        <br>

                        <label>
                            <input type="radio" name="enable_delivery_tracking" value="no"
                                   <?php checked(get_option('hozi_akadly_enable_delivery_tracking', 'yes'), 'no'); ?>>
                            <?php _e('معطل', 'hozi-akadly'); ?>
                        </label>

                        <p class="description">
                            <?php _e('عند التعطيل، لن يتمكن الوكلاء من الوصول إلى صفحة متابعة التوصيل', 'hozi-akadly'); ?>
                        </p>
                    </fieldset>
                </td>
            </tr>

            <!-- Delivery Tracking Access Control -->
            <tr class="delivery-tracking-option">
                <th scope="row">
                    <label for="delivery_tracking_access"><?php _e('صلاحيات الوصول لمتابعة التوصيل', 'hozi-akadly'); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text">
                            <span><?php _e('صلاحيات الوصول لمتابعة التوصيل', 'hozi-akadly'); ?></span>
                        </legend>

                        <label>
                            <input type="radio" name="delivery_tracking_access" value="assigned_agent_only"
                                   <?php checked(get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only'), 'assigned_agent_only'); ?>>
                            <strong><?php _e('الوكيل المخصص فقط', 'hozi-akadly'); ?></strong>
                            <p class="description">
                                <?php _e('يمكن للوكيل متابعة الطلبات التي أكدها فقط', 'hozi-akadly'); ?>
                            </p>
                        </label>
                        <br><br>

                        <label>
                            <input type="radio" name="delivery_tracking_access" value="any_agent"
                                   <?php checked(get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only'), 'any_agent'); ?>>
                            <strong><?php _e('أي وكيل', 'hozi-akadly'); ?></strong>
                            <p class="description">
                                <?php _e('يمكن لأي وكيل متابعة جميع الطلبات المؤكدة', 'hozi-akadly'); ?>
                            </p>
                        </label>
                        <br><br>

                        <label>
                            <input type="radio" name="delivery_tracking_access" value="managers_only"
                                   <?php checked(get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only'), 'managers_only'); ?>>
                            <strong><?php _e('المشرفين فقط', 'hozi-akadly'); ?></strong>
                            <p class="description">
                                <?php _e('يمكن للمشرفين فقط متابعة التوصيل، الوكلاء لا يمكنهم الوصول', 'hozi-akadly'); ?>
                            </p>
                        </label>
                        <br><br>

                        <label>
                            <input type="radio" name="delivery_tracking_access" value="custom_assignment"
                                   <?php checked(get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only'), 'custom_assignment'); ?>>
                            <strong><?php _e('إسناد مخصص', 'hozi-akadly'); ?></strong>
                            <p class="description">
                                <?php _e('إسناد متابعة التوصيل لوكيل أو مشرف محدد', 'hozi-akadly'); ?>
                            </p>
                        </label>
                    </fieldset>
                </td>
            </tr>

            <!-- Custom Assignment for Delivery Tracking -->
            <tr class="delivery-tracking-option custom-assignment-option" style="display: none;">
                <th scope="row">
                    <label for="delivery_tracking_assigned_user"><?php _e('المسؤول عن متابعة التوصيل', 'hozi-akadly'); ?></label>
                </th>
                <td>
                    <select name="delivery_tracking_assigned_user" id="delivery_tracking_assigned_user">
                        <option value=""><?php _e('اختر مستخدم', 'hozi-akadly'); ?></option>

                        <!-- Managers -->
                        <optgroup label="<?php _e('المشرفين', 'hozi-akadly'); ?>">
                            <?php
                            $managers = get_users(array('role' => 'administrator'));
                            $current_assigned = get_option('hozi_akadly_delivery_tracking_assigned_user', '');
                            foreach ($managers as $manager) {
                                echo '<option value="manager_' . esc_attr($manager->ID) . '" ' . selected($current_assigned, 'manager_' . $manager->ID, false) . '>' .
                                     esc_html($manager->display_name . ' (مشرف)') . '</option>';
                            }
                            ?>
                        </optgroup>

                        <!-- Agents -->
                        <optgroup label="<?php _e('الوكلاء', 'hozi-akadly'); ?>">
                            <?php
                            $agent_manager = new Hozi_Akadly_Agent_Manager();
                            $agents = $agent_manager->get_agents(true); // Active agents only
                            foreach ($agents as $agent) {
                                echo '<option value="agent_' . esc_attr($agent->user_id) . '" ' . selected($current_assigned, 'agent_' . $agent->user_id, false) . '>' .
                                     esc_html($agent->name . ' (وكيل)') . '</option>';
                            }
                            ?>
                        </optgroup>
                    </select>
                    <p class="description">
                        <?php _e('حدد المستخدم الذي سيكون مسؤولاً عن متابعة جميع عمليات التوصيل', 'hozi-akadly'); ?>
                    </p>
                </td>
            </tr>
        </table>

        <!-- Advanced Settings Section -->
        <h2><?php _e('الإعدادات المتقدمة', 'hozi-akadly'); ?></h2>
        
        <table class="form-table">
            <!-- Database Status -->
            <tr>
                <th scope="row"><?php _e('حالة قاعدة البيانات', 'hozi-akadly'); ?></th>
                <td>
                    <?php if (Hozi_Akadly_Database::tables_exist()) : ?>
                        <span class="hozi-status-indicator success">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php _e('الجداول موجودة ومتاحة', 'hozi-akadly'); ?>
                        </span>
                    <?php else : ?>
                        <span class="hozi-status-indicator error">
                            <span class="dashicons dashicons-warning"></span>
                            <?php _e('الجداول غير موجودة', 'hozi-akadly'); ?>
                        </span>
                        <p class="description">
                            <?php _e('قم بإلغاء تفعيل الإضافة وإعادة تفعيلها لإنشاء الجداول', 'hozi-akadly'); ?>
                        </p>
                    <?php endif; ?>
                </td>
            </tr>

            <!-- Plugin Version -->
            <tr>
                <th scope="row"><?php _e('إصدار الإضافة', 'hozi-akadly'); ?></th>
                <td>
                    <code><?php echo esc_html(HOZI_AKADLY_VERSION); ?></code>
                </td>
            </tr>

            <!-- Statistics -->
            <tr>
                <th scope="row"><?php _e('إحصائيات سريعة', 'hozi-akadly'); ?></th>
                <td>
                    <?php
                    global $wpdb;
                    $total_agents = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_agents");
                    $active_agents = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1");
                    $total_assignments = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments");
                    $pending_assignments = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments WHERE confirmation_status = 'pending_confirmation'");
                    ?>
                    <div class="hozi-quick-stats">
                        <div class="hozi-stat-item">
                            <strong><?php echo esc_html($total_agents); ?></strong>
                            <span><?php _e('إجمالي الوكلاء', 'hozi-akadly'); ?></span>
                        </div>
                        <div class="hozi-stat-item">
                            <strong><?php echo esc_html($active_agents); ?></strong>
                            <span><?php _e('وكلاء نشطون', 'hozi-akadly'); ?></span>
                        </div>
                        <div class="hozi-stat-item">
                            <strong><?php echo esc_html($total_assignments); ?></strong>
                            <span><?php _e('إجمالي التخصيصات', 'hozi-akadly'); ?></span>
                        </div>
                        <div class="hozi-stat-item">
                            <strong><?php echo esc_html($pending_assignments); ?></strong>
                            <span><?php _e('في انتظار التأكيد', 'hozi-akadly'); ?></span>
                        </div>
                    </div>
                </td>
            </tr>
        </table>

        <?php submit_button(__('حفظ الإعدادات', 'hozi-akadly')); ?>
    </form>

    <!-- Reset Section -->
    <div class="hozi-reset-section">
        <h2><?php _e('إعادة تعيين البيانات', 'hozi-akadly'); ?></h2>
        <p class="description">
            <?php _e('تحذير: هذه الإجراءات لا يمكن التراجع عنها!', 'hozi-akadly'); ?>
        </p>
        
        <div class="hozi-reset-actions">
            <button type="button" class="button button-secondary" onclick="resetConfirmationLogs()">
                <?php _e('مسح سجل التأكيدات', 'hozi-akadly'); ?>
            </button>
            
            <button type="button" class="button button-secondary" onclick="resetAllAssignments()">
                <?php _e('مسح جميع التخصيصات', 'hozi-akadly'); ?>
            </button>
        </div>
    </div>
</div>

<style>
.hozi-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
}

.hozi-status-indicator.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.hozi-status-indicator.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hozi-quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.hozi-stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.hozi-stat-item strong {
    display: block;
    font-size: 24px;
    color: #0073aa;
    margin-bottom: 5px;
}

.hozi-stat-item span {
    font-size: 12px;
    color: #666;
}

.hozi-reset-section {
    margin-top: 40px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

.hozi-reset-section h2 {
    color: #dc3545;
    margin-top: 0;
}

.hozi-reset-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.form-table th {
    width: 200px;
}

.hozi-settings-info {
    margin: 20px 0;
}

.hozi-info-box {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.hozi-info-box h4 {
    margin-top: 0;
    color: #0073aa;
    font-size: 16px;
}

.hozi-info-box ul {
    margin: 15px 0;
    padding-left: 20px;
}

.hozi-info-box li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.hozi-info-box p {
    margin-bottom: 0;
    color: #666;
    font-style: italic;
}

fieldset label {
    display: block;
    margin-bottom: 10px;
}

fieldset label input[type="radio"],
fieldset label input[type="checkbox"] {
    margin-left: 5px;
}

@media (max-width: 768px) {
    .hozi-quick-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .hozi-reset-actions {
        flex-direction: column;
    }
    
    .form-table th {
        width: auto;
    }
}
</style>

<script>
function resetConfirmationLogs() {
    if (confirm('هل أنت متأكد من مسح جميع سجلات التأكيد؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        if (confirm('تأكيد أخير: سيتم مسح جميع سجلات التأكيد نهائياً!')) {
            // Send AJAX request to reset logs
            jQuery.post(ajaxurl, {
                action: 'hozi_reset_logs',
                nonce: '<?php echo wp_create_nonce('hozi_akadly_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    alert('تم مسح سجلات التأكيد بنجاح');
                    location.reload();
                } else {
                    alert('فشل في مسح السجلات: ' + response.data.message);
                }
            });
        }
    }
}

function resetAllAssignments() {
    if (confirm('هل أنت متأكد من مسح جميع تخصيصات الطلبات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        if (confirm('تأكيد أخير: سيتم مسح جميع التخصيصات والسجلات نهائياً!')) {
            // Send AJAX request to reset assignments
            jQuery.post(ajaxurl, {
                action: 'hozi_reset_assignments',
                nonce: '<?php echo wp_create_nonce('hozi_akadly_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    alert('تم مسح جميع التخصيصات بنجاح');
                    location.reload();
                } else {
                    alert('فشل في مسح التخصيصات: ' + response.data.message);
                }
            });
        }
    }
}

jQuery(document).ready(function($) {
    // Show/hide auto assignment based on distribution method
    $('input[name="distribution_method"]').on('change', function() {
        var method = $(this).val();
        var autoAssignRow = $('input[name="auto_assign_new_orders"]').closest('tr');

        if (method === 'manual') {
            autoAssignRow.find('input[value="no"]').prop('checked', true);
            autoAssignRow.hide();
        } else {
            autoAssignRow.show();
        }
    });

    // Show/hide delivery tracking options based on enable/disable
    $('input[name="enable_delivery_tracking"]').on('change', function() {
        var enabled = $(this).val() === 'yes';
        $('.delivery-tracking-option').toggle(enabled);

        if (!enabled) {
            // Reset to default when disabled
            $('input[name="delivery_tracking_access"][value="assigned_agent_only"]').prop('checked', true);
            $('.custom-assignment-option').hide();
        }
    });

    // Show/hide custom assignment option based on access control
    $('input[name="delivery_tracking_access"]').on('change', function() {
        var access = $(this).val();
        $('.custom-assignment-option').toggle(access === 'custom_assignment');
    });

    // Trigger on page load
    $('input[name="distribution_method"]:checked').trigger('change');
    $('input[name="enable_delivery_tracking"]:checked').trigger('change');
    $('input[name="delivery_tracking_access"]:checked').trigger('change');
});
</script>
