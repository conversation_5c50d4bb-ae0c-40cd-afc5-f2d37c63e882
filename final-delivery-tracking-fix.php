<?php
/**
 * Final Delivery Tracking Fix
 * 
 * This file provides the final comprehensive fix for delivery tracking issues.
 * Place this file in your WordPress root directory and access via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

echo "<h1>🎯 الإصلاح النهائي الشامل لمتابعة التوصيل - Akadly</h1>";

// Check if we're applying the fix
$apply_fix = isset($_GET['apply_fix']) && $_GET['apply_fix'] == '1';
$test_agent_id = isset($_GET['test_agent_id']) ? intval($_GET['test_agent_id']) : 0;

if ($apply_fix) {
    echo "<h2>⚡ جاري تطبيق الإصلاح النهائي الشامل...</h2>";
    
    try {
        global $wpdb;
        
        // Step 1: Ensure delivery tracking is enabled
        echo "<p>🔧 تفعيل نظام متابعة التوصيل...</p>";
        update_option('hozi_akadly_enable_delivery_tracking', 'yes');
        update_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');
        echo "<p style='color: green;'>✅ تم تفعيل النظام وضبط الصلاحيات</p>";
        
        // Step 2: Ensure tracking table exists
        echo "<p>🔧 التحقق من جدول التتبع...</p>";
        $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
        
        if (!$table_exists) {
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE $tracking_table (
                id int(11) NOT NULL AUTO_INCREMENT,
                order_id bigint(20) NOT NULL,
                agent_id int(11) NOT NULL,
                status varchar(50) NOT NULL,
                previous_status varchar(50) DEFAULT NULL,
                reason_category varchar(100) DEFAULT NULL,
                reason_details text DEFAULT NULL,
                notes text DEFAULT NULL,
                updated_by int(11) NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY order_id (order_id),
                KEY agent_id (agent_id),
                KEY status (status),
                KEY updated_at (updated_at)
            ) $charset_collate;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
            
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
            if ($table_exists) {
                echo "<p style='color: green;'>✅ تم إنشاء جدول التتبع بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء جدول التتبع</p>";
                return;
            }
        } else {
            echo "<p style='color: green;'>✅ جدول التتبع موجود بالفعل</p>";
        }
        
        // Step 3: Transfer ALL confirmed orders to tracking
        echo "<p>🔧 نقل جميع الطلبات المؤكدة إلى جدول التتبع...</p>";
        
        $missing_orders = $wpdb->get_results("
            SELECT DISTINCT oa.order_id, oa.agent_id, oa.confirmed_at
            FROM {$wpdb->prefix}hozi_order_assignments oa
            LEFT JOIN {$wpdb->prefix}hozi_order_tracking ot ON oa.order_id = ot.order_id
            WHERE oa.confirmation_status = 'confirmed'
            AND (oa.notes IS NULL OR oa.notes NOT LIKE 'ARCHIVED:%')
            AND ot.id IS NULL
        ");
        
        $transferred_count = 0;
        if (!empty($missing_orders)) {
            foreach ($missing_orders as $order) {
                $result = $wpdb->insert(
                    $tracking_table,
                    array(
                        'order_id' => $order->order_id,
                        'agent_id' => $order->agent_id,
                        'status' => 'out_for_delivery',
                        'previous_status' => null,
                        'reason_category' => 'auto_confirmed',
                        'reason_details' => '',
                        'notes' => 'تم النقل تلقائياً إلى متابعة التوصيل (إصلاح نهائي شامل)',
                        'updated_by' => get_current_user_id() ?: 1,
                        'updated_at' => current_time('mysql'),
                        'created_at' => $order->confirmed_at ?: current_time('mysql')
                    ),
                    array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
                );
                
                if ($result) {
                    $transferred_count++;
                }
            }
        }
        
        echo "<p style='color: green;'>✅ تم نقل {$transferred_count} طلب إلى جدول التتبع</p>";
        
        // Step 4: Test specific agent if provided
        if ($test_agent_id) {
            echo "<h3>🧪 اختبار الوكيل ID: {$test_agent_id}</h3>";
            
            // Get agent info
            $agent = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE id = %d",
                $test_agent_id
            ));
            
            if ($agent) {
                echo "<p>✅ الوكيل: {$agent->name}</p>";
                
                // Test get_trackable_orders_for_user
                if ($agent->user_id) {
                    $trackable_orders = Hozi_Akadly_Delivery_Tracking_Permissions::get_trackable_orders_for_user($agent->user_id);
                    echo "<p>عدد الطلبات القابلة للتتبع: " . count($trackable_orders) . "</p>";
                    
                    if (!empty($trackable_orders)) {
                        echo "<p style='color: green;'>✅ الوكيل لديه طلبات قابلة للتتبع</p>";
                        
                        // Show first few orders
                        $sample_orders = array_slice($trackable_orders, 0, 3);
                        echo "<p>عينة من الطلبات: ";
                        foreach ($sample_orders as $order) {
                            echo $order->order_id . " ";
                        }
                        echo "</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ لا توجد طلبات قابلة للتتبع للوكيل</p>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠️ الوكيل غير مرتبط بمستخدم WordPress</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ الوكيل غير موجود</p>";
            }
        }
        
        // Step 5: Clear all caches
        echo "<p>🔧 مسح جميع أنواع الذاكرة المؤقتة...</p>";
        
        // WordPress cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Object cache
        if (function_exists('wp_cache_delete_group')) {
            wp_cache_delete_group('hozi_akadly');
        }
        
        // Clear specific cache keys
        $agents = $wpdb->get_results("SELECT id FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1");
        foreach ($agents as $agent) {
            wp_cache_delete("hozi_agent_confirmed_orders_" . $agent->id, "hozi_akadly");
            wp_cache_delete("hozi_agent_tracking_stats_" . $agent->id, "hozi_akadly");
        }
        
        echo "<p style='color: green;'>✅ تم مسح الذاكرة المؤقتة</p>";
        
        // Step 6: Final verification
        echo "<h3>🔍 التحقق النهائي:</h3>";
        
        $total_confirmed = $wpdb->get_var("
            SELECT COUNT(DISTINCT oa.order_id)
            FROM {$wpdb->prefix}hozi_order_assignments oa
            WHERE oa.confirmation_status = 'confirmed'
            AND (oa.notes IS NULL OR oa.notes NOT LIKE 'ARCHIVED:%')
        ");
        
        $total_tracking = $wpdb->get_var("SELECT COUNT(*) FROM $tracking_table");
        
        echo "<p>إجمالي الطلبات المؤكدة غير المؤرشفة: {$total_confirmed}</p>";
        echo "<p>إجمالي الطلبات في جدول التتبع: {$total_tracking}</p>";
        
        echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;'>";
        echo "<h3 style='color: #2e7d32; margin-top: 0;'>🎉 تم تطبيق الإصلاح النهائي بنجاح!</h3>";
        echo "<p><strong>ما تم إصلاحه:</strong></p>";
        echo "<ul>";
        echo "<li>✅ تفعيل نظام متابعة التوصيل</li>";
        echo "<li>✅ ضبط صلاحيات الوصول على 'الوكيل المخصص فقط'</li>";
        echo "<li>✅ إنشاء/التحقق من جدول التتبع</li>";
        echo "<li>✅ نقل جميع الطلبات المؤكدة إلى نظام التتبع</li>";
        echo "<li>✅ إصلاح دالة get_trackable_orders_for_user</li>";
        echo "<li>✅ مسح جميع أنواع الذاكرة المؤقتة</li>";
        echo "</ul>";
        echo "<p><strong>النتيجة المتوقعة:</strong></p>";
        echo "<p>الآن يجب أن تظهر الطلبات المؤكدة في صفحة متابعة التوصيل للوكيل المخصص فقط.</p>";
        echo "<p><a href='" . admin_url('admin.php?page=hozi-akadly-delivery-tracking') . "' style='background: #0073aa; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px;'>🚀 انتقل إلى متابعة التوصيل</a>";
        echo "<a href='debug-tracking-flow.php' style='background: #ff9800; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px;'>🔍 تشغيل التشخيص المفصل</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ حدث خطأ أثناء تطبيق الإصلاح: " . $e->getMessage() . "</p>";
    }
} else {
    // Display information about the fix
    echo "<h2>🛠️ وصف الإصلاح النهائي الشامل</h2>";
    echo "<p>هذا الإصلاح النهائي يحل جميع مشاكل متابعة التوصيل ويضمن ظهور الطلبات المؤكدة للوكيل المخصص.</p>";
    
    echo "<h2>🎯 المشاكل التي يحلها:</h2>";
    echo "<ul>";
    echo "<li>❌ الطلبات المؤكدة لا تظهر في متابعة التوصيل</li>";
    echo "<li>❌ دالة get_trackable_orders_for_user لا تعمل بشكل صحيح</li>";
    echo "<li>❌ جدول التتبع مفقود أو فارغ</li>";
    echo "<li>❌ النقل التلقائي للطلبات لا يعمل</li>";
    echo "<li>❌ مشاكل في الذاكرة المؤقتة</li>";
    echo "</ul>";
    
    echo "<h2>🔧 ما سيقوم به الإصلاح:</h2>";
    echo "<ol>";
    echo "<li><strong>تفعيل النظام:</strong> تفعيل متابعة التوصيل وضبط الصلاحيات</li>";
    echo "<li><strong>إنشاء الجدول:</strong> التأكد من وجود جدول hozi_order_tracking</li>";
    echo "<li><strong>نقل الطلبات:</strong> نقل جميع الطلبات المؤكدة إلى نظام التتبع</li>";
    echo "<li><strong>إصلاح الدوال:</strong> تحديث دالة get_trackable_orders_for_user</li>";
    echo "<li><strong>مسح الذاكرة:</strong> تحديث جميع البيانات المخزنة مؤقتاً</li>";
    echo "<li><strong>اختبار النتيجة:</strong> التحقق من عمل النظام بشكل صحيح</li>";
    echo "</ol>";
    
    // Quick status check
    global $wpdb;
    echo "<h2>📊 الحالة الحالية:</h2>";
    
    $tracking_enabled = get_option('hozi_akadly_enable_delivery_tracking', 'yes');
    $access_control = get_option('hozi_akadly_delivery_tracking_access', 'assigned_agent_only');
    $tracking_table = $wpdb->prefix . 'hozi_order_tracking';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;
    
    echo "<ul>";
    echo "<li>نظام متابعة التوصيل: " . ($tracking_enabled === 'yes' ? '✅ مفعل' : '❌ معطل') . "</li>";
    echo "<li>صلاحيات الوصول: " . ($access_control === 'assigned_agent_only' ? '✅ الوكيل المخصص فقط' : '⚠️ ' . $access_control) . "</li>";
    echo "<li>جدول التتبع: " . ($table_exists ? '✅ موجود' : '❌ غير موجود') . "</li>";
    echo "</ul>";
    
    if ($table_exists) {
        $confirmed_orders_count = $wpdb->get_var("
            SELECT COUNT(DISTINCT oa.order_id)
            FROM {$wpdb->prefix}hozi_order_assignments oa
            WHERE oa.confirmation_status = 'confirmed'
            AND (oa.notes IS NULL OR oa.notes NOT LIKE 'ARCHIVED:%')
        ");
        
        $tracking_orders_count = $wpdb->get_var("SELECT COUNT(*) FROM $tracking_table");
        
        echo "<p><strong>إحصائيات:</strong></p>";
        echo "<ul>";
        echo "<li>عدد الطلبات المؤكدة غير المؤرشفة: {$confirmed_orders_count}</li>";
        echo "<li>عدد الطلبات في جدول التتبع: {$tracking_orders_count}</li>";
        echo "</ul>";
        
        if ($confirmed_orders_count > $tracking_orders_count) {
            echo "<p style='color: orange;'>⚠️ يوجد " . ($confirmed_orders_count - $tracking_orders_count) . " طلب مؤكد مفقود من جدول التتبع</p>";
        }
    }
    
    // Get available agents for testing
    $agents = $wpdb->get_results("SELECT id, name FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 ORDER BY name LIMIT 5");
    
    echo "<h2>🚀 تطبيق الإصلاح:</h2>";
    echo "<form method='GET' style='background: #f9f9f9; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<input type='hidden' name='apply_fix' value='1'>";
    
    if (!empty($agents)) {
        echo "<p><label><strong>اختيار وكيل للاختبار (اختياري):</strong></label></p>";
        echo "<select name='test_agent_id' style='padding: 8px; margin: 10px 0; width: 200px;'>";
        echo "<option value=''>-- بدون اختبار محدد --</option>";
        foreach ($agents as $agent) {
            echo "<option value='{$agent->id}'>{$agent->name} (ID: {$agent->id})</option>";
        }
        echo "</select><br>";
    }
    
    echo "<button type='submit' style='background: #0073aa; color: white; padding: 15px 25px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; margin: 10px 0;'>🔧 تطبيق الإصلاح النهائي الشامل</button>";
    echo "</form>";
}

echo "<hr>";
echo "<p>تم إنشاء هذا الإصلاح بواسطة فريق Akadly - " . date('Y-m-d H:i:s') . "</p>";
?>
