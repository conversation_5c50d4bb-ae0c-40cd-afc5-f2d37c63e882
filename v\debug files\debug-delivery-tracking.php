<?php
/**
 * Debug delivery tracking page issues
 */

require_once('wp-config.php');

global $wpdb;

echo "<h1>🔍 تشخيص صفحة متابعة التوصيل</h1>";

// Get current user and agent
$user_id = get_current_user_id();
echo "<p><strong>المستخدم الحالي:</strong> ID = {$user_id}</p>";

$agent_data = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d AND is_active = 1",
    $user_id
));

if (!$agent_data) {
    echo "<p>❌ <strong>مشكلة:</strong> لم يتم العثور على بيانات الوكيل للمستخدم الحالي</p>";

    // Check if user exists in agents table at all
    $all_agent_data = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d",
        $user_id
    ));

    if ($all_agent_data) {
        echo "<p><strong>بيانات الوكيل الموجودة:</strong></p>";
        foreach ($all_agent_data as $agent) {
            echo "<p>- الاسم: {$agent->name}, نشط: " . ($agent->is_active ? 'نعم' : 'لا') . "</p>";
        }
    } else {
        echo "<p>❌ المستخدم غير مسجل كوكيل في النظام</p>";
    }
    exit;
}

echo "<p>✅ <strong>الوكيل:</strong> {$agent_data->name} (ID: {$agent_data->id})</p>";

echo "<h2>📋 اختبار الاستعلام الجديد:</h2>";

// Test the NEW simplified query from delivery-tracking.php
$completed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT
        oa.order_id,
        oa.confirmed_at,
        oa.notes as confirmation_notes,
        oa.archived,
        oa.confirmed_at as order_date,
        'confirmed' as post_status
    FROM {$wpdb->prefix}hozi_order_assignments oa
    WHERE oa.agent_id = %d
    AND oa.confirmation_status = 'confirmed'
    ORDER BY oa.confirmed_at DESC
    LIMIT 100",
    $agent_data->id
));

echo "<p><strong>عدد الطلبات المؤكدة:</strong> " . count($completed_orders) . "</p>";

if ($completed_orders) {
    echo "<h3>📋 تفاصيل الطلبات المؤكدة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>مؤرشف</th><th>حالة WC</th></tr>";
    foreach ($completed_orders as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>" . ($order->archived ? 'نعم' : 'لا') . "</td>";
        echo "<td>{$order->post_status}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Test tracking data
    echo "<h3>📦 اختبار بيانات التتبع:</h3>";
    $order_ids = array_column($completed_orders, 'order_id');
    $order_ids_str = implode(',', array_map('intval', $order_ids));

    $tracking_data = $wpdb->get_results("
        SELECT ot1.order_id, ot1.status, ot1.notes, ot1.updated_at, ot1.created_at
        FROM {$wpdb->prefix}hozi_order_tracking ot1
        INNER JOIN (
            SELECT order_id, MAX(updated_at) as max_updated
            FROM {$wpdb->prefix}hozi_order_tracking
            WHERE order_id IN ({$order_ids_str})
            GROUP BY order_id
        ) ot2 ON ot1.order_id = ot2.order_id AND ot1.updated_at = ot2.max_updated
    ");

    echo "<p><strong>عدد الطلبات مع بيانات تتبع:</strong> " . count($tracking_data) . "</p>";

    if ($tracking_data) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم الطلب</th><th>حالة التتبع</th><th>ملاحظات</th><th>تاريخ التحديث</th></tr>";
        foreach ($tracking_data as $track) {
            echo "<tr>";
            echo "<td>#{$track->order_id}</td>";
            echo "<td>{$track->status}</td>";
            echo "<td>" . ($track->notes ?: 'لا توجد') . "</td>";
            echo "<td>" . date('Y/m/d H:i', strtotime($track->updated_at)) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Test filtering logic
    echo "<h3>🔍 اختبار منطق الفلترة:</h3>";

    $tracking_lookup = array();
    foreach ($tracking_data as $track) {
        $tracking_lookup[$track->order_id] = $track;
    }

    // Merge tracking data with orders
    foreach ($completed_orders as $order_data) {
        if (isset($tracking_lookup[$order_data->order_id])) {
            $track = $tracking_lookup[$order_data->order_id];
            $order_data->delivery_status = $track->status;
            $order_data->delivery_notes = $track->notes;
            $order_data->delivery_date = $track->updated_at;
            $order_data->tracking_created = $track->created_at;
        } else {
            $order_data->delivery_status = null;
            $order_data->delivery_notes = '';
            $order_data->delivery_date = null;
            $order_data->tracking_created = null;
        }
    }

    echo "<p><strong>قبل الفلترة:</strong> " . count($completed_orders) . " طلب</p>";

    // Apply the filter
    $filtered_orders = array_filter($completed_orders, function($order) {
        return !$order->delivery_status ||
               $order->delivery_status !== 'delivered' ||
               !$order->archived;
    });

    echo "<p><strong>بعد الفلترة:</strong> " . count($filtered_orders) . " طلب</p>";

    if (count($filtered_orders) != count($completed_orders)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>⚠️ تم استبعاد طلبات بواسطة الفلترة:</h4>";
        foreach ($completed_orders as $order) {
            $show = !$order->delivery_status ||
                   $order->delivery_status !== 'delivered' ||
                   !$order->archived;
            if (!$show) {
                echo "<p>- طلب #{$order->order_id}: حالة التتبع = {$order->delivery_status}, مؤرشف = " . ($order->archived ? 'نعم' : 'لا') . "</p>";
            }
        }
        echo "</div>";
    }

    if (count($filtered_orders) > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>✅ الطلبات التي يجب أن تظهر:</h4>";
        foreach ($filtered_orders as $order) {
            echo "<p>- طلب #{$order->order_id}: حالة التتبع = " . ($order->delivery_status ?: 'بانتظار التوصيل') . ", مؤرشف = " . ($order->archived ? 'نعم' : 'لا') . "</p>";
        }
        echo "</div>";
    }

} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ لا توجد طلبات مؤكدة!</h3>";
    echo "<p>تحقق من:</p>";
    echo "<ul>";
    echo "<li>هل قام الوكيل بتأكيد أي طلبات؟</li>";
    echo "<li>هل البيانات موجودة في جدول hozi_order_assignments؟</li>";
    echo "<li>هل حالة التأكيد = 'confirmed'؟</li>";
    echo "</ul>";
    echo "</div>";

    // Check all assignments for this agent
    $all_assignments = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE agent_id = %d ORDER BY assigned_at DESC LIMIT 10",
        $agent_data->id
    ));

    echo "<h3>📋 جميع المهام المخصصة للوكيل:</h3>";
    if ($all_assignments) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم الطلب</th><th>حالة التأكيد</th><th>تاريخ التخصيص</th><th>تاريخ التأكيد</th></tr>";
        foreach ($all_assignments as $assignment) {
            echo "<tr>";
            echo "<td>#{$assignment->order_id}</td>";
            echo "<td>{$assignment->confirmation_status}</td>";
            echo "<td>" . date('Y/m/d H:i', strtotime($assignment->assigned_at)) . "</td>";
            echo "<td>" . ($assignment->confirmed_at ? date('Y/m/d H:i', strtotime($assignment->confirmed_at)) : 'لم يؤكد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ لا توجد مهام مخصصة لهذا الوكيل</p>";
    }
}
?>
