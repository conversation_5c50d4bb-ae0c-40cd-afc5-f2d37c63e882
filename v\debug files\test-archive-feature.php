<?php
/**
 * Test Archive Feature - أكدلي
 *
 * This script tests the new auto-archive functionality
 */

// WordPress environment
if (file_exists('wp-config.php')) {
    require_once('wp-config.php');
} else {
    die('WordPress not found');
}

if (file_exists('wp-load.php')) {
    require_once('wp-load.php');
} else {
    die('WordPress not loaded');
}

if (!function_exists('current_user_can') || !current_user_can('manage_options')) {
    die('Access denied - Please login as administrator');
}

echo "<h1>🧪 اختبار ميزة الأرشفة التلقائية - أكدلي</h1>";

global $wpdb;

// Load required classes
require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-database.php';
require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-order-distributor.php';
require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-agent-manager.php';

echo "<h2>🔧 حالة قاعدة البيانات</h2>";

// Check if columns exist
$assignments_table = $wpdb->prefix . 'hozi_order_assignments';
$archived_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived'");
$archived_at_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived_at'");

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
if (!empty($archived_exists)) {
    echo "<p style='color: #28a745;'>✅ عمود 'archived' موجود</p>";
} else {
    echo "<p style='color: #dc3545;'>❌ عمود 'archived' غير موجود - استخدم زر التحديث أدناه</p>";
}

if (!empty($archived_at_exists)) {
    echo "<p style='color: #28a745;'>✅ عمود 'archived_at' موجود</p>";
} else {
    echo "<p style='color: #dc3545;'>❌ عمود 'archived_at' غير موجود - استخدم زر التحديث أدناه</p>";
}
echo "</div>";

echo "<hr>";

// Get sirin agent
$sirin_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE name = 'sirin'");

if (!$sirin_agent) {
    echo "<p style='color: red;'>❌ الوكيل sirin غير موجود</p>";
    exit;
}

echo "<p>✅ الوكيل sirin موجود - ID: {$sirin_agent->id}</p>";

echo "<h2>📋 الطلبات المؤكدة الحالية</h2>";

// Get confirmed orders for sirin
$confirmed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_order_assignments
     WHERE agent_id = %d AND confirmation_status = 'confirmed' AND (archived IS NULL OR archived = 0)
     ORDER BY confirmed_at DESC LIMIT 5",
    $sirin_agent->id
));

echo "<p><strong>عدد الطلبات المؤكدة غير المؤرشفة:</strong> " . count($confirmed_orders) . "</p>";

if (!empty($confirmed_orders)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>رقم الطلب</th><th>حالة التأكيد</th><th>تاريخ التأكيد</th><th>مؤرشف؟</th><th>إجراء</th>";
    echo "</tr>";

    foreach ($confirmed_orders as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>{$order->confirmation_status}</td>";
        echo "<td>" . ($order->confirmed_at ?: 'غير محدد') . "</td>";
        echo "<td>" . ($order->archived ? 'نعم' : 'لا') . "</td>";
        echo "<td><a href='?test_archive={$order->order_id}' class='button' style='font-size: 10px;'>🗂️ اختبار الأرشفة</a></td>";
        echo "</tr>";
    }
    echo "</table>";

    // Test archive functionality
    if (isset($_GET['test_archive'])) {
        $test_order_id = intval($_GET['test_archive']);

        echo "<h3>🧪 اختبار الأرشفة للطلب #{$test_order_id}</h3>";

        // Initialize order distributor
        $agent_manager = new Hozi_Akadly_Agent_Manager();
        $order_distributor = new Hozi_Akadly_Order_Distributor();

        // Simulate second confirmation (which should trigger archive)
        $result = $order_distributor->update_confirmation_status($test_order_id, 'confirmed', 'اختبار الأرشفة التلقائية');

        if ($result) {
            echo "<p style='color: green;'>✅ تم تشغيل منطق الأرشفة بنجاح!</p>";

            // Check if order was archived
            $updated_order = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
                $test_order_id
            ));

            if ($updated_order) {
                echo "<p><strong>الحالة الجديدة:</strong> {$updated_order->confirmation_status}</p>";
                echo "<p><strong>مؤرشف:</strong> " . ($updated_order->archived ? 'نعم' : 'لا') . "</p>";
                echo "<p><strong>تاريخ الأرشفة:</strong> " . ($updated_order->archived_at ?: 'غير محدد') . "</p>";

                if ($updated_order->confirmation_status === 'akadly_completed' && $updated_order->archived) {
                    echo "<p style='color: green; font-weight: bold;'>🎉 الأرشفة التلقائية تعمل بشكل مثالي!</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ الأرشفة لم تعمل كما متوقع</p>";
                }
            }

            // Check WooCommerce order status
            $wc_order = wc_get_order($test_order_id);
            if ($wc_order) {
                echo "<p><strong>حالة الطلب في WooCommerce:</strong> " . $wc_order->get_status() . "</p>";
            }

        } else {
            echo "<p style='color: red;'>❌ فشل في تشغيل منطق الأرشفة</p>";
        }

        echo "<script>setTimeout(() => window.location.href = window.location.pathname, 3000);</script>";
    }

} else {
    echo "<p style='color: orange;'>⚠️ لا توجد طلبات مؤكدة للاختبار</p>";

    // Create a test confirmed order
    echo "<h3>🔧 إنشاء طلب تجريبي للاختبار</h3>";

    if (isset($_GET['create_test_order'])) {
        // Get latest order
        $latest_order = $wpdb->get_var("SELECT ID FROM {$wpdb->prefix}posts WHERE post_type = 'shop_order' ORDER BY ID DESC LIMIT 1");

        if ($latest_order) {
            // Create assignment for this order
            $wpdb->insert(
                $wpdb->prefix . 'hozi_order_assignments',
                array(
                    'order_id' => $latest_order,
                    'agent_id' => $sirin_agent->id,
                    'assignment_method' => 'manual',
                    'confirmation_status' => 'confirmed',
                    'assigned_at' => current_time('mysql'),
                    'confirmed_at' => current_time('mysql'),
                    'notes' => 'طلب تجريبي لاختبار الأرشفة',
                    'archived' => 0
                ),
                array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d')
            );

            echo "<p style='color: green;'>✅ تم إنشاء طلب تجريبي مؤكد #{$latest_order}</p>";
            echo "<script>setTimeout(() => window.location.href = window.location.pathname, 2000);</script>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد طلبات في المتجر</p>";
        }
    } else {
        echo "<p><a href='?create_test_order=1' class='button button-primary'>🔧 إنشاء طلب تجريبي</a></p>";
    }
}

echo "<hr>";

echo "<h2>📊 إحصائيات الأرشفة</h2>";

// Get archive statistics
$archive_stats = $wpdb->get_row($wpdb->prepare(
    "SELECT
        COUNT(*) as total_orders,
        SUM(CASE WHEN archived = 1 THEN 1 ELSE 0 END) as archived_orders,
        SUM(CASE WHEN confirmation_status = 'akadly_completed' THEN 1 ELSE 0 END) as completed_orders
     FROM {$wpdb->prefix}hozi_order_assignments
     WHERE agent_id = %d",
    $sirin_agent->id
));

if ($archive_stats) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>إجمالي الطلبات</th><th>الطلبات المؤرشفة</th><th>الطلبات المكتملة</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>{$archive_stats->total_orders}</td>";
    echo "<td>{$archive_stats->archived_orders}</td>";
    echo "<td>{$archive_stats->completed_orders}</td>";
    echo "</tr>";
    echo "</table>";
}

echo "<h2>🎯 كيفية عمل الأرشفة</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>📋 الآلية:</h3>";
echo "<ol>";
echo "<li><strong>التأكيد الأول:</strong> الطلب يصبح 'confirmed'</li>";
echo "<li><strong>التأكيد الثاني:</strong> الطلب يصبح 'akadly_completed' ويُؤرشف تلقائياً</li>";
echo "<li><strong>النتيجة:</strong> الطلب يختفي من قائمة التتبع ويصبح 'completed' في WooCommerce</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🎯 اختبار الميزات الجديدة</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>✨ الميزات المضافة:</h3>";
echo "<ul>";
echo "<li><strong>🗂️ تبويب الطلبات المؤرشفة:</strong> صفحة جديدة لعرض الطلبات المؤرشفة</li>";
echo "<li><strong>🧹 زر مسح الطلبات المكتملة:</strong> في صفحة التتبع</li>";
echo "<li><strong>📊 إحصائيات الأرشيف:</strong> عرض تفصيلي للطلبات المؤرشفة</li>";
echo "<li><strong>🔄 أرشفة تلقائية:</strong> عند التأكيد الثاني</li>";
echo "</ul>";
echo "</div>";

echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly-my-orders' class='button button-primary'>📋 طلباتي</a>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly-my-tracking' class='button button-primary'>👁️ تتبع الطلبات</a>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly-my-archived' class='button button-secondary'>🗂️ الطلبات المؤرشفة</a>";
echo "<a href='?update_db=1' class='button' style='background: #28a745; border-color: #28a745; color: white;'>🔧 تحديث قاعدة البيانات</a>";
echo "<a href='/wp-admin/admin.php?page=hozi-akadly' class='button'>🏠 لوحة التحكم</a>";
echo "</div>";

// Handle database update
if (isset($_GET['update_db'])) {
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
    echo "<h4 style='color: #0c5460; margin-top: 0;'>🔧 تحديث قاعدة البيانات:</h4>";

    try {
        // Check if table exists first
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$assignments_table'");

        if (!$table_exists) {
            echo "<p style='color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px;'>❌ جدول hozi_order_assignments غير موجود! يجب إنشاء الجداول أولاً.</p>";
            echo "<p><a href='/wp-admin/admin.php?page=hozi-akadly' class='button button-primary'>إنشاء الجداول من لوحة التحكم</a></p>";
        } else {
            Hozi_Akadly_Database::add_archive_columns();
            echo "<p style='color: #155724; background: #d4edda; padding: 10px; border-radius: 4px;'>✅ تم تحديث قاعدة البيانات بنجاح! تم إضافة أعمدة الأرشيف.</p>";

            // Check columns after update
            $archived_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived'");
            $archived_at_exists = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table LIKE 'archived_at'");

            echo "<p style='color: #0c5460;'>";
            echo "📋 حالة الأعمدة بعد التحديث:<br>";
            echo "• عمود 'archived': " . (!empty($archived_exists) ? "✅ موجود" : "❌ غير موجود") . "<br>";
            echo "• عمود 'archived_at': " . (!empty($archived_at_exists) ? "✅ موجود" : "❌ غير موجود");
            echo "</p>";

            echo "<p style='color: #0c5460;'><strong>🔄 يرجى تحديث الصفحة لرؤية التغييرات.</strong></p>";
        }

    } catch (Exception $e) {
        echo "<p style='color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px;'>❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage() . "</p>";
    }

    echo "</div>";
}

echo "<h3>🧪 خطوات الاختبار:</h3>";
echo "<ol style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<li><strong>الطريقة الأولى - أرشفة عند التوصيل:</strong>";
echo "<ul style='margin: 10px 0;'>";
echo "<li>اذهب لصفحة <strong>طلباتي</strong> وأكد طلب</li>";
echo "<li>اذهب لصفحة <strong>تتبع الطلبات</strong> وحدث الطلب إلى <strong>'تم التوصيل'</strong></li>";
echo "<li>سيتم أرشفة الطلب تلقائياً ونقله للأرشيف</li>";
echo "</ul></li>";
echo "<li><strong>الطريقة الثانية - مسح الطلبات المكتملة:</strong>";
echo "<ul style='margin: 10px 0;'>";
echo "<li>بعد وجود طلبات مكتملة في صفحة التتبع</li>";
echo "<li>استخدم زر <strong>'مسح الطلبات المكتملة'</strong></li>";
echo "<li>سيتم نقل جميع الطلبات المكتملة للأرشيف</li>";
echo "</ul></li>";
echo "<li><strong>الطريقة الثالثة - التأكيد الثاني:</strong>";
echo "<ul style='margin: 10px 0;'>";
echo "<li>في صفحة <strong>طلباتي</strong> جرب تأكيد طلب مؤكد مسبقاً</li>";
echo "<li>ستظهر رسالة تحذير ثم أرشفة تلقائية</li>";
echo "</ul></li>";
echo "<li><strong>التحقق من النتائج:</strong>";
echo "<ul style='margin: 10px 0;'>";
echo "<li>تحقق من صفحة <strong>الطلبات المؤرشفة</strong></li>";
echo "<li>تحقق من إحصائيات الأرشيف</li>";
echo "</ul></li>";
echo "</ol>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
echo "<h4 style='color: #856404; margin-top: 0;'>🔧 إصلاح المشكلة:</h4>";
echo "<p style='color: #856404; margin-bottom: 0;'>تم إصلاح مشكلة عدم الأرشفة في صفحة التتبع. الآن عند تحديث الطلب إلى 'تم التوصيل' سيتم أرشفته تلقائياً.</p>";
echo "</div>";
?>
