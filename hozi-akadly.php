<?php
/**
 * Plugin Name: أكدلي - Akadly
 * Plugin URI: https://hostazi.shop/hozi-akadly
 * Description: نظام تأكيد الطلبيات الشامل لـ WooCommerce - توزيع ذكي للطلبات وإدارة وكلاء التأكيد
 * Version: 1.0.0
 * Author: Hostazi
 * Author URI: https://hostazi.shop
 * Text Domain: hozi-akadly
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * Woo: 8.0.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('HOZI_AKADLY_VERSION', '1.0.0');
define('HOZI_AKADLY_PLUGIN_FILE', __FILE__);
define('HOZI_AKADLY_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('HOZI_AKADLY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('HOZI_AKADLY_PLUGIN_BASENAME', plugin_basename(__FILE__));
// define('HOZI_AKADLY_DEBUG', true); // Debug mode disabled - license required

/**
 * Check if WooCommerce is active
 */
function hozi_akadly_check_woocommerce() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', 'hozi_akadly_woocommerce_missing_notice');
        return false;
    }
    return true;
}

/**
 * WooCommerce missing notice
 */
function hozi_akadly_woocommerce_missing_notice() {
    ?>
    <div class="notice notice-error">
        <p><?php _e('Hozi Akadly requires WooCommerce to be installed and active.', 'hozi-akadly'); ?></p>
    </div>
    <?php
}

/**
 * Initialize the plugin
 */
function hozi_akadly_init() {
    if (!hozi_akadly_check_woocommerce()) {
        return;
    }

    // Load plugin text domain
    load_plugin_textdomain('hozi-akadly', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Include required files
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-license-manager.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-hozi-akadly.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-database.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-agent-manager.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-order-distributor.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-order-tracker.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-customer-orders.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-admin-menu-manager.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-delivery-tracking-permissions.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-messaging-system.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'admin/class-admin.php';
    require_once HOZI_AKADLY_PLUGIN_DIR . 'public/class-public.php';

    // Initialize license manager first
    Hozi_Akadly_License_Manager::get_instance();

    // Always initialize main class - license check will be done inside
    Hozi_Akadly::get_instance();

    // Initialize messaging system
    new Hozi_Akadly_Messaging_System();

    // Check for database updates
    hozi_akadly_check_version();

    // Force add archived columns (temporary fix for existing installations)
    hozi_akadly_add_archived_columns();
}

/**
 * Plugin activation hook
 */
function hozi_akadly_activate() {
    if (!hozi_akadly_check_woocommerce()) {
        wp_die(__('Hozi Akadly requires WooCommerce to be installed and active.', 'hozi-akadly'));
    }

    // Create database tables
    require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-database.php';
    Hozi_Akadly_Database::create_tables();

    // Create confirmation agent role
    hozi_akadly_create_agent_role();

    // Set default options
    hozi_akadly_set_default_options();

    // Flush rewrite rules to remove old endpoints and register new ones
    flush_rewrite_rules();

    // Set activation notice
    set_transient('hozi_akadly_activation_notice', true, 30);

    // 🚀 PERMANENT FIX: Schedule periodic check for missing tracking orders
    if (!wp_next_scheduled('hozi_akadly_periodic_tracking_check')) {
        wp_schedule_event(time(), 'hourly', 'hozi_akadly_periodic_tracking_check');
    }
}

/**
 * Plugin deactivation hook
 */
function hozi_akadly_deactivate() {
    // Clean up scheduled events if any
    wp_clear_scheduled_hook('hozi_akadly_cleanup');
    wp_clear_scheduled_hook('hozi_akadly_backup_transfer');
    wp_clear_scheduled_hook('hozi_akadly_periodic_tracking_check');

    // Flush rewrite rules to remove custom endpoints
    flush_rewrite_rules();
}

/**
 * Create confirmation agent role
 */
function hozi_akadly_create_agent_role() {
    // Remove existing role first to recreate with correct capabilities
    remove_role('confirmation_agent');

    add_role(
        'confirmation_agent',
        __('Confirmation Agent', 'hozi-akadly'),
        array(
            'read' => true,
            'edit_dashboard' => true,
            'edit_posts' => true, // Required for wp-admin access
            'upload_files' => true, // Basic capability
            'hozi_view_assigned_orders' => true,
            'hozi_confirm_orders' => true,
            'hozi_update_order_status' => true,
        )
    );

    // Also add capabilities to existing role if it exists
    $role = get_role('confirmation_agent');
    if ($role) {
        $role->add_cap('read');
        $role->add_cap('edit_dashboard');
        $role->add_cap('edit_posts');
        $role->add_cap('upload_files');
        $role->add_cap('hozi_view_assigned_orders');
        $role->add_cap('hozi_confirm_orders');
        $role->add_cap('hozi_update_order_status');
    }
}

/**
 * Set default plugin options
 */
function hozi_akadly_set_default_options() {
    $default_options = array(
        'distribution_method' => 'round_robin', // round_robin or manual
        'auto_assign_new_orders' => 'yes',
        'order_statuses_to_assign' => array('pending', 'processing'),
        'enable_delivery_tracking' => 'yes', // yes or no
        'delivery_tracking_access' => 'assigned_agent_only', // assigned_agent_only, any_agent, managers_only, custom_assignment
        'delivery_tracking_assigned_user' => '', // user_id for custom assignment
        'confirmation_statuses' => array(
            'pending_confirmation' => __('في انتظار التأكيد', 'hozi-akadly'),
            'confirmed' => __('تم التأكيد', 'hozi-akadly'),
            'rejected' => __('تم الرفض', 'hozi-akadly'),
            'no_answer' => __('العميل لم يرد', 'hozi-akadly'),
            'callback_later' => __('إعادة الاتصال لاحقًا', 'hozi-akadly'),
        ),
    );

    foreach ($default_options as $option_name => $option_value) {
        add_option('hozi_akadly_' . $option_name, $option_value);
    }
}

/**
 * Check plugin version and update if needed
 */
function hozi_akadly_check_version() {
    $current_version = get_option('hozi_akadly_version', '0.0.0');

    if (version_compare($current_version, HOZI_AKADLY_VERSION, '<')) {
        // Update database tables
        require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-database.php';
        Hozi_Akadly_Database::create_tables();

        // Add missing archived columns to order_assignments table
        hozi_akadly_add_archived_columns();

        // 🚀 PERMANENT FIX: Ensure all confirmed orders are in tracking after update
        if (class_exists('Hozi_Akadly_Delivery_Tracking_Permissions')) {
            Hozi_Akadly_Delivery_Tracking_Permissions::ensure_confirmed_orders_in_tracking();
        }

        // Update version
        update_option('hozi_akadly_version', HOZI_AKADLY_VERSION);
    }
}

/**
 * Add archived columns to order_assignments table
 */
function hozi_akadly_add_archived_columns() {
    global $wpdb;

    $assignments_table = $wpdb->prefix . 'hozi_order_assignments';

    // Get current table structure
    $columns = $wpdb->get_col("SHOW COLUMNS FROM $assignments_table");

    // Add archived column if not exists
    if (!in_array('archived', $columns)) {
        $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN archived tinyint(1) DEFAULT 0");
    }

    // Add archived_at column if not exists
    if (!in_array('archived_at', $columns)) {
        $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN archived_at datetime DEFAULT NULL");
    }
}

/**
 * Declare HPOS compatibility
 */
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});

// Hook into WordPress
add_action('plugins_loaded', 'hozi_akadly_init');
register_activation_hook(__FILE__, 'hozi_akadly_activate');
register_deactivation_hook(__FILE__, 'hozi_akadly_deactivate');

// 🚀 PERMANENT FIX: Add backup transfer hook
add_action('hozi_akadly_backup_transfer', 'hozi_akadly_handle_backup_transfer', 10, 2);

// 🚀 PERMANENT FIX: Add periodic tracking check hook
add_action('hozi_akadly_periodic_tracking_check', 'hozi_akadly_handle_periodic_tracking_check');

/**
 * Handle backup transfer to tracking system
 * This ensures orders are transferred even if the main transfer fails
 */
function hozi_akadly_handle_backup_transfer($order_id, $agent_id) {
    global $wpdb;

    try {
        // Check if order is already in tracking
        $existing_tracking = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
            $order_id
        ));

        if ($existing_tracking) {
            error_log("Hozi Akadly Backup: Order {$order_id} already in tracking, skipping backup transfer");
            return;
        }

        // Check if order is still confirmed and not archived
        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments
             WHERE order_id = %d AND agent_id = %d AND confirmation_status = 'confirmed'
             AND (notes IS NULL OR notes NOT LIKE 'ARCHIVED:%%')",
            $order_id, $agent_id
        ));

        if (!$assignment) {
            error_log("Hozi Akadly Backup: Order {$order_id} not confirmed or archived, skipping backup transfer");
            return;
        }

        // Ensure tracking table exists
        hozi_akadly_ensure_tracking_table();

        // Insert into tracking table
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_tracking',
            array(
                'order_id' => $order_id,
                'agent_id' => $agent_id,
                'status' => 'out_for_delivery',
                'previous_status' => null,
                'reason_category' => 'backup_transfer',
                'reason_details' => '',
                'notes' => 'تم النقل بواسطة النظام الاحتياطي - ضمان وصول الطلب لمتابعة التوصيل',
                'updated_by' => 1,
                'updated_at' => current_time('mysql'),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );

        if ($result) {
            error_log("Hozi Akadly Backup: ✅ Successfully transferred order {$order_id} to tracking via backup system");

            // Add order note
            $order = wc_get_order($order_id);
            if ($order) {
                $order->add_order_note(
                    '🔄 تم نقل الطلب إلى متابعة التوصيل بواسطة النظام الاحتياطي',
                    0
                );
            }
        } else {
            error_log("Hozi Akadly Backup: ❌ Failed to transfer order {$order_id} via backup system: " . $wpdb->last_error);
        }

    } catch (Exception $e) {
        error_log("Hozi Akadly Backup Transfer Error: " . $e->getMessage());
    }
}

/**
 * Handle periodic tracking check
 * This runs hourly to ensure no confirmed orders are missing from tracking
 */
function hozi_akadly_handle_periodic_tracking_check() {
    try {
        // Only run if delivery tracking is enabled
        if (get_option('hozi_akadly_enable_delivery_tracking', 'yes') !== 'yes') {
            return;
        }

        // Use the existing function to ensure all confirmed orders are in tracking
        if (class_exists('Hozi_Akadly_Delivery_Tracking_Permissions')) {
            $transferred_count = Hozi_Akadly_Delivery_Tracking_Permissions::ensure_confirmed_orders_in_tracking();

            if ($transferred_count > 0) {
                error_log("Hozi Akadly Periodic Check: ✅ Transferred {$transferred_count} missing orders to tracking");
            }
        }

    } catch (Exception $e) {
        error_log("Hozi Akadly Periodic Check Error: " . $e->getMessage());
    }
}

/**
 * Ensure tracking table exists
 */
function hozi_akadly_ensure_tracking_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'hozi_order_tracking';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

    if (!$table_exists) {
        require_once HOZI_AKADLY_PLUGIN_DIR . 'includes/class-database.php';
        Hozi_Akadly_Database::create_tables();
    }
}

// AJAX handlers
add_action('wp_ajax_hozi_assign_order', 'hozi_akadly_ajax_assign_order');

/**
 * AJAX handler for quick order assignment
 */
function hozi_akadly_ajax_assign_order() {
    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'hozi_akadly_nonce')) {
        wp_send_json_error(array('message' => 'فشل في التحقق من الأمان'));
    }

    // Check permissions
    if (!current_user_can('edit_shop_orders')) {
        wp_send_json_error(array('message' => 'ليس لديك صلاحية لتعديل الطلبات'));
    }

    $order_id = intval($_POST['order_id']);
    $agent_id = intval($_POST['agent_id']);

    if (!$order_id || !$agent_id) {
        wp_send_json_error(array('message' => 'بيانات غير صحيحة'));
    }

    global $wpdb;

    // Get agent info
    $agent = $wpdb->get_row($wpdb->prepare(
        "SELECT name FROM {$wpdb->prefix}hozi_agents WHERE id = %d AND is_active = 1",
        $agent_id
    ));

    if (!$agent) {
        wp_send_json_error(array('message' => 'الوكيل غير موجود أو غير نشط'));
    }

    // Check if order exists
    $order = wc_get_order($order_id);
    if (!$order) {
        wp_send_json_error(array('message' => 'الطلب غير موجود'));
    }

    // Check if already assigned
    $existing_assignment = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}hozi_order_assignments WHERE order_id = %d",
        $order_id
    ));

    if ($existing_assignment) {
        // Update existing assignment
        $wpdb->update(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'agent_id' => $agent_id,
                'assigned_at' => current_time('mysql')
            ),
            array('order_id' => $order_id),
            array('%d', '%s'),
            array('%d')
        );

        $action = 'تم تغيير تخصيص الطلب';
    } else {
        // Create new assignment
        $wpdb->insert(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'order_id' => $order_id,
                'agent_id' => $agent_id,
                'assigned_at' => current_time('mysql'),
                'confirmation_status' => 'pending_confirmation'
            ),
            array('%d', '%d', '%s', '%s')
        );

        $action = 'تم تخصيص الطلب';
    }

    // Add order note
    $note_text = sprintf('%s للوكيل: %s (تخصيص سريع)', $action, $agent->name);
    $order->add_order_note($note_text, 0); // 0 = private note

    // Log the assignment
    $wpdb->insert(
        $wpdb->prefix . 'hozi_confirmation_logs',
        array(
            'order_id' => $order_id,
            'agent_id' => $agent_id,
            'action' => 'assigned',
            'notes' => 'تخصيص سريع من صفحة توزيع الطلبات',
            'created_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%s', '%s')
    );

    wp_send_json_success(array('message' => $action . ' بنجاح'));
}



/**
 * Add plugin action links
 */
function hozi_akadly_plugin_action_links($links) {
    $settings_link = '<a href="' . admin_url('admin.php?page=hozi-akadly-settings') . '">' . __('Settings', 'hozi-akadly') . '</a>';
    array_unshift($links, $settings_link);
    return $links;
}
add_filter('plugin_action_links_' . HOZI_AKADLY_PLUGIN_BASENAME, 'hozi_akadly_plugin_action_links');
