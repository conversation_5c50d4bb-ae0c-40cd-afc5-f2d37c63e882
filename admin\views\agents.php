<?php
/**
 * Agents management view
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('إدارة الوكلاء', 'hozi-akadly'); ?></h1>

    <!-- Add New Agent Form -->
    <div class="hozi-add-agent-section">
        <h2><?php _e('إضافة وكيل جديد', 'hozi-akadly'); ?></h2>
        <form method="post" class="hozi-add-agent-form">
            <?php wp_nonce_field('hozi_agent_action'); ?>
            <input type="hidden" name="action" value="create_agent">
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="user_id"><?php _e('المستخدم', 'hozi-akadly'); ?></label>
                    </th>
                    <td>
                        <select name="user_id" id="user_id" required>
                            <option value=""><?php _e('اختر مستخدم', 'hozi-akadly'); ?></option>
                            <?php
                            $users = get_users(array(
                                'meta_query' => array(
                                    array(
                                        'key' => 'wp_capabilities',
                                        'value' => 'confirmation_agent',
                                        'compare' => 'NOT LIKE'
                                    )
                                )
                            ));
                            
                            foreach ($users as $user) {
                                // Check if user is already an agent
                                $existing_agent = Hozi_Akadly_Database::get_agent_by_user_id($user->ID);
                                if (!$existing_agent) {
                                    echo '<option value="' . esc_attr($user->ID) . '">' . esc_html($user->display_name . ' (' . $user->user_email . ')') . '</option>';
                                }
                            }
                            ?>
                        </select>
                        <p class="description"><?php _e('اختر مستخدم موجود لتحويله إلى وكيل تأكيد', 'hozi-akadly'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="name"><?php _e('اسم الوكيل', 'hozi-akadly'); ?></label>
                    </th>
                    <td>
                        <input type="text" name="name" id="name" class="regular-text" required>
                        <p class="description"><?php _e('اسم الوكيل كما سيظهر في النظام', 'hozi-akadly'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="phone"><?php _e('رقم الهاتف', 'hozi-akadly'); ?></label>
                    </th>
                    <td>
                        <input type="tel" name="phone" id="phone" class="regular-text">
                        <p class="description"><?php _e('رقم هاتف الوكيل (اختياري)', 'hozi-akadly'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="max_orders_per_day"><?php _e('الحد الأقصى للطلبات يومياً', 'hozi-akadly'); ?></label>
                    </th>
                    <td>
                        <input type="number" name="max_orders_per_day" id="max_orders_per_day" value="0" min="0">
                        <p class="description"><?php _e('0 = بلا حدود', 'hozi-akadly'); ?></p>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="submit" class="button-primary" value="<?php _e('إضافة وكيل', 'hozi-akadly'); ?>">
            </p>
        </form>
    </div>

    <!-- Agents List -->
    <div class="hozi-agents-list">
        <h2><?php _e('قائمة الوكلاء', 'hozi-akadly'); ?></h2>
        
        <?php if (!empty($agents)) : ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col"><?php _e('الاسم', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('البريد الإلكتروني', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('الهاتف', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('الحالة', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('الطلبات الحالية', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('إجمالي المؤكد', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('إجمالي المرفوض', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('آخر تخصيص', 'hozi-akadly'); ?></th>
                        <th scope="col"><?php _e('الإجراءات', 'hozi-akadly'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($agents as $agent) : ?>
                        <tr>
                            <td>
                                <strong><?php echo esc_html($agent->name); ?></strong>
                                <div class="row-actions">
                                    <span class="edit">
                                        <a href="<?php echo admin_url('user-edit.php?user_id=' . $agent->user_id); ?>">
                                            <?php _e('تعديل المستخدم', 'hozi-akadly'); ?>
                                        </a>
                                    </span>
                                </div>
                            </td>
                            <td><?php echo esc_html($agent->wp_email); ?></td>
                            <td>
                                <?php if ($agent->phone) : ?>
                                    <a href="tel:<?php echo esc_attr($agent->phone); ?>">
                                        <?php echo esc_html($agent->phone); ?>
                                    </a>
                                <?php else : ?>
                                    <span class="description"><?php _e('غير محدد', 'hozi-akadly'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($agent->is_active) : ?>
                                    <span class="hozi-status active"><?php _e('نشط', 'hozi-akadly'); ?></span>
                                <?php else : ?>
                                    <span class="hozi-status inactive"><?php _e('غير نشط', 'hozi-akadly'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="hozi-count current"><?php echo esc_html($agent->current_orders_count); ?></span>
                                <?php if ($agent->max_orders_per_day > 0) : ?>
                                    / <?php echo esc_html($agent->max_orders_per_day); ?>
                                <?php endif; ?>
                            </td>
                            <td><span class="hozi-count confirmed"><?php echo esc_html($agent->total_confirmed); ?></span></td>
                            <td><span class="hozi-count rejected"><?php echo esc_html($agent->total_rejected); ?></span></td>
                            <td>
                                <?php if ($agent->last_assigned_at) : ?>
                                    <?php echo human_time_diff(strtotime($agent->last_assigned_at), current_time('timestamp')); ?>
                                    <?php _e('منذ', 'hozi-akadly'); ?>
                                <?php else : ?>
                                    <span class="description"><?php _e('لم يتم التخصيص بعد', 'hozi-akadly'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="hozi-agent-actions">
                                    <form method="post" style="display: inline;">
                                        <?php wp_nonce_field('hozi_agent_action'); ?>
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="agent_id" value="<?php echo esc_attr($agent->id); ?>">
                                        <button type="submit" class="button button-small">
                                            <?php echo $agent->is_active ? __('تعطيل', 'hozi-akadly') : __('تفعيل', 'hozi-akadly'); ?>
                                        </button>
                                    </form>
                                    
                                    <a href="<?php echo admin_url('admin.php?page=hozi-akadly-assignments&agent_id=' . $agent->id); ?>" 
                                       class="button button-small">
                                        <?php _e('عرض الطلبات', 'hozi-akadly'); ?>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else : ?>
            <div class="hozi-no-agents">
                <p><?php _e('لا يوجد وكلاء مسجلين حتى الآن.', 'hozi-akadly'); ?></p>
                <p><?php _e('قم بإضافة وكيل جديد باستخدام النموذج أعلاه.', 'hozi-akadly'); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.hozi-add-agent-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.hozi-add-agent-section h2 {
    margin-top: 0;
    color: #333;
}

.hozi-agents-list {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
}

.hozi-agents-list h2 {
    margin-top: 0;
    color: #333;
}

.hozi-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.hozi-status.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.hozi-status.inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hozi-count {
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
}

.hozi-count.current {
    background: #fff3cd;
    color: #856404;
}

.hozi-count.confirmed {
    background: #d4edda;
    color: #155724;
}

.hozi-count.rejected {
    background: #f8d7da;
    color: #721c24;
}

.hozi-agent-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.hozi-agent-actions .button {
    margin: 0;
}

.hozi-no-agents {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.hozi-no-agents p {
    margin-bottom: 10px;
}

@media (max-width: 768px) {
    .wp-list-table {
        font-size: 12px;
    }
    
    .hozi-agent-actions {
        flex-direction: column;
    }
    
    .hozi-agent-actions .button {
        font-size: 11px;
        padding: 2px 6px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Auto-fill agent name when user is selected
    $('#user_id').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var userName = selectedOption.text().split(' (')[0]; // Get name before email
        if (userName && userName !== 'اختر مستخدم') {
            $('#name').val(userName);
        }
    });
    
    // Confirm status toggle
    $('form input[name="action"][value="toggle_status"]').closest('form').on('submit', function(e) {
        var agentName = $(this).closest('tr').find('td:first strong').text();
        if (!confirm('هل أنت متأكد من تغيير حالة الوكيل "' + agentName + '"؟')) {
            e.preventDefault();
        }
    });
});
</script>
