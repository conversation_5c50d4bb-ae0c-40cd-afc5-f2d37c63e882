<?php
/**
 * Debug New Order #5244
 * تشخيص الطلب الجديد
 */

// WordPress environment
require_once('wp-config.php');

if (!current_user_can('manage_options')) {
    die('غير مصرح لك بالوصول');
}

echo "<h1>🔍 تشخيص الطلب الجديد #5244</h1>";

global $wpdb;

$order_id = 5244;

// Check order assignment
echo "<h2>📋 تخصيص الطلب:</h2>";

$assignment = $wpdb->get_row($wpdb->prepare(
    "SELECT
        oa.*,
        a.name as agent_name,
        a.user_id as agent_user_id
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    WHERE oa.order_id = %d",
    $order_id
));

if ($assignment) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ تخصيص الطلب موجود:</h3>";
    echo "<p><strong>رقم الطلب:</strong> #{$assignment->order_id}</p>";
    echo "<p><strong>ID الوكيل:</strong> {$assignment->agent_id}</p>";
    echo "<p><strong>اسم الوكيل:</strong> {$assignment->agent_name}</p>";
    echo "<p><strong>ID المستخدم:</strong> {$assignment->agent_user_id}</p>";
    echo "<p><strong>حالة التأكيد:</strong> {$assignment->confirmation_status}</p>";
    echo "<p><strong>تاريخ التخصيص:</strong> {$assignment->assigned_at}</p>";
    echo "<p><strong>تاريخ التأكيد:</strong> {$assignment->confirmed_at}</p>";
    echo "<p><strong>مؤرشف:</strong> " . ($assignment->archived ? 'نعم' : 'لا') . "</p>";
    echo "</div>";
} else {
    echo "<p style='color: red;'>❌ لا يوجد تخصيص لهذا الطلب!</p>";
    exit;
}

// Check WooCommerce order status
echo "<h2>🛒 حالة الطلب في WooCommerce:</h2>";

$order = wc_get_order($order_id);
if ($order) {
    echo "<p><strong>حالة الطلب:</strong> {$order->get_status()}</p>";
    echo "<p><strong>تاريخ الطلب:</strong> " . $order->get_date_created()->format('Y-m-d H:i:s') . "</p>";
    echo "<p><strong>إجمالي الطلب:</strong> " . $order->get_total() . "</p>";
    echo "<p><strong>اسم العميل:</strong> " . $order->get_billing_first_name() . " " . $order->get_billing_last_name() . "</p>";
} else {
    echo "<p style='color: red;'>❌ الطلب غير موجود في WooCommerce!</p>";
}

// Check if order is in tracking table
echo "<h2>📦 حالة التتبع:</h2>";

$tracking = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d ORDER BY created_at DESC LIMIT 1",
    $order_id
));

if ($tracking) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ الطلب موجود في جدول التتبع:</h3>";
    echo "<p><strong>ID التتبع:</strong> {$tracking->id}</p>";
    echo "<p><strong>الحالة:</strong> {$tracking->status}</p>";
    echo "<p><strong>ID الوكيل:</strong> {$tracking->agent_id}</p>";
    echo "<p><strong>تاريخ الإنشاء:</strong> {$tracking->created_at}</p>";
    echo "<p><strong>آخر تحديث:</strong> {$tracking->updated_at}</p>";
    echo "<p><strong>الملاحظات:</strong> {$tracking->notes}</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>⚠️ الطلب غير موجود في جدول التتبع!</h3>";
    echo "<p>هذا يفسر لماذا لا يظهر في صفحة متابعة التوصيل.</p>";
    echo "</div>";
}

// Check current user
echo "<h2>👤 المستخدم الحالي:</h2>";

$current_user = wp_get_current_user();
$current_agent = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d",
    $current_user->ID
));

if ($current_agent) {
    echo "<p><strong>المستخدم:</strong> {$current_user->user_login} (ID: {$current_user->ID})</p>";
    echo "<p><strong>الوكيل:</strong> {$current_agent->name} (ID: {$current_agent->id})</p>";
    
    $matches_assignment = ($current_agent->id == $assignment->agent_id);
    echo "<p><strong>يطابق تخصيص الطلب:</strong> " . ($matches_assignment ? '✅ نعم' : '❌ لا') . "</p>";
} else {
    echo "<p style='color: red;'>❌ المستخدم الحالي ليس وكيل!</p>";
}

// Test the delivery tracking query
echo "<h2>🔍 اختبار استعلام متابعة التوصيل:</h2>";

if ($current_agent) {
    $delivery_orders = $wpdb->get_results($wpdb->prepare(
        "SELECT
            oa.order_id,
            oa.confirmed_at,
            oa.notes as confirmation_notes,
            p.post_date as order_date,
            p.post_status
        FROM {$wpdb->prefix}hozi_order_assignments oa
        INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
        WHERE oa.agent_id = %d
        AND oa.confirmation_status = 'confirmed'
        AND (oa.archived IS NULL OR oa.archived = 0)
        ORDER BY oa.confirmed_at DESC
        LIMIT 50",
        $current_agent->id
    ));
    
    echo "<p><strong>عدد الطلبات المؤكدة للوكيل الحالي:</strong> " . count($delivery_orders) . "</p>";
    
    if ($delivery_orders) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم الطلب</th><th>تاريخ التأكيد</th><th>حالة WC</th><th>في التتبع؟</th></tr>";
        
        foreach ($delivery_orders as $delivery_order) {
            $in_tracking = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $delivery_order->order_id
            ));
            
            $is_current = ($delivery_order->order_id == $order_id) ? ' style="background: #ffffcc;"' : '';
            
            echo "<tr{$is_current}>";
            echo "<td>#{$delivery_order->order_id}</td>";
            echo "<td>" . date('Y/m/d H:i', strtotime($delivery_order->confirmed_at)) . "</td>";
            echo "<td>{$delivery_order->post_status}</td>";
            echo "<td>" . ($in_tracking ? '✅ نعم' : '❌ لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if our order is in the list
        $order_found = false;
        foreach ($delivery_orders as $delivery_order) {
            if ($delivery_order->order_id == $order_id) {
                $order_found = true;
                break;
            }
        }
        
        if ($order_found) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ الطلب #5244 موجود في استعلام متابعة التوصيل!</h3>";
            echo "<p>المشكلة قد تكون في عرض البيانات في الصفحة.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>❌ الطلب #5244 غير موجود في استعلام متابعة التوصيل!</h3>";
            echo "<p>تحقق من شروط الاستعلام.</p>";
            echo "</div>";
        }
    }
}

// Manual transfer to tracking
if (isset($_POST['manual_transfer'])) {
    echo "<h2>🚀 النقل اليدوي إلى جدول التتبع:</h2>";
    
    try {
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_tracking',
            array(
                'order_id' => $order_id,
                'agent_id' => $assignment->agent_id,
                'status' => 'out_for_delivery',
                'reason_category' => 'manual_transfer',
                'notes' => 'تم النقل يدوياً من صفحة التشخيص',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم النقل بنجاح!</h3>";
            echo "<p>تم إضافة الطلب #5244 إلى جدول التتبع.</p>";
            echo "<p>يجب أن يظهر الآن في صفحة متابعة التوصيل.</p>";
            echo "</div>";
            
            // Refresh to show updated data
            echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
        } else {
            echo "<p style='color: red;'>❌ فشل في النقل: " . $wpdb->last_error . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    }
}

// Check why auto transfer didn't work
echo "<h2>🔍 فحص سبب عدم النقل التلقائي:</h2>";

// Check if the hook is working
echo "<p><strong>فحص الـ hooks:</strong></p>";
echo "<ul>";
echo "<li>woocommerce_order_status_changed: " . (has_action('woocommerce_order_status_changed') ? '✅ موجود' : '❌ غير موجود') . "</li>";
echo "<li>woocommerce_order_status_on-hold: " . (has_action('woocommerce_order_status_on-hold') ? '✅ موجود' : '❌ غير موجود') . "</li>";
echo "</ul>";

// Check order status history
$order_notes = wc_get_order_notes(array('order_id' => $order_id, 'limit' => 10));
if ($order_notes) {
    echo "<h3>📝 سجل الطلب:</h3>";
    echo "<ul>";
    foreach ($order_notes as $note) {
        echo "<li>" . date('Y/m/d H:i', strtotime($note->date_created)) . ": " . strip_tags($note->content) . "</li>";
    }
    echo "</ul>";
}

?>

<hr>
<h2>🛠️ إجراءات الإصلاح:</h2>

<?php if (!$tracking): ?>
    <form method="post" style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <h3>🚀 نقل الطلب يدوياً إلى جدول التتبع:</h3>
        <p>سيتم إضافة الطلب #5244 إلى جدول التتبع ليظهر في صفحة متابعة التوصيل.</p>
        <button type="submit" name="manual_transfer" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
            🚀 نقل إلى التتبع
        </button>
    </form>
<?php else: ?>
    <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <h3>✅ الطلب موجود في جدول التتبع</h3>
        <p>يجب أن يظهر في صفحة متابعة التوصيل الآن.</p>
    </div>
<?php endif; ?>

<h2>🔗 روابط مفيدة:</h2>
<p><a href="admin.php?page=hozi-akadly-delivery-tracking" target="_blank" style="background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">📦 صفحة متابعة التوصيل</a></p>
<p><a href="post.php?post=<?php echo $order_id; ?>&action=edit" target="_blank" style="background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">📝 عرض الطلب في WooCommerce</a></p>
<p><a href="check-error-logs.php" target="_blank" style="background: #6c757d; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;">📋 فحص السجلات</a></p>
