<?php
/**
 * Agent Archived Orders Page
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap hozi-agent-archived-wrap">
    <!-- Simple Header -->
    <div class="hozi-simple-header">
        <h1><?php _e('الطلبات المؤرشفة', 'hozi-akadly'); ?></h1>
        <p class="hozi-archived-subtitle"><?php _e('الطلبات المكتملة والمؤرشفة من قبلي', 'hozi-akadly'); ?></p>
    </div>

    <!-- Navigation Tabs -->
    <div class="hozi-nav-tabs">
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-orders'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-cart"></span>
            <?php _e('الطلبات المخصصة لي', 'hozi-akadly'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-tracking'); ?>" class="hozi-nav-tab">
            <span class="dashicons dashicons-visibility"></span>
            <?php _e('تتبع طلباتي المؤكدة', 'hozi-akadly'); ?>
        </a>
        <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-archived'); ?>" class="hozi-nav-tab hozi-nav-tab-active">
            <span class="dashicons dashicons-archive"></span>
            <?php _e('الطلبات المؤرشفة', 'hozi-akadly'); ?>
        </a>
    </div>

    <!-- Archive Statistics -->
    <div class="hozi-archive-stats-section">
        <h2><?php _e('إحصائيات الأرشيف', 'hozi-akadly'); ?></h2>

        <div class="hozi-stats-grid">
            <!-- Total Archived -->
            <div class="hozi-stat-card hozi-archived-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-archive"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($archive_stats->total_archived ?? 0); ?></h3>
                    <p><?php _e('إجمالي المؤرشف', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-period"><?php _e('منذ بداية العمل', 'hozi-akadly'); ?></span>
                </div>
            </div>

            <!-- This Month -->
            <div class="hozi-stat-card hozi-month-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-calendar-alt"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($archive_stats->this_month ?? 0); ?></h3>
                    <p><?php _e('هذا الشهر', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-percentage">
                        <?php
                        $total = $archive_stats->total_archived ?? 0;
                        $month = $archive_stats->this_month ?? 0;
                        $percentage = $total > 0 ? round(($month / $total) * 100, 1) : 0;
                        printf(__('%s%% من الإجمالي', 'hozi-akadly'), $percentage);
                        ?>
                    </span>
                </div>
            </div>

            <!-- This Week -->
            <div class="hozi-stat-card hozi-week-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-clock"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($archive_stats->this_week ?? 0); ?></h3>
                    <p><?php _e('هذا الأسبوع', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-percentage">
                        <?php
                        $week_percentage = $total > 0 ? round((($archive_stats->this_week ?? 0) / $total) * 100, 1) : 0;
                        printf(__('%s%% من الإجمالي', 'hozi-akadly'), $week_percentage);
                        ?>
                    </span>
                </div>
            </div>

            <!-- Average per Day -->
            <div class="hozi-stat-card hozi-average-card">
                <div class="hozi-stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="hozi-stat-content">
                    <h3><?php echo esc_html($archive_stats->daily_average ?? 0); ?></h3>
                    <p><?php _e('متوسط يومي', 'hozi-akadly'); ?></p>
                    <span class="hozi-stat-period"><?php _e('آخر 30 يوم', 'hozi-akadly'); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Archive Actions -->
    <div class="hozi-archive-actions-section">
        <div class="hozi-actions-header">
            <h3><?php _e('إدارة الأرشيف', 'hozi-akadly'); ?></h3>
            <div class="hozi-archive-actions">
                <button type="button" class="button button-secondary" id="export-archive">
                    <span class="dashicons dashicons-download"></span>
                    <?php _e('تصدير الأرشيف', 'hozi-akadly'); ?>
                </button>
                <button type="button" class="button button-secondary" id="clear-old-archive">
                    <span class="dashicons dashicons-trash"></span>
                    <?php _e('مسح الأرشيف القديم', 'hozi-akadly'); ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Archived Orders List -->
    <div class="hozi-archived-orders-section">
        <div class="hozi-orders-header">
            <h3><?php _e('الطلبات المؤرشفة', 'hozi-akadly'); ?></h3>
            <?php if ($pagination_info && $pagination_info['total_orders'] > 0) : ?>
                <div class="hozi-pagination-info">
                    <?php printf(
                        __('عرض %d-%d من %d طلب مؤرشف', 'hozi-akadly'),
                        $pagination_info['showing_from'],
                        $pagination_info['showing_to'],
                        $pagination_info['total_orders']
                    ); ?>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!empty($archived_orders)) : ?>
            <div class="hozi-archived-orders-grid">
                <?php foreach ($archived_orders as $order_data) :
                    $order = wc_get_order($order_data->order_id);
                    if (!$order) continue;
                ?>
                    <div class="hozi-archived-order-card" data-order-id="<?php echo esc_attr($order_data->order_id); ?>">
                        <!-- Order Header -->
                        <div class="hozi-order-header">
                            <div class="hozi-order-number">
                                <strong><?php _e('طلب رقم:', 'hozi-akadly'); ?> #<?php echo esc_html($order_data->order_id); ?></strong>
                                <span class="hozi-order-date"><?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->order_date))); ?></span>
                            </div>
                            <div class="hozi-archived-badge">
                                <span class="dashicons dashicons-archive"></span>
                                <?php _e('مؤرشف', 'hozi-akadly'); ?>
                            </div>
                        </div>

                        <!-- Customer Info -->
                        <div class="hozi-customer-section">
                            <h4><?php _e('معلومات العميل', 'hozi-akadly'); ?></h4>
                            <div class="hozi-customer-details">
                                <div class="hozi-customer-name">
                                    <strong><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></strong>
                                </div>
                                <div class="hozi-customer-phone">
                                    <span class="dashicons dashicons-phone"></span>
                                    <?php echo esc_html($order->get_billing_phone()); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Order Summary -->
                        <div class="hozi-order-summary-section">
                            <div class="hozi-order-total">
                                <span class="hozi-total-label"><?php _e('المجموع:', 'hozi-akadly'); ?></span>
                                <span class="hozi-total-value"><?php echo $order->get_formatted_order_total(); ?></span>
                            </div>
                            <div class="hozi-order-items-count">
                                <span class="dashicons dashicons-cart"></span>
                                <?php printf(__('%d منتج', 'hozi-akadly'), $order->get_item_count()); ?>
                            </div>
                        </div>

                        <!-- Archive Info -->
                        <div class="hozi-archive-info-section">
                            <h4><?php _e('معلومات الأرشفة', 'hozi-akadly'); ?></h4>
                            <div class="hozi-archive-details">
                                <div class="hozi-confirmed-date">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    <?php _e('تم التأكيد:', 'hozi-akadly'); ?>
                                    <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->confirmed_at))); ?>
                                </div>
                                <div class="hozi-archived-date">
                                    <span class="dashicons dashicons-archive"></span>
                                    <?php _e('تم الأرشفة:', 'hozi-akadly'); ?>
                                    <?php echo esc_html(date_i18n('Y/m/d H:i', strtotime($order_data->archived_at))); ?>
                                </div>
                                <?php if ($order_data->notes) : ?>
                                    <div class="hozi-archive-notes">
                                        <span class="dashicons dashicons-edit"></span>
                                        <?php _e('ملاحظات:', 'hozi-akadly'); ?>
                                        <span><?php echo esc_html($order_data->notes); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="hozi-archive-actions">
                            <a href="<?php echo admin_url('post.php?post=' . $order_data->order_id . '&action=edit'); ?>"
                               class="button button-small" target="_blank">
                                <span class="dashicons dashicons-external"></span>
                                <?php _e('عرض الطلب', 'hozi-akadly'); ?>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else : ?>
            <div class="hozi-no-archived-orders">
                <div class="hozi-no-orders-icon">
                    <span class="dashicons dashicons-archive"></span>
                </div>
                <h3><?php _e('لا توجد طلبات مؤرشفة', 'hozi-akadly'); ?></h3>
                <p><?php _e('ستظهر الطلبات المؤرشفة هنا عند إكمالها', 'hozi-akadly'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=hozi-akadly-my-orders'); ?>" class="button button-primary">
                    <?php _e('العودة للطلبات المخصصة', 'hozi-akadly'); ?>
                </a>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if ($pagination_info && $pagination_info['total_pages'] > 1) : ?>
            <div class="hozi-pagination">
                <?php
                $current_page = $pagination_info['current_page'];
                $total_pages = $pagination_info['total_pages'];
                $base_url = admin_url('admin.php?page=hozi-akadly-my-archived');

                // Previous page
                if ($current_page > 1) {
                    $prev_url = add_query_arg('paged', $current_page - 1, $base_url);
                    echo '<a href="' . esc_url($prev_url) . '" class="hozi-pagination-btn hozi-pagination-prev">';
                    echo '<span class="dashicons dashicons-arrow-right-alt2"></span> ' . __('السابق', 'hozi-akadly');
                    echo '</a>';
                }

                // Page numbers
                echo '<div class="hozi-pagination-numbers">';
                for ($i = 1; $i <= $total_pages; $i++) {
                    $page_url = add_query_arg('paged', $i, $base_url);
                    $active_class = ($i == $current_page) ? ' hozi-pagination-active' : '';
                    echo '<a href="' . esc_url($page_url) . '" class="hozi-pagination-number' . $active_class . '">' . $i . '</a>';
                }
                echo '</div>';

                // Next page
                if ($current_page < $total_pages) {
                    $next_url = add_query_arg('paged', $current_page + 1, $base_url);
                    echo '<a href="' . esc_url($next_url) . '" class="hozi-pagination-btn hozi-pagination-next">';
                    echo __('التالي', 'hozi-akadly') . ' <span class="dashicons dashicons-arrow-left-alt2"></span>';
                    echo '</a>';
                }
                ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Archive Page Styles */
.hozi-agent-archived-wrap {
    max-width: 1200px;
    margin: 0 auto;
}

.hozi-simple-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px 0;
    border-bottom: 2px solid #e1e5e9;
}

.hozi-simple-header h1 {
    margin: 0 0 10px 0;
    font-size: 24px;
    color: #333;
}

.hozi-archived-subtitle {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Navigation Tabs */
.hozi-nav-tabs {
    display: flex;
    gap: 0;
    margin: 20px 0;
    border-bottom: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    text-decoration: none;
    color: #666;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    transition: all 0.3s ease;
    flex: 1;
    justify-content: center;
    font-weight: 500;
}

.hozi-nav-tab:last-child {
    border-right: none;
}

.hozi-nav-tab:hover {
    background: #e9ecef;
    color: #333;
    text-decoration: none;
}

.hozi-nav-tab-active {
    background: #007cba !important;
    color: white !important;
    font-weight: 600;
}

.hozi-nav-tab .dashicons {
    font-size: 16px;
}

/* Archive Statistics */
.hozi-archive-stats-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-archive-stats-section h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.hozi-stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease;
    border-left: 4px solid #007cba;
}

.hozi-stat-card:hover {
    transform: translateY(-2px);
}

.hozi-stat-card.hozi-archived-card { border-left-color: #6c757d; }
.hozi-stat-card.hozi-month-card { border-left-color: #28a745; }
.hozi-stat-card.hozi-week-card { border-left-color: #17a2b8; }
.hozi-stat-card.hozi-average-card { border-left-color: #ffc107; }

.hozi-stat-icon {
    font-size: 24px;
    opacity: 0.8;
    color: #666;
}

.hozi-stat-content h3 {
    font-size: 24px;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.hozi-stat-content p {
    margin: 0 0 5px 0;
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

.hozi-stat-period, .hozi-stat-percentage {
    font-size: 12px;
    color: #999;
}

/* Archive Actions */
.hozi-archive-actions-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.hozi-actions-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.hozi-archive-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.hozi-archive-actions .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Archived Orders */
.hozi-archived-orders-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hozi-orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.hozi-orders-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.hozi-pagination-info {
    background: #f8f9fa;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    border: 1px solid #e9ecef;
}

.hozi-archived-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.hozi-archived-order-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    position: relative;
    border-left: 4px solid #6c757d;
}

.hozi-order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.hozi-order-number strong {
    color: #007cba;
    font-size: 16px;
}

.hozi-order-date {
    display: block;
    color: #666;
    font-size: 12px;
    margin-top: 5px;
}

.hozi-archived-badge {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
}

.hozi-customer-section, .hozi-order-summary-section, .hozi-archive-info-section {
    margin-bottom: 15px;
}

.hozi-customer-section h4, .hozi-order-summary-section h4, .hozi-archive-info-section h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.hozi-customer-details, .hozi-archive-details {
    font-size: 13px;
    color: #666;
}

.hozi-customer-name strong {
    color: #333;
    font-size: 14px;
}

.hozi-customer-phone {
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.hozi-order-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
}

.hozi-total-value {
    font-weight: bold;
    color: #28a745;
    font-size: 16px;
}

.hozi-order-items-count {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: #666;
}

.hozi-archive-details > div {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.hozi-archive-details .dashicons {
    font-size: 14px;
    color: #666;
}

.hozi-archive-actions {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* No Orders State */
.hozi-no-archived-orders {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.hozi-no-orders-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.hozi-no-archived-orders h3 {
    color: #666;
    margin-bottom: 10px;
}

.hozi-no-archived-orders p {
    color: #999;
    margin-bottom: 20px;
}

/* Pagination */
.hozi-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #e9ecef;
}

.hozi-pagination-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-pagination-btn:hover {
    background: #f8f9fa;
    border-color: #007cba;
    color: #007cba;
    text-decoration: none;
}

.hozi-pagination-numbers {
    display: flex;
    gap: 5px;
}

.hozi-pagination-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.hozi-pagination-number:hover {
    background: #f8f9fa;
    border-color: #007cba;
    color: #007cba;
    text-decoration: none;
}

.hozi-pagination-number.hozi-pagination-active {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hozi-nav-tabs {
        flex-direction: column;
    }

    .hozi-nav-tab {
        border-right: none;
        border-bottom: 1px solid #e1e5e9;
    }

    .hozi-nav-tab:last-child {
        border-bottom: none;
    }

    .hozi-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .hozi-archived-orders-grid {
        grid-template-columns: 1fr;
    }

    .hozi-actions-header {
        flex-direction: column;
        align-items: stretch;
    }

    .hozi-archive-actions {
        justify-content: center;
    }

    .hozi-orders-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Export archive functionality
    $('#export-archive').on('click', function() {
        alert('🚧 ميزة التصدير قيد التطوير\n\nسيتم إضافتها في التحديث القادم');
    });

    // Clear old archive functionality
    $('#clear-old-archive').on('click', function() {
        if (confirm('⚠️ تحذير!\n\nهل أنت متأكد من مسح الأرشيف القديم؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
            alert('🚧 ميزة مسح الأرشيف قيد التطوير\n\nسيتم إضافتها في التحديث القادم');
        }
    });
});
</script>
