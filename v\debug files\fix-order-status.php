<?php
/**
 * Fix Order Status for Delivery Tracking
 * Convert confirmed orders to completed status
 */

// WordPress environment
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo "<h1>🔧 إصلاح حالة الطلبات للتوصيل</h1>";

global $wpdb;

// Get first active agent
$test_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 LIMIT 1");

if (!$test_agent) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ لا يوجد وكلاء نشطين!</h3>";
    echo "</div>";
    exit;
}

echo "<h2>🧪 الوكيل: {$test_agent->name} (ID: {$test_agent->id})</h2>";

// Check current user and agent mapping
$current_user = wp_get_current_user();
echo "<h3>👤 المستخدم الحالي: {$current_user->display_name} (ID: {$current_user->ID})</h3>";

// Check which agent confirmed the orders
$order_agents = $wpdb->get_results(
    "SELECT DISTINCT oa.agent_id, a.name as agent_name, COUNT(*) as order_count
     FROM {$wpdb->prefix}hozi_order_assignments oa
     LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
     WHERE oa.confirmation_status = 'confirmed'
     GROUP BY oa.agent_id, a.name"
);

echo "<h3>🔍 الوكلاء الذين أكدوا طلبات:</h3>";
if ($order_agents) {
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>ID الوكيل</th><th>اسم الوكيل</th><th>عدد الطلبات المؤكدة</th></tr>";
    foreach ($order_agents as $agent) {
        echo "<tr>";
        echo "<td>{$agent->agent_id}</td>";
        echo "<td>{$agent->agent_name}</td>";
        echo "<td>{$agent->order_count}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ لا توجد طلبات مؤكدة!</p>";
}

// Check agent-user mapping
echo "<h3>🔗 ربط الوكلاء بالمستخدمين:</h3>";
$all_agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents ORDER BY id");

if ($all_agents) {
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>ID الوكيل</th><th>اسم الوكيل</th><th>ID المستخدم</th><th>نشط</th><th>إجراء</th></tr>";
    foreach ($all_agents as $agent) {
        $is_current = ($agent->user_id == $current_user->ID) ? '✅ الحالي' : '';
        $active_status = $agent->is_active ? '✅ نشط' : '❌ غير نشط';

        echo "<tr>";
        echo "<td>{$agent->id}</td>";
        echo "<td>{$agent->name} {$is_current}</td>";
        echo "<td>{$agent->user_id}</td>";
        echo "<td>{$active_status}</td>";
        echo "<td>";

        if ($agent->user_id != $current_user->ID) {
            echo "<a href='?action=link_agent&agent_id={$agent->id}' style='background: #4caf50; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; font-size: 12px;'>🔗 ربط بي</a>";
        } else {
            echo "<span style='color: green;'>✅ مربوط</span>";
        }

        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ لا يوجد وكلاء!</p>";
}

// Get confirmed orders that are not completed
$confirmed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmation_status, oa.confirmed_at, p.post_status, p.post_date
     FROM {$wpdb->prefix}hozi_order_assignments oa
     LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND p.post_status != 'wc-completed'
     ORDER BY oa.confirmed_at DESC",
    $test_agent->id
));

echo "<h2>📋 الطلبات المؤكدة غير المكتملة:</h2>";

if ($confirmed_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة التأكيد</th><th>حالة WC الحالية</th><th>تاريخ التأكيد</th><th>إجراء</th></tr>";

    foreach ($confirmed_orders as $order) {
        $status_color = ($order->post_status === 'wc-completed') ? 'green' : 'orange';

        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td style='color: green;'>{$order->confirmation_status}</td>";
        echo "<td style='color: {$status_color};'>{$order->post_status}</td>";
        echo "<td>" . ($order->confirmed_at ? date('Y/m/d H:i', strtotime($order->confirmed_at)) : 'غير محدد') . "</td>";
        echo "<td>";

        if ($order->post_status !== 'wc-completed') {
            echo "<a href='?action=complete&order_id={$order->order_id}' style='background: #4caf50; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; font-size: 12px;'>✅ تحويل لمكتمل</a>";
        } else {
            echo "<span style='color: green;'>✅ مكتمل</span>";
        }

        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";

    if (count($confirmed_orders) > 0) {
        echo "<p><a href='?action=complete_all' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 تحويل جميع الطلبات المؤكدة إلى مكتملة</a></p>";
    }
} else {
    echo "<p>✅ جميع الطلبات المؤكدة مكتملة بالفعل!</p>";
}

// Handle actions
if (isset($_GET['action'])) {
    echo "<h2>🔧 تنفيذ الإجراء:</h2>";

    if ($_GET['action'] === 'link_agent' && isset($_GET['agent_id'])) {
        $agent_id = intval($_GET['agent_id']);
        $current_user = wp_get_current_user();

        // Update agent to link with current user
        $result = $wpdb->update(
            $wpdb->prefix . 'hozi_agents',
            array('user_id' => $current_user->ID),
            array('id' => $agent_id),
            array('%d'),
            array('%d')
        );

        if ($result !== false) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم ربط الوكيل بنجاح!</h3>";
            echo "<p><strong>المستخدم:</strong> {$current_user->display_name} (ID: {$current_user->ID})</p>";
            echo "<p><strong>الوكيل:</strong> ID {$agent_id}</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>❌ فشل في ربط الوكيل!</h3>";
            echo "</div>";
        }
    }

    if ($_GET['action'] === 'complete' && isset($_GET['order_id'])) {
        $order_id = intval($_GET['order_id']);
        $order = wc_get_order($order_id);

        if ($order) {
            $old_status = $order->get_status();
            $order->update_status('completed', 'تم تحويل الطلب إلى مكتمل لنظام متابعة التوصيل');

            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم تحديث الطلب #{$order_id}</h3>";
            echo "<p><strong>الحالة السابقة:</strong> {$old_status}</p>";
            echo "<p><strong>الحالة الجديدة:</strong> completed</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>❌ فشل في العثور على الطلب #{$order_id}</h3>";
            echo "</div>";
        }
    }

    if ($_GET['action'] === 'complete_all') {
        $updated_count = 0;

        foreach ($confirmed_orders as $order_data) {
            if ($order_data->post_status !== 'wc-completed') {
                $order = wc_get_order($order_data->order_id);
                if ($order) {
                    $order->update_status('completed', 'تم تحويل الطلب إلى مكتمل لنظام متابعة التوصيل (تحديث جماعي)');
                    $updated_count++;
                }
            }
        }

        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>✅ تم تحديث {$updated_count} طلب</h3>";
        echo "<p>جميع الطلبات المؤكدة أصبحت بحالة 'مكتمل'</p>";
        echo "</div>";
    }

    echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل الصفحة</a></p>";
}

// Show current completed orders
echo "<h2>✅ الطلبات المكتملة الحالية:</h2>";

$completed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmation_status, oa.confirmed_at, p.post_status, p.post_date
     FROM {$wpdb->prefix}hozi_order_assignments oa
     LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND p.post_status = 'wc-completed'
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $test_agent->id
));

if ($completed_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة WC</th><th>تاريخ الطلب</th><th>تاريخ التأكيد</th></tr>";

    foreach ($completed_orders as $order) {
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td style='color: green;'>{$order->post_status}</td>";
        echo "<td>" . ($order->post_date ? date('Y/m/d H:i', strtotime($order->post_date)) : 'غير محدد') . "</td>";
        echo "<td>" . ($order->confirmed_at ? date('Y/m/d H:i', strtotime($order->confirmed_at)) : 'غير محدد') . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🎉 يوجد " . count($completed_orders) . " طلب مكتمل!</h3>";
    echo "<p>هذه الطلبات يجب أن تظهر في صفحة متابعة التوصيل</p>";
    echo "</div>";
} else {
    echo "<p>❌ لا توجد طلبات مكتملة ومؤكدة</p>";
}

echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 عرض صفحة متابعة التوصيل</a></p>";
echo "<p><a href='debug-delivery-tracking.php' target='_blank' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 تشخيص النظام</a></p>";
echo "<p><a href='create-test-order.php' target='_blank' style='background: #9c27b0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🆕 إنشاء طلب اختبار</a></p>";

echo "<h2>📊 ملخص النظام:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

// Count all confirmed orders
$all_confirmed = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments
     WHERE agent_id = %d AND confirmation_status = 'confirmed'",
    $test_agent->id
));

// Count completed confirmed orders
$completed_confirmed = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_assignments oa
     LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND p.post_status = 'wc-completed'",
    $test_agent->id
));

echo "<p><strong>إجمالي الطلبات المؤكدة:</strong> {$all_confirmed}</p>";
echo "<p><strong>الطلبات المؤكدة والمكتملة:</strong> {$completed_confirmed}</p>";
echo "<p><strong>الطلبات التي تحتاج تحديث:</strong> " . ($all_confirmed - $completed_confirmed) . "</p>";

if ($completed_confirmed > 0) {
    echo "<p style='color: green;'><strong>✅ النظام جاهز!</strong> يجب أن تظهر الطلبات في صفحة متابعة التوصيل</p>";
} else {
    echo "<p style='color: orange;'><strong>⚠️ لا توجد طلبات جاهزة للتوصيل</strong></p>";
}

echo "</div>";
?>
