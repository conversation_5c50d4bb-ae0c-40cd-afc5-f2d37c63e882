<?php
/**
 * Update Order Assignments Table
 * Add columns to track admin confirmations separately from agent confirmations
 */

// Function to update order assignments database
function hozi_update_admin_confirmation_tracking() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'hozi_order_assignments';
    
    // Check if table exists first
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if (!$table_exists) {
        return "❌ Table {$table_name} does not exist";
    }

    $results = array();

    // Check if confirmed_by_admin column exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'confirmed_by_admin'");

    if (empty($column_exists)) {
        $sql = "ALTER TABLE {$table_name} ADD COLUMN confirmed_by_admin tinyint(1) DEFAULT 0 AFTER notes";
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            $results[] = "✅ Added confirmed_by_admin column";
        } else {
            $results[] = "❌ Failed to add confirmed_by_admin column: " . $wpdb->last_error;
        }
    } else {
        $results[] = "✅ confirmed_by_admin column already exists";
    }

    // Check if admin_user_id column exists
    $admin_user_id_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'admin_user_id'");

    if (empty($admin_user_id_exists)) {
        $sql = "ALTER TABLE {$table_name} ADD COLUMN admin_user_id int(11) DEFAULT NULL AFTER confirmed_by_admin";
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            $results[] = "✅ Added admin_user_id column";
        } else {
            $results[] = "❌ Failed to add admin_user_id column: " . $wpdb->last_error;
        }
    } else {
        $results[] = "✅ admin_user_id column already exists";
    }

    // Check if original_agent_id column exists
    $original_agent_id_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'original_agent_id'");

    if (empty($original_agent_id_exists)) {
        $sql = "ALTER TABLE {$table_name} ADD COLUMN original_agent_id int(11) DEFAULT NULL AFTER admin_user_id";
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            $results[] = "✅ Added original_agent_id column";
        } else {
            $results[] = "❌ Failed to add original_agent_id column: " . $wpdb->last_error;
        }
    } else {
        $results[] = "✅ original_agent_id column already exists";
    }

    // Add indexes for new columns
    $indexes = $wpdb->get_results("SHOW INDEX FROM {$table_name} WHERE Key_name = 'confirmed_by_admin'");
    if (empty($indexes)) {
        $sql = "ALTER TABLE {$table_name} ADD KEY confirmed_by_admin (confirmed_by_admin)";
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            $results[] = "✅ Added index for confirmed_by_admin column";
        } else {
            $results[] = "❌ Failed to add index: " . $wpdb->last_error;
        }
    } else {
        $results[] = "✅ Index for confirmed_by_admin already exists";
    }

    return implode("\n", $results);
}

// Auto-run if accessed with update_admin_tracking parameter
if (isset($_GET['update_admin_tracking']) && $_GET['update_admin_tracking'] == '1') {
    echo '<div style="background: white; padding: 20px; margin: 20px; border: 1px solid #ccc; border-radius: 5px;">';
    echo '<h2>🔄 تحديث تتبع تأكيدات المدير</h2>';
    echo '<pre style="background: #f5f5f5; padding: 15px; border-radius: 3px;">';
    echo hozi_update_admin_confirmation_tracking();
    echo '</pre>';
    echo '<p><a href="' . admin_url('admin.php?page=hozi-akadly-settings') . '" class="button button-primary">العودة للإعدادات</a></p>';
    echo '</div>';
}
?>