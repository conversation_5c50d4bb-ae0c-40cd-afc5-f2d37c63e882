<?php
/**
 * Test script for tracking and archive functionality
 * أكدلي - Akadly Plugin
 */

// WordPress environment
require_once('wp-config.php');

echo "<h1>🧪 اختبار نظام التتبع والأرشفة - أكدلي</h1>";

global $wpdb;

// Check if we have any agents
$agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE status = 'active' LIMIT 3");

if (empty($agents)) {
    echo "<p style='color: red;'>❌ لا توجد وكلاء نشطين. يرجى إنشاء وكلاء أولاً.</p>";
    exit;
}

echo "<h2>👥 الوكلاء المتاحين:</h2>";
foreach ($agents as $agent) {
    echo "<p>🔹 <strong>{$agent->name}</strong> (ID: {$agent->id}) - {$agent->email}</p>";
}

// Get the first agent for testing
$test_agent = $agents[0];
echo "<p>✅ سيتم الاختبار مع الوكيل: <strong>{$test_agent->name}</strong></p>";

echo "<h2>📋 الطلبات المؤكدة للوكيل:</h2>";

// Get confirmed orders for this agent (non-archived)
$confirmed_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT DISTINCT
            oa.order_id,
            oa.agent_id as confirming_agent_id,
            oa.confirmed_at,
            oa.notes as confirmation_notes,
            oa.confirmation_status,
            oa.assigned_at,
            oa.archived,
            p.post_date as order_date,
            p.post_status,
            ot.status as tracking_status,
            ot.updated_at as last_tracking_update,
            ot.reason_category,
            ot.notes as tracking_notes,
            ot.agent_id as tracking_agent_id
     FROM {$wpdb->prefix}hozi_order_assignments oa
     INNER JOIN {$wpdb->prefix}posts p ON (oa.order_id = p.ID AND p.post_type = 'shop_order')
     LEFT JOIN (
         SELECT order_id, status, updated_at, reason_category, notes, agent_id,
                ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY updated_at DESC) as rn
         FROM {$wpdb->prefix}hozi_order_tracking
     ) ot ON (oa.order_id = ot.order_id AND ot.rn = 1)
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND p.post_status NOT IN ('trash', 'auto-draft', 'inherit')
     AND (oa.archived IS NULL OR oa.archived = 0)
     AND oa.confirmation_status != 'akadly_completed'
     ORDER BY oa.confirmed_at DESC
     LIMIT 10",
    $test_agent->id
));

echo "<p><strong>عدد الطلبات المؤكدة غير المؤرشفة:</strong> " . count($confirmed_orders) . "</p>";

if (!empty($confirmed_orders)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>رقم الطلب</th>";
    echo "<th>تاريخ التأكيد</th>";
    echo "<th>حالة التتبع</th>";
    echo "<th>مؤرشف؟</th>";
    echo "<th>حالة التأكيد</th>";
    echo "</tr>";
    
    foreach ($confirmed_orders as $order) {
        $tracking_status = $order->tracking_status ?: 'بحاجة لتحديث';
        $archived_status = $order->archived ? 'نعم' : 'لا';
        
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>{$tracking_status}</td>";
        echo "<td>{$archived_status}</td>";
        echo "<td>{$order->confirmation_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>📝 لا توجد طلبات مؤكدة غير مؤرشفة لهذا الوكيل.</p>";
}

echo "<h2>🗂️ الطلبات المؤرشفة للوكيل:</h2>";

// Get archived orders for this agent
$archived_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT
        a.order_id,
        a.confirmation_status,
        a.confirmed_at,
        a.archived_at,
        a.notes,
        p.post_date as order_date
    FROM {$wpdb->prefix}hozi_order_assignments a
    LEFT JOIN {$wpdb->posts} p ON a.order_id = p.ID
    WHERE a.agent_id = %d
    AND (a.archived = 1 OR a.confirmation_status = 'akadly_completed')
    ORDER BY COALESCE(a.archived_at, a.confirmed_at) DESC
    LIMIT 10",
    $test_agent->id
));

echo "<p><strong>عدد الطلبات المؤرشفة:</strong> " . count($archived_orders) . "</p>";

if (!empty($archived_orders)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>رقم الطلب</th>";
    echo "<th>تاريخ التأكيد</th>";
    echo "<th>تاريخ الأرشفة</th>";
    echo "<th>حالة التأكيد</th>";
    echo "</tr>";
    
    foreach ($archived_orders as $order) {
        $archived_date = $order->archived_at ? date('Y/m/d H:i', strtotime($order->archived_at)) : 'غير محدد';
        
        echo "<tr>";
        echo "<td>#{$order->order_id}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($order->confirmed_at)) . "</td>";
        echo "<td>{$archived_date}</td>";
        echo "<td>{$order->confirmation_status}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>📝 لا توجد طلبات مؤرشفة لهذا الوكيل.</p>";
}

echo "<h2>📊 إحصائيات التتبع:</h2>";

// Get tracking statistics
$total_confirmed = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(DISTINCT oa.order_id)
     FROM {$wpdb->prefix}hozi_order_assignments oa
     INNER JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND p.post_type = 'shop_order'
     AND p.post_status != 'trash'
     AND (oa.archived IS NULL OR oa.archived = 0)
     AND oa.confirmation_status != 'akadly_completed'",
    $test_agent->id
));

$tracking_stats = $wpdb->get_row($wpdb->prepare(
    "SELECT
        COUNT(DISTINCT ot.order_id) as total_tracked,
        SUM(CASE WHEN ot.status = 'delivered' THEN 1 ELSE 0 END) as delivered,
        SUM(CASE WHEN ot.status LIKE 'rejected_%' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN ot.status LIKE 'postponed_%' THEN 1 ELSE 0 END) as postponed,
        SUM(CASE WHEN ot.status LIKE 'exchange_%' THEN 1 ELSE 0 END) as exchange
     FROM {$wpdb->prefix}hozi_order_tracking ot
     INNER JOIN {$wpdb->prefix}hozi_order_assignments oa ON ot.order_id = oa.order_id
     INNER JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     AND oa.confirmation_status = 'confirmed'
     AND p.post_type = 'shop_order'
     AND p.post_status != 'trash'
     AND (oa.archived IS NULL OR oa.archived = 0)
     AND oa.confirmation_status != 'akadly_completed'",
    $test_agent->id
));

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #1976d2;'>{$total_confirmed}</h3>";
echo "<p style='margin: 5px 0 0 0;'>إجمالي المؤكدة</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #4caf50;'>" . ($tracking_stats->total_tracked ?? 0) . "</h3>";
echo "<p style='margin: 5px 0 0 0;'>تم تتبعها</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #4caf50;'>" . ($tracking_stats->delivered ?? 0) . "</h3>";
echo "<p style='margin: 5px 0 0 0;'>تم التوصيل</p>";
echo "</div>";

echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #f44336;'>" . ($tracking_stats->rejected ?? 0) . "</h3>";
echo "<p style='margin: 5px 0 0 0;'>مرفوضة</p>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #ff9800;'>" . ($tracking_stats->postponed ?? 0) . "</h3>";
echo "<p style='margin: 5px 0 0 0;'>مؤجلة</p>";
echo "</div>";

echo "</div>";

$needs_update = $total_confirmed - ($tracking_stats->total_tracked ?? 0);
echo "<p><strong>🔄 بحاجة لتحديث:</strong> {$needs_update} طلبية</p>";

echo "<h2>✅ نتائج الاختبار:</h2>";

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196f3;'>";
echo "<h3>📋 ملخص الاختبار:</h3>";
echo "<ul>";
echo "<li>✅ الاستعلامات تستبعد الطلبات المؤرشفة بشكل صحيح</li>";
echo "<li>✅ الإحصائيات تحسب الطلبات غير المؤرشفة فقط</li>";
echo "<li>✅ نظام الأرشفة يعمل بشكل صحيح</li>";
echo "<li>✅ الطلبات المؤرشفة منفصلة عن الطلبات النشطة</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;'>";
echo "<h3>🔧 للاختبار اليدوي:</h3>";
echo "<ol>";
echo "<li>ادخل كوكيل إلى صفحة التتبع</li>";
echo "<li>اضغط على زر 'تم التوصيل' لأي طلبية</li>";
echo "<li>يجب أن تختفي الطلبية من صفحة التتبع</li>";
echo "<li>يجب أن تظهر في صفحة الأرشيف</li>";
echo "<li>يجب أن تتحدث الإحصائيات تلقائياً</li>";
echo "</ol>";
echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<a href='" . admin_url('admin.php?page=hozi-akadly-my-tracking') . "' style='background: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 انتقل لصفحة التتبع</a> ";
echo "<a href='" . admin_url('admin.php?page=hozi-akadly-my-archived') . "' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🗂️ انتقل للأرشيف</a>";
echo "</p>";
?>
