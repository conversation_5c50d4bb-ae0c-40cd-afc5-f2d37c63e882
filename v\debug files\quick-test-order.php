<?php
/**
 * Quick Test Order Creator
 * إنشاء طلب اختبار سريع
 */

// WordPress environment
require_once('wp-config.php');
require_once(ABSPATH . 'wp-admin/includes/admin.php');

// Security check
if (!current_user_can('manage_options')) {
    die('غير مصرح لك بالوصول');
}

echo "<h1>⚡ إنشاء طلب اختبار سريع - أكدلي</h1>";

global $wpdb;

// Handle order creation
if (isset($_POST['create_order'])) {
    
    echo "<h2>🔧 إنشاء طلب اختبار...</h2>";
    
    try {
        // Create a test order
        $order = wc_create_order();
        
        if ($order) {
            // Add a simple product (or create one if needed)
            $products = wc_get_products(array('limit' => 1, 'status' => 'publish'));
            if (!empty($products)) {
                $product = $products[0];
                $order->add_product($product, 1);
                echo "<p>✅ تم إضافة المنتج: {$product->get_name()}</p>";
            } else {
                // Create a simple test product
                $product = new WC_Product_Simple();
                $product->set_name('منتج اختبار أكدلي');
                $product->set_status('publish');
                $product->set_catalog_visibility('visible');
                $product->set_description('منتج اختبار لنظام أكدلي');
                $product->set_sku('test-akadly-' . time());
                $product->set_price(100);
                $product->set_regular_price(100);
                $product->set_manage_stock(false);
                $product->set_stock_status('instock');
                $product_id = $product->save();
                $order->add_product($product, 1);
                echo "<p>✅ تم إنشاء وإضافة منتج اختبار</p>";
            }
            
            // Set billing details
            $order->set_billing_first_name('عميل');
            $order->set_billing_last_name('اختبار');
            $order->set_billing_phone('0123456789');
            $order->set_billing_email('<EMAIL>');
            $order->set_billing_address_1('عنوان اختبار');
            $order->set_billing_city('الرياض');
            $order->set_billing_country('SA');
            
            // Set shipping details (same as billing)
            $order->set_shipping_first_name('عميل');
            $order->set_shipping_last_name('اختبار');
            $order->set_shipping_address_1('عنوان اختبار');
            $order->set_shipping_city('الرياض');
            $order->set_shipping_country('SA');
            
            // Calculate totals
            $order->calculate_totals();
            
            // Set order status to processing (as per new workflow)
            $order->set_status('processing');
            
            // Save the order
            $order->save();
            
            $order_id = $order->get_id();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; border: 2px solid #4caf50;'>";
            echo "<h3>✅ تم إنشاء طلب الاختبار بنجاح!</h3>";
            echo "<p><strong>رقم الطلب:</strong> #{$order_id}</p>";
            echo "<p><strong>حالة الطلب:</strong> {$order->get_status()}</p>";
            echo "<p><strong>إجمالي الطلب:</strong> " . $order->get_total() . " ريال</p>";
            echo "</div>";
            
            // Now assign it to an agent
            $agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE status = 'active' LIMIT 1");
            
            if ($agent) {
                // Assign order to agent
                $order_distributor = new Hozi_Akadly_Order_Distributor();
                $assignment_result = $order_distributor->assign_order($order_id, $agent->id, 'manual');
                
                if ($assignment_result) {
                    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 8px; margin: 20px 0; border: 2px solid #007cba;'>";
                    echo "<h3>👤 تم تخصيص الطلب للوكيل:</h3>";
                    echo "<p><strong>اسم الوكيل:</strong> {$agent->name}</p>";
                    echo "<p><strong>ID الوكيل:</strong> {$agent->id}</p>";
                    echo "</div>";
                    
                    echo "<h2>🧪 اختبار التأكيد الآن:</h2>";
                    echo "<p><a href='test-confirmation-flow.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🧪 اختبار تدفق التأكيد</a></p>";
                    echo "<p><a href='admin.php?page=hozi-akadly-my-orders' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📋 فتح لوحة الوكيل</a></p>";
                    
                } else {
                    echo "<p style='color: red;'>❌ فشل في تخصيص الطلب للوكيل</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ لا يوجد وكلاء نشطين لتخصيص الطلب</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء الطلب</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    }
}

// Show current status
echo "<h2>📊 الحالة الحالية:</h2>";

// Count pending orders
$pending_orders = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->prefix}hozi_order_assignments 
    WHERE confirmation_status = 'pending_confirmation'
");

echo "<p><strong>الطلبات في انتظار التأكيد:</strong> {$pending_orders}</p>";

// Count confirmed orders
$confirmed_orders = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->prefix}hozi_order_assignments 
    WHERE confirmation_status = 'confirmed' 
    AND (archived IS NULL OR archived = 0)
");

echo "<p><strong>الطلبات المؤكدة:</strong> {$confirmed_orders}</p>";

// Count tracking orders
$tracking_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_order_tracking");
echo "<p><strong>الطلبات في التتبع:</strong> {$tracking_orders}</p>";

// Count active agents
$active_agents = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}hozi_agents WHERE status = 'active'");
echo "<p><strong>الوكلاء النشطين:</strong> {$active_agents}</p>";

if ($pending_orders == 0) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border: 2px solid #ffc107;'>";
    echo "<h3>⚠️ لا توجد طلبات للاختبار</h3>";
    echo "<p>أنشئ طلب اختبار جديد لتجربة نظام التأكيد</p>";
    echo "</div>";
}

// Show recent orders
echo "<h2>📋 آخر الطلبات:</h2>";

$recent_orders = $wpdb->get_results("
    SELECT oa.order_id, oa.confirmation_status, oa.assigned_at, a.name as agent_name, p.post_status
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
    ORDER BY oa.assigned_at DESC
    LIMIT 5
");

if ($recent_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>الوكيل</th><th>حالة التأكيد</th><th>حالة WC</th><th>تاريخ التخصيص</th></tr>";
    
    foreach ($recent_orders as $recent_order) {
        echo "<tr>";
        echo "<td>#{$recent_order->order_id}</td>";
        echo "<td>{$recent_order->agent_name}</td>";
        echo "<td>{$recent_order->confirmation_status}</td>";
        echo "<td>{$recent_order->post_status}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($recent_order->assigned_at)) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات.</p>";
}

?>

<form method="post" style="margin: 20px 0;">
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
        <h3>🛒 إنشاء طلب اختبار جديد</h3>
        <p>سيتم إنشاء طلب اختبار وتخصيصه تلقائياً لأول وكيل نشط</p>
        <button type="submit" name="create_order" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">
            ✨ إنشاء طلب اختبار
        </button>
    </div>
</form>

<hr>
<h2>🔗 روابط مفيدة:</h2>
<p><a href="check-error-logs.php" style="background: #6c757d; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;">📋 فحص السجلات</a></p>
<p><a href="debug-delivery-new.php" style="background: #ff9800; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;">🔍 أداة التشخيص</a></p>
<p><a href="test-confirmation-flow.php" style="background: #007cba; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;">🧪 اختبار التأكيد</a></p>
<p><a href="admin.php?page=hozi-akadly-agents" style="background: #17a2b8; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;">👥 إدارة الوكلاء</a></p>
