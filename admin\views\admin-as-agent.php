<?php
/**
 * Admin as Agent View - Allows admin to work as an agent
 */

if (!defined('ABSPATH')) {
    exit;
}

// Start session if not already started
if (!session_id()) {
    session_start();
}
?>

<div class="wrap hozi-admin-as-agent-wrap">
    <!-- Header -->
    <div class="hozi-admin-agent-header">
        <div class="hozi-header-content">
            <div class="hozi-header-icon">
                <i class="dashicons dashicons-admin-users"></i>
            </div>
            <div class="hozi-header-text">
                <h1 class="hozi-main-title">🎭 العمل كوكيل</h1>
                <p class="hozi-subtitle">يمكن للمشرف العمل نيابة عن أي وكيل في حالة غيابه</p>
            </div>
        </div>
    </div>

    <div class="hozi-admin-agent-content">
        <!-- Agent Selection -->
        <div class="hozi-agent-selection-section">
            <div class="hozi-section-header">
                <h2>👤 اختيار الوكيل</h2>
                <p>اختر الوكيل الذي تريد العمل نيابة عنه</p>
            </div>

            <form method="post" class="hozi-agent-selection-form">
                <?php wp_nonce_field('hozi_akadly_nonce', 'hozi_akadly_nonce'); ?>

                <div class="hozi-form-row">
                    <label for="agent_id">الوكيل:</label>
                    <select name="agent_id" id="agent_id" required>
                        <option value="">-- اختر الوكيل --</option>
                        <?php foreach ($agents as $agent): ?>
                            <option value="<?php echo esc_attr($agent->id); ?>"
                                    <?php selected($selected_agent_id, $agent->id); ?>>
                                <?php echo esc_html($agent->name); ?>
                                <?php if ($agent->phone): ?>
                                    (<?php echo esc_html($agent->phone); ?>)
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <button type="submit" name="select_agent" class="button button-primary">
                        <span class="dashicons dashicons-admin-users"></span>
                        اختيار الوكيل
                    </button>
                </div>
            </form>
        </div>

        <?php if ($selected_agent && !empty($pending_orders)): ?>
            <!-- Selected Agent Info -->
            <div class="hozi-selected-agent-info">
                <div class="hozi-agent-card">
                    <div class="hozi-agent-avatar">
                        <i class="dashicons dashicons-admin-users"></i>
                    </div>
                    <div class="hozi-agent-details">
                        <h3><?php echo esc_html($selected_agent->name); ?></h3>
                        <p>📞 <?php echo esc_html($selected_agent->phone); ?></p>
                        <p>📧 <?php echo esc_html($selected_agent->email); ?></p>
                    </div>
                    <div class="hozi-admin-badge">
                        <span class="hozi-badge-text">🎭 المشرف يعمل نيابة عن هذا الوكيل</span>
                    </div>
                </div>
            </div>

            <!-- Agent Statistics -->
            <?php if ($agent_stats): ?>
                <div class="hozi-agent-stats-section">
                    <h3>📊 إحصائيات الوكيل</h3>
                    <div class="hozi-stats-grid">
                        <div class="hozi-stat-card hozi-stat-pending">
                            <div class="hozi-stat-icon">
                                <i class="dashicons dashicons-clock"></i>
                            </div>
                            <div class="hozi-stat-content">
                                <div class="hozi-stat-number"><?php echo intval($agent_stats->pending ?? 0); ?></div>
                                <div class="hozi-stat-label">في الانتظار</div>
                            </div>
                        </div>

                        <div class="hozi-stat-card hozi-stat-confirmed">
                            <div class="hozi-stat-icon">
                                <i class="dashicons dashicons-yes-alt"></i>
                            </div>
                            <div class="hozi-stat-content">
                                <div class="hozi-stat-number"><?php echo intval($agent_stats->confirmed ?? 0); ?></div>
                                <div class="hozi-stat-label">مؤكد</div>
                            </div>
                        </div>

                        <div class="hozi-stat-card hozi-stat-rejected">
                            <div class="hozi-stat-icon">
                                <i class="dashicons dashicons-dismiss"></i>
                            </div>
                            <div class="hozi-stat-content">
                                <div class="hozi-stat-number"><?php echo intval($agent_stats->rejected ?? 0); ?></div>
                                <div class="hozi-stat-label">مرفوض</div>
                            </div>
                        </div>

                        <div class="hozi-stat-card hozi-stat-no-answer">
                            <div class="hozi-stat-icon">
                                <i class="dashicons dashicons-phone"></i>
                            </div>
                            <div class="hozi-stat-content">
                                <div class="hozi-stat-number"><?php echo intval($agent_stats->no_answer ?? 0); ?></div>
                                <div class="hozi-stat-label">لم يرد</div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Orders Section -->
            <div class="hozi-orders-section">
                <div class="hozi-section-header">
                    <h2>📦 الطلبات المخصصة للوكيل</h2>
                    <p>الطلبات التي تحتاج إلى تأكيد من الوكيل المختار</p>
                </div>

                <?php if ($pagination_info && $pagination_info['total_orders'] > 0): ?>
                    <div class="hozi-pagination-info">
                        <span>عرض <?php echo $pagination_info['showing_from']; ?>-<?php echo $pagination_info['showing_to']; ?> من <?php echo $pagination_info['total_orders']; ?> طلب</span>
                    </div>
                <?php endif; ?>

                <div class="hozi-orders-grid">
                    <?php foreach ($pending_orders as $assignment):
                        $order = wc_get_order($assignment->order_id);
                        if (!$order) continue;

                        $customer_id = $order->get_customer_id();
                        $customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
                        $customer_phone = $order->get_billing_phone();
                        $customer_email = $order->get_billing_email();
                        $order_total = $order->get_total();
                        $order_date = $order->get_date_created();
                        $order_items = $order->get_items();
                    ?>
                        <div class="hozi-order-card">
                            <!-- Order Header -->
                            <div class="hozi-order-header">
                                <div class="hozi-order-number">
                                    <strong>طلب #<?php echo $order->get_id(); ?></strong>
                                    <span class="hozi-order-date"><?php echo $order_date->date_i18n('Y/m/d H:i'); ?></span>
                                </div>
                                <div class="hozi-order-total">
                                    <span class="hozi-total-amount"><?php echo wc_price($order_total); ?></span>
                                </div>
                            </div>

                            <!-- Customer Info -->
                            <div class="hozi-customer-info">
                                <h4>👤 معلومات العميل</h4>
                                <div class="hozi-customer-details">
                                    <div class="hozi-customer-item">
                                        <span class="hozi-label">الاسم:</span>
                                        <span class="hozi-value"><?php echo esc_html($customer_name); ?></span>
                                    </div>
                                    <div class="hozi-customer-item">
                                        <span class="hozi-label">الهاتف:</span>
                                        <span class="hozi-value">
                                            <a href="tel:<?php echo esc_attr($customer_phone); ?>" class="hozi-phone-link">
                                                📞 <?php echo esc_html($customer_phone); ?>
                                            </a>
                                        </span>
                                    </div>
                                    <?php if ($customer_email): ?>
                                        <div class="hozi-customer-item">
                                            <span class="hozi-label">البريد:</span>
                                            <span class="hozi-value"><?php echo esc_html($customer_email); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Order Items -->
                            <div class="hozi-order-items">
                                <h4>🛍️ المنتجات</h4>
                                <div class="hozi-items-list">
                                    <?php foreach ($order_items as $item): ?>
                                        <div class="hozi-item">
                                            <span class="hozi-item-name"><?php echo esc_html($item->get_name()); ?></span>
                                            <span class="hozi-item-qty">الكمية: <?php echo $item->get_quantity(); ?></span>
                                            <span class="hozi-item-total"><?php echo wc_price($item->get_total()); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Confirmation Form -->
                            <div class="hozi-confirmation-section">
                                <form method="post" class="hozi-confirmation-form">
                                    <?php wp_nonce_field('hozi_akadly_nonce', 'hozi_akadly_nonce'); ?>
                                    <input type="hidden" name="order_id" value="<?php echo $order->get_id(); ?>">
                                    <input type="hidden" name="update_confirmation" value="1">

                                    <div class="hozi-admin-notice">
                                        <i class="dashicons dashicons-info"></i>
                                        <span>أنت تعمل كمشرف نيابة عن الوكيل: <strong><?php echo esc_html($selected_agent->name); ?></strong></span>
                                    </div>

                                    <div class="hozi-notes-section">
                                        <label for="notes_<?php echo $order->get_id(); ?>">📝 ملاحظات:</label>
                                        <textarea name="notes" id="notes_<?php echo $order->get_id(); ?>"
                                                  placeholder="أضف ملاحظات حول المكالمة أو تفاصيل إضافية..."></textarea>
                                    </div>

                                    <div class="hozi-action-buttons">
                                        <button type="submit" name="status" value="confirmed" class="button button-primary hozi-btn-confirm">
                                            <span class="dashicons dashicons-yes"></span>
                                            تم التأكيد
                                        </button>

                                        <button type="submit" name="status" value="no_answer" class="button hozi-btn-no-answer">
                                            <span class="dashicons dashicons-phone"></span>
                                            لم يرد
                                        </button>

                                        <button type="submit" name="status" value="callback_later" class="button hozi-btn-callback">
                                            <span class="dashicons dashicons-clock"></span>
                                            إعادة الاتصال
                                        </button>

                                        <button type="submit" name="status" value="rejected" class="button hozi-btn-reject">
                                            <span class="dashicons dashicons-dismiss"></span>
                                            رفض
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($pagination_info && $pagination_info['total_pages'] > 1): ?>
                    <div class="hozi-pagination">
                        <?php
                        $base_url = admin_url('admin.php?page=hozi-akadly-admin-as-agent&agent_id=' . $selected_agent_id);

                        if ($pagination_info['current_page'] > 1): ?>
                            <a href="<?php echo $base_url . '&paged=' . ($pagination_info['current_page'] - 1); ?>"
                               class="button">« السابق</a>
                        <?php endif; ?>

                        <span class="hozi-page-info">
                            صفحة <?php echo $pagination_info['current_page']; ?> من <?php echo $pagination_info['total_pages']; ?>
                        </span>

                        <?php if ($pagination_info['current_page'] < $pagination_info['total_pages']): ?>
                            <a href="<?php echo $base_url . '&paged=' . ($pagination_info['current_page'] + 1); ?>"
                               class="button">التالي »</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

        <?php elseif ($selected_agent && empty($pending_orders)): ?>
            <!-- No Orders Message -->
            <div class="hozi-no-orders">
                <div class="hozi-no-orders-icon">
                    <i class="dashicons dashicons-yes-alt"></i>
                </div>
                <h3>✅ لا توجد طلبات في الانتظار</h3>
                <p>الوكيل <strong><?php echo esc_html($selected_agent->name); ?></strong> ليس لديه طلبات تحتاج إلى تأكيد حالياً.</p>
            </div>

        <?php elseif (!$selected_agent_id): ?>
            <!-- No Agent Selected -->
            <div class="hozi-no-agent-selected">
                <div class="hozi-no-agent-icon">
                    <i class="dashicons dashicons-admin-users"></i>
                </div>
                <h3>👤 اختر وكيل للبدء</h3>
                <p>اختر الوكيل الذي تريد العمل نيابة عنه من القائمة أعلاه.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* ===== ADMIN AS AGENT STYLES ===== */
.hozi-admin-as-agent-wrap {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: -20px -20px -20px -2px;
    padding: 0;
}

.hozi-admin-agent-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    margin-bottom: 30px;
}

.hozi-header-content {
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.hozi-header-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 25px;
    backdrop-filter: blur(10px);
}

.hozi-header-icon .dashicons {
    font-size: 40px;
    color: white;
}

.hozi-main-title {
    font-size: 2.5em;
    margin: 0;
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hozi-subtitle {
    font-size: 1.1em;
    margin: 10px 0 0 0;
    opacity: 0.9;
}

.hozi-admin-agent-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px 30px;
}

/* ===== AGENT SELECTION ===== */
.hozi-agent-selection-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.hozi-section-header h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.5em;
}

.hozi-section-header p {
    margin: 0 0 20px 0;
    color: #666;
}

.hozi-form-row {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.hozi-form-row label {
    font-weight: 600;
    color: #333;
}

.hozi-form-row select {
    min-width: 300px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
}

.hozi-form-row select:focus {
    border-color: #667eea;
    outline: none;
}

/* ===== SELECTED AGENT INFO ===== */
.hozi-selected-agent-info {
    margin-bottom: 30px;
}

.hozi-agent-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border: 2px solid #667eea;
}

.hozi-agent-avatar {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
}

.hozi-agent-avatar .dashicons {
    font-size: 35px;
    color: white;
}

.hozi-agent-details {
    flex: 1;
}

.hozi-agent-details h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.3em;
}

.hozi-agent-details p {
    margin: 5px 0;
    color: #666;
}

.hozi-admin-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    text-align: center;
}

/* ===== AGENT STATS ===== */
.hozi-agent-stats-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.hozi-agent-stats-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.3em;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.hozi-stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.hozi-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.hozi-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}

.hozi-stat-pending .hozi-stat-icon { background: linear-gradient(135deg, #FF9800, #f57c00); }
.hozi-stat-confirmed .hozi-stat-icon { background: linear-gradient(135deg, #4CAF50, #45a049); }
.hozi-stat-rejected .hozi-stat-icon { background: linear-gradient(135deg, #f44336, #d32f2f); }
.hozi-stat-no-answer .hozi-stat-icon { background: linear-gradient(135deg, #9C27B0, #7b1fa2); }

.hozi-stat-icon .dashicons {
    font-size: 24px;
    color: white;
}

.hozi-stat-number {
    font-size: 2em;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.hozi-stat-label {
    font-size: 0.9em;
    color: #666;
    font-weight: 600;
}

/* ===== ORDERS SECTION ===== */
.hozi-orders-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.hozi-pagination-info {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    color: #666;
    font-weight: 600;
}

.hozi-orders-grid {
    display: grid;
    gap: 25px;
}

.hozi-order-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.hozi-order-card:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.hozi-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.hozi-order-number strong {
    color: #667eea;
    font-size: 1.2em;
}

.hozi-order-date {
    display: block;
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
}

.hozi-total-amount {
    font-size: 1.3em;
    font-weight: 700;
    color: #27ae60;
}

/* ===== CUSTOMER INFO ===== */
.hozi-customer-info h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1em;
}

.hozi-customer-details {
    display: grid;
    gap: 10px;
}

.hozi-customer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hozi-label {
    font-weight: 600;
    color: #666;
}

.hozi-value {
    color: #333;
}

.hozi-phone-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.hozi-phone-link:hover {
    color: #5a67d8;
}

/* ===== ORDER ITEMS ===== */
.hozi-order-items {
    margin: 20px 0;
}

.hozi-order-items h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1em;
}

.hozi-items-list {
    display: grid;
    gap: 10px;
}

.hozi-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 15px;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
}

.hozi-item-name {
    font-weight: 600;
    color: #333;
}

.hozi-item-qty {
    color: #666;
    font-size: 0.9em;
}

.hozi-item-total {
    font-weight: 700;
    color: #27ae60;
}

/* ===== CONFIRMATION SECTION ===== */
.hozi-confirmation-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.hozi-admin-notice {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.hozi-notes-section {
    margin-bottom: 20px;
}

.hozi-notes-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.hozi-notes-section textarea {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    resize: vertical;
    font-family: inherit;
}

.hozi-notes-section textarea:focus {
    border-color: #667eea;
    outline: none;
}

.hozi-action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.hozi-action-buttons button {
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.hozi-btn-confirm {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    border-color: #4CAF50 !important;
}

.hozi-btn-no-answer {
    background: linear-gradient(135deg, #9C27B0, #7b1fa2) !important;
    border-color: #9C27B0 !important;
    color: white !important;
}

.hozi-btn-callback {
    background: linear-gradient(135deg, #FF9800, #f57c00) !important;
    border-color: #FF9800 !important;
    color: white !important;
}

.hozi-btn-reject {
    background: linear-gradient(135deg, #f44336, #d32f2f) !important;
    border-color: #f44336 !important;
    color: white !important;
}

.hozi-action-buttons button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* ===== NO ORDERS/AGENT MESSAGES ===== */
.hozi-no-orders,
.hozi-no-agent-selected {
    background: white;
    border-radius: 15px;
    padding: 60px 30px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.hozi-no-orders-icon,
.hozi-no-agent-icon {
    width: 80px;
    height: 80px;
    background: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.hozi-no-orders-icon .dashicons,
.hozi-no-agent-icon .dashicons {
    font-size: 40px;
    color: #ccc;
}

.hozi-no-orders h3,
.hozi-no-agent-selected h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.5em;
}

.hozi-no-orders p,
.hozi-no-agent-selected p {
    margin: 0;
    color: #666;
    font-size: 1.1em;
}

/* ===== PAGINATION ===== */
.hozi-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.hozi-page-info {
    font-weight: 600;
    color: #666;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .hozi-admin-agent-content {
        padding: 0 15px 15px;
    }

    .hozi-header-content {
        flex-direction: column;
        text-align: center;
    }

    .hozi-header-icon {
        margin: 0 0 20px 0;
    }

    .hozi-form-row {
        flex-direction: column;
        align-items: stretch;
    }

    .hozi-form-row select {
        min-width: auto;
    }

    .hozi-agent-card {
        flex-direction: column;
        text-align: center;
    }

    .hozi-agent-avatar {
        margin: 0 0 20px 0;
    }

    .hozi-stats-grid {
        grid-template-columns: 1fr;
    }

    .hozi-order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .hozi-customer-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .hozi-item {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .hozi-action-buttons {
        grid-template-columns: 1fr;
    }
}
</style>
