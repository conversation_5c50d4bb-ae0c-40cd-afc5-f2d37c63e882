# 🔧 حل مشاكل نظام متابعة التوصيل - Akadly

## 🚨 المشكلة: الطلبات لا تنتقل إلى متابعة التوصيل بعد التأكيد

### 📋 الأسباب المحتملة:

#### 1. **جدول قاعدة البيانات مفقود**
- جدول `hozi_order_tracking` غير موجود
- **الحل:** إعادة تفعيل الإضافة أو استخدام ملف التشخيص

#### 2. **أذونات متابعة التوصيل معطلة**
- نظام متابعة التوصيل معطل من الإعدادات
- **الحل:** تفعيل النظام من الإعدادات

#### 3. **صلاحيات الوصول غير صحيحة**
- إعدادات الوصول لا تسمح للوكيل بالرؤية
- **الحل:** تعديل إعدادات الوصول

---

## 🛠️ خطوات الحل السريع:

### الخطوة 1: فحص الإعدادات
1. انتقل إلى **أكدلي > الإعدادات**
2. ابحث عن قسم **"إعدادات متابعة التوصيل"**
3. تأكد من:
   - ✅ **تفعيل نظام متابعة التوصيل:** مفعل
   - ✅ **صلاحيات الوصول:** الوكيل المخصص فقط (أو حسب احتياجاتك)

### الخطوة 2: استخدام ملف التشخيص
1. ارفع ملف `debug-delivery-tracking.php` إلى مجلد WordPress الرئيسي
2. افتح الملف في المتصفح: `yoursite.com/debug-delivery-tracking.php`
3. اتبع التعليمات في الملف لإصلاح المشاكل

### الخطوة 3: إعادة تفعيل الإضافة
1. انتقل إلى **الإضافات > الإضافات المثبتة**
2. **عطل** إضافة Akadly
3. **فعل** إضافة Akadly مرة أخرى
4. هذا سيعيد إنشاء جداول قاعدة البيانات

### الخطوة 4: اختبار النظام
1. أنشئ طلب تجريبي جديد
2. اذهب إلى لوحة الوكيل وأكد الطلب
3. تحقق من ظهور الطلب في صفحة **متابعة التوصيل**

---

## 🔍 فحص تفصيلي للمشاكل:

### فحص جدول قاعدة البيانات:
```sql
-- تشغيل في phpMyAdmin أو أي أداة قاعدة بيانات
SHOW TABLES LIKE 'wp_hozi_order_tracking';
```

### فحص الطلبات المؤكدة:
```sql
-- عرض الطلبات المؤكدة التي لا تظهر في التتبع
SELECT oa.order_id, oa.agent_id, oa.confirmation_status, ot.id as tracking_id
FROM wp_hozi_order_assignments oa
LEFT JOIN wp_hozi_order_tracking ot ON oa.order_id = ot.order_id
WHERE oa.confirmation_status = 'confirmed' AND ot.id IS NULL;
```

### فحص سجلات الأخطاء:
- ابحث في ملف `wp-content/debug.log` عن رسائل تحتوي على "Hozi Akadly"
- ابحث عن أخطاء متعلقة بقاعدة البيانات

---

## 📞 الحصول على المساعدة:

### معلومات مطلوبة عند طلب الدعم:
1. **إصدار WordPress:** `<?php echo get_bloginfo('version'); ?>`
2. **إصدار WooCommerce:** `<?php echo WC()->version; ?>`
3. **إصدار Akadly:** `<?php echo HOZI_AKADLY_VERSION; ?>`
4. **نتائج ملف التشخيص:** `debug-delivery-tracking.php`
5. **رسائل الخطأ:** من ملف `debug.log`

### قنوات الدعم:
- **واتساب:** +213771067105
- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** https://hostazi.shop

---

## 🎯 نصائح لتجنب المشاكل:

### 1. النسخ الاحتياطي
- احتفظ بنسخة احتياطية من قاعدة البيانات قبل التحديثات
- احتفظ بنسخة احتياطية من ملفات الإضافة

### 2. التحديثات
- لا تحدث WordPress أو WooCommerce بدون اختبار
- اختبر الإضافة بعد كل تحديث

### 3. المراقبة
- راقب سجلات الأخطاء بانتظام
- اختبر النظام دورياً بطلبات تجريبية

### 4. الإعدادات
- لا تغير إعدادات متابعة التوصيل أثناء وجود طلبات نشطة
- اختبر الإعدادات الجديدة على موقع تجريبي أولاً

---

## 🔄 إعادة تعيين النظام (الحل الأخير):

إذا لم تنجح الحلول السابقة:

### 1. حفظ البيانات المهمة
```sql
-- نسخ احتياطية للجداول المهمة
CREATE TABLE backup_hozi_agents AS SELECT * FROM wp_hozi_agents;
CREATE TABLE backup_hozi_order_assignments AS SELECT * FROM wp_hozi_order_assignments;
```

### 2. إعادة إنشاء الجداول
```sql
-- حذف الجداول المشكلة
DROP TABLE IF EXISTS wp_hozi_order_tracking;
```

### 3. إعادة تفعيل الإضافة
- عطل الإضافة
- فعل الإضافة مرة أخرى

### 4. استعادة البيانات إذا لزم الأمر
```sql
-- استعادة البيانات المهمة
INSERT INTO wp_hozi_agents SELECT * FROM backup_hozi_agents;
INSERT INTO wp_hozi_order_assignments SELECT * FROM backup_hozi_order_assignments;
```

---

**تم إعداد هذا الدليل بواسطة:** فريق Hostazi  
**التاريخ:** يناير 2025  
**الإصدار:** 1.0.0
