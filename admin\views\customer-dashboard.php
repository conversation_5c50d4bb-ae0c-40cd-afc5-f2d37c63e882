<?php
/**
 * Customer Dashboard View - Admin Area
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get current customer
$customer_id = get_current_user_id();
$customer_info = get_userdata($customer_id);

if (!$customer_info) {
    wp_die(__('لم يتم العثور على بيانات العميل.', 'hozi-akadly'));
}

// Get customer orders with confirmation status
$customer_orders_class = new Hozi_Akadly_Customer_Orders();
$filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : 'all';

// Map filter to status
$status_map = array(
    'all' => 'all',
    'confirmed' => 'confirmed',
    'pending' => 'pending_confirmation',
    'cancelled' => 'rejected',
    'no-answer' => 'no_answer',
    'callback' => 'callback_later'
);

$status = $status_map[$filter] ?? 'all';

// Pagination settings
$per_page = 10;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// Get orders and counts with pagination
$orders = $customer_orders_class->get_customer_orders_with_confirmation($customer_id, $status, $per_page, $offset);
$total_orders = $customer_orders_class->get_customer_orders_count($customer_id, $status);
$counts = $customer_orders_class->get_customer_order_counts($customer_id);

// Calculate pagination info
$total_pages = ceil($total_orders / $per_page);
$pagination_info = array(
    'current_page' => $current_page,
    'total_pages' => $total_pages,
    'per_page' => $per_page,
    'total_orders' => $total_orders,
    'showing_from' => $offset + 1,
    'showing_to' => min($offset + $per_page, $total_orders)
);
?>

<div class="wrap hozi-customer-dashboard">
    <!-- Beautiful Header -->
    <div class="hozi-customer-header">
        <div class="hozi-header-content">
            <div class="hozi-header-icon">
                <span class="dashicons dashicons-admin-users"></span>
            </div>
            <div class="hozi-header-text">
                <h1 class="hozi-main-title">أكدلي - Akadly</h1>
                <p class="hozi-subtitle">لوحة تحكم العميل</p>
            </div>
        </div>
        <div class="hozi-customer-info">
            <div class="customer-welcome">
                <h2>مرحباً، <?php echo esc_html($customer_info->display_name); ?></h2>
                <p><?php echo esc_html($customer_info->user_email); ?></p>
            </div>
        </div>
    </div>

    <!-- Customer Statistics -->
    <div class="hozi-customer-stats">
        <div class="hozi-stats-grid">
            <div class="hozi-stat-card total">
                <div class="hozi-stat-icon">
                    <i class="dashicons dashicons-cart"></i>
                </div>
                <div class="hozi-stat-content">
                    <div class="hozi-stat-number"><?php echo esc_html($counts->total); ?></div>
                    <div class="hozi-stat-label">إجمالي الطلبات</div>
                </div>
            </div>

            <div class="hozi-stat-card confirmed">
                <div class="hozi-stat-icon">
                    <i class="dashicons dashicons-yes-alt"></i>
                </div>
                <div class="hozi-stat-content">
                    <div class="hozi-stat-number"><?php echo esc_html($counts->confirmed); ?></div>
                    <div class="hozi-stat-label">طلبات مؤكدة</div>
                </div>
            </div>

            <div class="hozi-stat-card pending">
                <div class="hozi-stat-icon">
                    <i class="dashicons dashicons-clock"></i>
                </div>
                <div class="hozi-stat-content">
                    <div class="hozi-stat-number"><?php echo esc_html($counts->pending); ?></div>
                    <div class="hozi-stat-label">في انتظار التأكيد</div>
                </div>
            </div>

            <div class="hozi-stat-card rejected">
                <div class="hozi-stat-icon">
                    <i class="dashicons dashicons-dismiss"></i>
                </div>
                <div class="hozi-stat-content">
                    <div class="hozi-stat-number"><?php echo esc_html($counts->rejected); ?></div>
                    <div class="hozi-stat-label">طلبات ملغية</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Buttons -->
    <div class="hozi-filter-section">
        <h3>تصفية الطلبات</h3>
        <div class="hozi-filter-buttons">
            <a href="<?php echo admin_url('admin.php?page=hozi-akadly-customer-dashboard'); ?>"
               class="filter-btn <?php echo $filter === 'all' ? 'active' : ''; ?>">
                جميع الطلبات (<?php echo $counts->total; ?>)
            </a>
            <a href="<?php echo admin_url('admin.php?page=hozi-akadly-customer-dashboard&filter=pending'); ?>"
               class="filter-btn pending <?php echo $filter === 'pending' ? 'active' : ''; ?>">
                في انتظار التأكيد (<?php echo $counts->pending; ?>)
            </a>
            <a href="<?php echo admin_url('admin.php?page=hozi-akadly-customer-dashboard&filter=confirmed'); ?>"
               class="filter-btn confirmed <?php echo $filter === 'confirmed' ? 'active' : ''; ?>">
                ✅ تم التأكيد (<?php echo $counts->confirmed; ?>)
            </a>
            <a href="<?php echo admin_url('admin.php?page=hozi-akadly-customer-dashboard&filter=cancelled'); ?>"
               class="filter-btn cancelled <?php echo $filter === 'cancelled' ? 'active' : ''; ?>">
                ❌ تم الإلغاء (<?php echo $counts->rejected; ?>)
            </a>
            <a href="<?php echo admin_url('admin.php?page=hozi-akadly-customer-dashboard&filter=callback'); ?>"
               class="filter-btn callback <?php echo $filter === 'callback' ? 'active' : ''; ?>">
                🔄 تم التأجيل (<?php echo $counts->callback; ?>)
            </a>
        </div>
    </div>

    <!-- Orders List -->
    <div class="hozi-orders-section">
        <h3>طلباتي</h3>

        <?php if (!empty($orders)) : ?>
            <div class="hozi-orders-table">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th scope="col">رقم الطلب</th>
                            <th scope="col">تاريخ الطلب</th>
                            <th scope="col">المبلغ</th>
                            <th scope="col">حالة الطلب</th>
                            <th scope="col">حالة التأكيد</th>
                            <th scope="col">التاريخ والملاحظات</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order_data) :
                            $order = wc_get_order($order_data->order_id);
                            if (!$order) continue;
                        ?>
                            <tr>
                                <td>
                                    <strong>#<?php echo esc_html($order->get_order_number()); ?></strong>
                                </td>
                                <td>
                                    <?php echo esc_html($order->get_date_created()->format('Y/m/d H:i')); ?>
                                </td>
                                <td>
                                    <strong><?php echo wc_price($order->get_total()); ?></strong>
                                </td>
                                <td>
                                    <span class="order-status status-<?php echo esc_attr($order->get_status()); ?>">
                                        <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $confirmation_status = $order_data->confirmation_status ?: 'pending_confirmation';
                                    $status_labels = array(
                                        'pending_confirmation' => '⏳ في انتظار التأكيد',
                                        'confirmed' => '✅ تم التأكيد',
                                        'rejected' => '❌ تم الإلغاء',
                                        'no_answer' => '📞 لم يرد',
                                        'callback_later' => '🔄 تم التأجيل'
                                    );
                                    ?>
                                    <span class="confirmation-status status-<?php echo esc_attr($confirmation_status); ?>">
                                        <?php echo $status_labels[$confirmation_status] ?? 'غير محدد'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($order_data->confirmed_at) : ?>
                                        <span class="confirmation-date">
                                            <?php echo esc_html(date('Y/m/d H:i', strtotime($order_data->confirmed_at))); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($order_data->notes) : ?>
                                        <div class="order-notes">
                                            <strong>ملاحظة:</strong> <?php echo esc_html($order_data->notes); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo esc_url($order->get_view_order_url()); ?>"
                                       class="button button-small" target="_blank">
                                        عرض التفاصيل
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($pagination_info && $pagination_info['total_pages'] > 1) : ?>
                <div class="hozi-pagination">
                    <div class="pagination-info">
                        <span>
                            عرض <?php echo $pagination_info['showing_from']; ?> - <?php echo $pagination_info['showing_to']; ?>
                            من <?php echo $pagination_info['total_orders']; ?> طلب
                        </span>
                    </div>

                    <div class="pagination-links">
                        <?php
                        $current_page = $pagination_info['current_page'];
                        $total_pages = $pagination_info['total_pages'];

                        // Build base URL
                        $base_url = admin_url('admin.php?page=hozi-akadly-customer-dashboard');
                        $url_params = array();
                        if (isset($_GET['filter']) && $_GET['filter'] !== 'all') {
                            $url_params['filter'] = $_GET['filter'];
                        }

                        // Previous page
                        if ($current_page > 1) {
                            $prev_url = add_query_arg(array_merge($url_params, array('paged' => $current_page - 1)), $base_url);
                            echo '<a href="' . esc_url($prev_url) . '" class="pagination-link prev">« السابق</a>';
                        }

                        // Page numbers
                        $start_page = max(1, $current_page - 2);
                        $end_page = min($total_pages, $current_page + 2);

                        if ($start_page > 1) {
                            $first_url = add_query_arg($url_params, $base_url);
                            echo '<a href="' . esc_url($first_url) . '" class="pagination-link">1</a>';
                            if ($start_page > 2) {
                                echo '<span class="pagination-dots">...</span>';
                            }
                        }

                        for ($i = $start_page; $i <= $end_page; $i++) {
                            if ($i == $current_page) {
                                echo '<span class="pagination-link current">' . $i . '</span>';
                            } else {
                                $page_url = add_query_arg(array_merge($url_params, array('paged' => $i)), $base_url);
                                echo '<a href="' . esc_url($page_url) . '" class="pagination-link">' . $i . '</a>';
                            }
                        }

                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<span class="pagination-dots">...</span>';
                            }
                            $last_url = add_query_arg(array_merge($url_params, array('paged' => $total_pages)), $base_url);
                            echo '<a href="' . esc_url($last_url) . '" class="pagination-link">' . $total_pages . '</a>';
                        }

                        // Next page
                        if ($current_page < $total_pages) {
                            $next_url = add_query_arg(array_merge($url_params, array('paged' => $current_page + 1)), $base_url);
                            echo '<a href="' . esc_url($next_url) . '" class="pagination-link next">التالي »</a>';
                        }
                        ?>
                    </div>
                </div>
            <?php endif; ?>

        <?php else : ?>
            <div class="hozi-no-orders">
                <div class="notice notice-info">
                    <p>لا توجد طلبات <?php echo $filter !== 'all' ? 'بهذا التصنيف' : ''; ?> حالياً.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.hozi-customer-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
.hozi-customer-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.hozi-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.hozi-header-icon {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hozi-header-icon .dashicons {
    font-size: 30px;
}

.hozi-main-title {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
}

.hozi-subtitle {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

.hozi-customer-info {
    text-align: right;
}

.customer-welcome h2 {
    margin: 0;
    font-size: 24px;
}

.customer-welcome p {
    margin: 5px 0 0 0;
    opacity: 0.8;
}

/* Statistics Cards */
.hozi-customer-stats {
    margin-bottom: 30px;
}

.hozi-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.hozi-stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.hozi-stat-card:hover {
    transform: translateY(-5px);
}

.hozi-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.hozi-stat-card.total .hozi-stat-icon {
    background: #3498db;
}

.hozi-stat-card.confirmed .hozi-stat-icon {
    background: #2ecc71;
}

.hozi-stat-card.pending .hozi-stat-icon {
    background: #f39c12;
}

.hozi-stat-card.rejected .hozi-stat-icon {
    background: #e74c3c;
}

.hozi-stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #2c3e50;
}

.hozi-stat-label {
    color: #7f8c8d;
    font-size: 14px;
}

/* Filter Section */
.hozi-filter-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.hozi-filter-section h3 {
    margin-top: 0;
    color: #2c3e50;
}

.hozi-filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #007cba;
    border-color: #007cba;
    color: white;
    text-decoration: none;
}

.filter-btn.confirmed:hover,
.filter-btn.confirmed.active {
    background: #2ecc71;
    border-color: #2ecc71;
}

.filter-btn.cancelled:hover,
.filter-btn.cancelled.active {
    background: #e74c3c;
    border-color: #e74c3c;
}

.filter-btn.callback:hover,
.filter-btn.callback.active {
    background: #f39c12;
    border-color: #f39c12;
}

/* Orders Section */
.hozi-orders-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.hozi-orders-section h3 {
    margin-top: 0;
    color: #2c3e50;
}

.hozi-orders-table table {
    border-radius: 8px;
    overflow: hidden;
}

.confirmation-status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

.confirmation-status.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.confirmation-status.status-pending_confirmation {
    background: #fff3cd;
    color: #856404;
}

.confirmation-status.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.confirmation-status.status-no_answer {
    background: #ffeaa7;
    color: #6c5ce7;
}

.confirmation-status.status-callback_later {
    background: #ddd6fe;
    color: #5b21b6;
}

.order-notes {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

.confirmation-date {
    font-size: 12px;
    color: #666;
}

.hozi-no-orders {
    text-align: center;
    padding: 40px;
}

/* Pagination Styles */
.hozi-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #ddd;
    margin-top: 20px;
    border-radius: 0 0 12px 12px;
}

.pagination-info {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.pagination-links {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-link {
    display: inline-block;
    padding: 8px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
    min-width: 40px;
    text-align: center;
}

.pagination-link:hover {
    background: #007cba;
    color: white;
    border-color: #007cba;
    text-decoration: none;
}

.pagination-link.current {
    background: #007cba;
    color: white;
    border-color: #007cba;
    font-weight: bold;
}

.pagination-link.prev,
.pagination-link.next {
    font-weight: bold;
    min-width: auto;
    padding: 8px 16px;
}

.pagination-dots {
    padding: 8px 4px;
    color: #666;
    font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
    .hozi-customer-header {
        flex-direction: column;
        text-align: center;
    }

    .hozi-filter-buttons {
        justify-content: center;
    }

    .hozi-orders-table {
        overflow-x: auto;
    }

    .hozi-pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .pagination-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .pagination-link {
        padding: 6px 10px;
        font-size: 13px;
        min-width: 35px;
    }

    .pagination-link.prev,
    .pagination-link.next {
        padding: 6px 12px;
    }
}
</style>
