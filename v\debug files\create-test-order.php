<?php
/**
 * Create Test Order for Delivery Tracking
 * Quick tool to create a test order that should appear in delivery tracking
 */

// WordPress environment
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo "<h1>🆕 إنشاء طلب اختبار لمتابعة التوصيل</h1>";

global $wpdb;

// Get first active agent
$test_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 LIMIT 1");

if (!$test_agent) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ لا يوجد وكلاء نشطين!</h3>";
    echo "<p>يجب إنشاء وكيل نشط أولاً.</p>";
    echo "</div>";
    exit;
}

echo "<h2>🧪 الوكيل المستخدم: {$test_agent->name} (ID: {$test_agent->id})</h2>";

if (isset($_GET['action']) && $_GET['action'] === 'create') {
    echo "<h2>🔧 إنشاء طلب اختبار:</h2>";
    
    try {
        // Create a test order
        $order = wc_create_order();
        $order->set_billing_first_name('عميل');
        $order->set_billing_last_name('اختبار التوصيل');
        $order->set_billing_phone('01234567890');
        $order->set_billing_email('<EMAIL>');
        $order->set_billing_address_1('عنوان اختبار التوصيل - شارع النيل');
        $order->set_billing_city('القاهرة');
        $order->set_billing_country('EG');
        
        // Add a simple product
        $products = wc_get_products(array('limit' => 1));
        if (!empty($products)) {
            $product = $products[0];
            $order->add_product($product, 1);
            echo "<p>✅ تم إضافة المنتج: {$product->get_name()}</p>";
        } else {
            // Create a simple product if none exists
            $product = new WC_Product_Simple();
            $product->set_name('منتج اختبار التوصيل');
            $product->set_regular_price(100);
            $product->set_status('publish');
            $product->save();
            $order->add_product($product, 1);
            echo "<p>✅ تم إنشاء وإضافة منتج اختبار</p>";
        }
        
        $order->calculate_totals();
        $order->set_status('completed'); // Set as completed directly
        $order->save();
        
        $order_id = $order->get_id();
        
        echo "<p>✅ تم إنشاء طلب اختبار: #{$order_id}</p>";
        echo "<p>📊 إجمالي الطلب: " . wc_price($order->get_total()) . "</p>";
        echo "<p>📅 تاريخ الطلب: " . $order->get_date_created()->format('Y-m-d H:i:s') . "</p>";
        echo "<p>🎯 حالة الطلب: " . $order->get_status() . "</p>";
        
        // Create confirmed assignment
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'order_id' => $order_id,
                'agent_id' => $test_agent->id,
                'confirmation_status' => 'confirmed',
                'assigned_at' => current_time('mysql'),
                'confirmed_at' => current_time('mysql'),
                'assignment_method' => 'manual',
                'archived' => 0,
                'notes' => 'طلب اختبار لنظام متابعة التوصيل - تم إنشاؤه تلقائياً'
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%d', '%s')
        );
        
        if ($result) {
            echo "<p>✅ تم إنشاء تخصيص مؤكد للوكيل</p>";
            echo "<p>📝 ملاحظات التخصيص: طلب اختبار لنظام متابعة التوصيل</p>";
            
            // Add order note
            $order->add_order_note(
                sprintf(
                    '🧪 طلب اختبار لنظام متابعة التوصيل%s%s%s',
                    "\n👤 الوكيل: " . $test_agent->name,
                    "\n✅ تم التأكيد تلقائياً",
                    "\n📦 جاهز لاختبار متابعة التوصيل"
                ),
                0
            );
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>🎉 تم إنشاء الطلب بنجاح!</h3>";
            echo "<p><strong>رقم الطلب:</strong> #{$order_id}</p>";
            echo "<p><strong>الحالة:</strong> مكتمل ومؤكد من الوكيل</p>";
            echo "<p><strong>يجب أن يظهر الآن في:</strong> صفحة متابعة التوصيل</p>";
            echo "</div>";
            
            echo "<h3>🔗 روابط مفيدة:</h3>";
            echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 عرض في متابعة التوصيل</a></p>";
            echo "<p><a href='post.php?post={$order_id}&action=edit' target='_blank' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📝 عرض الطلب في WooCommerce</a></p>";
            echo "<p><a href='debug-delivery-tracking.php' target='_blank' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 تشخيص النظام</a></p>";
            
        } else {
            echo "<p>❌ فشل في إنشاء التخصيص: " . $wpdb->last_error . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ خطأ في إنشاء الطلب: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Show existing test orders
echo "<h2>📋 الطلبات الموجودة للوكيل:</h2>";

$existing_orders = $wpdb->get_results($wpdb->prepare(
    "SELECT oa.order_id, oa.confirmation_status, oa.confirmed_at, p.post_status, p.post_date
     FROM {$wpdb->prefix}hozi_order_assignments oa
     LEFT JOIN {$wpdb->prefix}posts p ON oa.order_id = p.ID
     WHERE oa.agent_id = %d
     ORDER BY oa.assigned_at DESC
     LIMIT 10",
    $test_agent->id
));

if ($existing_orders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>حالة التأكيد</th><th>حالة WC</th><th>تاريخ الطلب</th><th>تاريخ التأكيد</th><th>إجراءات</th></tr>";
    
    foreach ($existing_orders as $existing_order) {
        $confirmation_color = ($existing_order->confirmation_status === 'confirmed') ? 'green' : 'orange';
        $wc_status_color = ($existing_order->post_status === 'wc-completed') ? 'green' : 'orange';
        
        echo "<tr>";
        echo "<td>#{$existing_order->order_id}</td>";
        echo "<td style='color: {$confirmation_color};'>{$existing_order->confirmation_status}</td>";
        echo "<td style='color: {$wc_status_color};'>{$existing_order->post_status}</td>";
        echo "<td>" . ($existing_order->post_date ? date('Y/m/d H:i', strtotime($existing_order->post_date)) : 'غير محدد') . "</td>";
        echo "<td>" . ($existing_order->confirmed_at ? date('Y/m/d H:i', strtotime($existing_order->confirmed_at)) : 'غير مؤكد') . "</td>";
        echo "<td>";
        
        if ($existing_order->confirmation_status === 'confirmed' && $existing_order->post_status === 'wc-completed') {
            echo "<span style='color: green;'>✅ جاهز للتوصيل</span>";
        } else {
            echo "<span style='color: orange;'>⏳ غير جاهز</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد طلبات للوكيل.</p>";
}

echo "<h2>🔧 إجراءات:</h2>";
echo "<p><a href='?action=create' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🆕 إنشاء طلب اختبار جديد</a></p>";
echo "<p><a href='admin.php?page=hozi-akadly-delivery-tracking' target='_blank' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>📦 عرض صفحة متابعة التوصيل</a></p>";
echo "<p><a href='debug-delivery-tracking.php' target='_blank' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔍 تشخيص النظام</a></p>";
echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل</a></p>";

echo "<h2>📊 معلومات النظام:</h2>";

// Check system status
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";

// Check if delivery tracking table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}hozi_delivery_tracking'");
echo "<p><strong>جدول متابعة التوصيل:</strong> " . ($table_exists ? '✅ موجود' : '❌ غير موجود') . "</p>";

// Check WooCommerce
echo "<p><strong>WooCommerce:</strong> " . (class_exists('WooCommerce') ? '✅ نشط' : '❌ غير نشط') . "</p>";

// Check agent manager
echo "<p><strong>مدير الوكلاء:</strong> " . (class_exists('Hozi_Akadly_Agent_Manager') ? '✅ متوفر' : '❌ غير متوفر') . "</p>";

echo "</div>";
?>
