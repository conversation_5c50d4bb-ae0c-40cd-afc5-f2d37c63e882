<?php
/**
 * Admin Menu Manager for Hozi Akadly
 * Manages admin menu visibility for different user types
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Admin_Menu_Manager {

    /**
     * Constructor
     */
    public function __construct() {
        // Hide admin menu items for agents
        add_action('admin_menu', array($this, 'hide_admin_menu_items'), 999);

        // Hide admin bar items for agents
        add_action('wp_before_admin_bar_render', array($this, 'hide_admin_bar_items'), 999);

        // Remove dashboard widgets for agents
        add_action('wp_dashboard_setup', array($this, 'remove_dashboard_widgets'), 999);

        // Redirect agents to their dashboard
        add_action('admin_init', array($this, 'redirect_agents_to_dashboard'));

        // Add custom CSS for agents
        add_action('admin_head', array($this, 'add_agent_admin_css'));

        // Hide admin notices for agents
        add_action('admin_head', array($this, 'hide_admin_notices_for_agents'));
    }

    /**
     * Hide admin menu items for agents
     */
    public function hide_admin_menu_items() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Only apply to agents, not admins
        if (!$agent_manager->is_agent() || current_user_can('manage_options')) {
            return;
        }

        // Remove all default WordPress menu items
        remove_menu_page('index.php');                  // Dashboard
        remove_menu_page('edit.php');                   // Posts
        remove_menu_page('upload.php');                 // Media
        remove_menu_page('edit.php?post_type=page');    // Pages
        remove_menu_page('edit-comments.php');          // Comments
        remove_menu_page('themes.php');                 // Appearance
        remove_menu_page('plugins.php');                // Plugins
        remove_menu_page('users.php');                  // Users
        remove_menu_page('tools.php');                  // Tools
        remove_menu_page('options-general.php');        // Settings

        // Remove WooCommerce menu items
        remove_menu_page('woocommerce');                // WooCommerce
        remove_menu_page('edit.php?post_type=product'); // Products
        remove_menu_page('wc-admin&path=/analytics/overview'); // Analytics

        // Remove other common plugin menus
        remove_menu_page('elementor');                  // Elementor
        remove_menu_page('edit.php?post_type=elementor_library'); // Elementor Templates
        remove_menu_page('wpcf7');                      // Contact Form 7
        remove_menu_page('wpseo_dashboard');            // Yoast SEO
        remove_menu_page('rank-math');                  // Rank Math
        remove_menu_page('jetpack');                    // Jetpack
        remove_menu_page('edit.php?post_type=shop_order'); // WooCommerce Orders
        remove_menu_page('edit.php?post_type=shop_coupon'); // WooCommerce Coupons
        remove_menu_page('wc-reports');                 // WooCommerce Reports
        remove_menu_page('wc-settings');                // WooCommerce Settings
        remove_menu_page('wc-status');                  // WooCommerce Status
        remove_menu_page('wc-addons');                  // WooCommerce Extensions

        // Remove other common menus
        remove_menu_page('edit.php?post_type=acf-field-group'); // ACF
        remove_menu_page('mailchimp-for-wp');           // Mailchimp
        remove_menu_page('smush');                      // Smush
        remove_menu_page('wp-optimize');                // WP Optimize
        remove_menu_page('updraftplus');                // UpdraftPlus
        remove_menu_page('wordfence');                  // Wordfence
        remove_menu_page('sucuri');                     // Sucuri
        remove_menu_page('itsec');                      // iThemes Security
        remove_menu_page('backwpup');                   // BackWPup
        remove_menu_page('duplicator');                 // Duplicator
        remove_menu_page('wp-mail-smtp');               // WP Mail SMTP
        remove_menu_page('edit.php?post_type=popup');   // Popup Maker
        remove_menu_page('edit.php?post_type=slider');  // Revolution Slider
        remove_menu_page('revslider');                  // Revolution Slider
        remove_menu_page('LayerSlider');                // LayerSlider
        remove_menu_page('edit.php?post_type=testimonial'); // Testimonials
        remove_menu_page('edit.php?post_type=portfolio'); // Portfolio
        remove_menu_page('edit.php?post_type=team');    // Team Members
        remove_menu_page('edit.php?post_type=event');   // Events
        remove_menu_page('edit.php?post_type=faq');     // FAQ
        remove_menu_page('edit.php?post_type=service'); // Services

        // Remove submenu items that might still appear
        global $submenu;

        // Keep only profile.php (حسابك) in users menu
        add_menu_page(
            __('حسابك', 'hozi-akadly'),
            __('حسابك', 'hozi-akadly'),
            'read',
            'profile.php',
            '',
            'dashicons-admin-users',
            70
        );

        // Remove users submenu items
        if (isset($submenu['users.php'])) {
            unset($submenu['users.php']);
        }
    }

    /**
     * Hide admin bar items for agents
     */
    public function hide_admin_bar_items() {
        global $wp_admin_bar;

        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Only apply to agents, not admins
        if (!$agent_manager->is_agent() || current_user_can('manage_options')) {
            return;
        }

        // Remove admin bar items
        $wp_admin_bar->remove_node('wp-logo');          // WordPress logo
        $wp_admin_bar->remove_node('about');            // About WordPress
        $wp_admin_bar->remove_node('wporg');            // WordPress.org
        $wp_admin_bar->remove_node('documentation');    // Documentation
        $wp_admin_bar->remove_node('support-forums');   // Support Forums
        $wp_admin_bar->remove_node('feedback');         // Feedback
        $wp_admin_bar->remove_node('site-name');        // Site name
        $wp_admin_bar->remove_node('view-site');        // View site
        $wp_admin_bar->remove_node('dashboard');        // Dashboard
        $wp_admin_bar->remove_node('themes');           // Themes
        $wp_admin_bar->remove_node('widgets');          // Widgets
        $wp_admin_bar->remove_node('menus');            // Menus
        $wp_admin_bar->remove_node('customize');        // Customize
        $wp_admin_bar->remove_node('updates');          // Updates
        $wp_admin_bar->remove_node('comments');         // Comments
        $wp_admin_bar->remove_node('new-content');      // New content
        $wp_admin_bar->remove_node('wpseo-menu');       // Yoast SEO
        $wp_admin_bar->remove_node('rank-math');        // Rank Math
        $wp_admin_bar->remove_node('elementor_edit_page'); // Elementor

        // Keep only user account menu
        // $wp_admin_bar->remove_node('my-account'); // Keep this for profile access
    }

    /**
     * Remove dashboard widgets for agents
     */
    public function remove_dashboard_widgets() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Only apply to agents, not admins
        if (!$agent_manager->is_agent() || current_user_can('manage_options')) {
            return;
        }

        // Remove all dashboard widgets
        remove_meta_box('dashboard_right_now', 'dashboard', 'normal');
        remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
        remove_meta_box('dashboard_incoming_links', 'dashboard', 'normal');
        remove_meta_box('dashboard_plugins', 'dashboard', 'normal');
        remove_meta_box('dashboard_quick_press', 'dashboard', 'side');
        remove_meta_box('dashboard_recent_drafts', 'dashboard', 'side');
        remove_meta_box('dashboard_primary', 'dashboard', 'side');
        remove_meta_box('dashboard_secondary', 'dashboard', 'side');
        remove_meta_box('dashboard_activity', 'dashboard', 'normal');
        remove_meta_box('woocommerce_dashboard_status', 'dashboard', 'normal');
        remove_meta_box('woocommerce_dashboard_recent_reviews', 'dashboard', 'normal');
        remove_meta_box('wc_admin_dashboard_setup', 'dashboard', 'normal');
        remove_meta_box('yith_dashboard_blog_news', 'dashboard', 'normal');
        remove_meta_box('elementor_dashboard_overview', 'dashboard', 'normal');
    }

    /**
     * Redirect agents to their dashboard
     */
    public function redirect_agents_to_dashboard() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Only apply to agents, not admins
        if (!$agent_manager->is_agent() || current_user_can('manage_options')) {
            return;
        }

        // Get current page
        global $pagenow;

        // Redirect from dashboard to agent dashboard
        if ($pagenow === 'index.php' && !isset($_GET['page'])) {
            wp_redirect(admin_url('admin.php?page=hozi-akadly-my-orders'));
            exit;
        }

        // Redirect from other restricted pages
        $restricted_pages = array(
            'edit.php',
            'post-new.php',
            'upload.php',
            'edit-comments.php',
            'themes.php',
            'plugins.php',
            'users.php',
            'tools.php',
            'options-general.php'
        );

        if (in_array($pagenow, $restricted_pages)) {
            wp_redirect(admin_url('admin.php?page=hozi-akadly-my-orders'));
            exit;
        }
    }

    /**
     * Add custom CSS for agents
     */
    public function add_agent_admin_css() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Only apply to agents, not admins
        if (!$agent_manager->is_agent() || current_user_can('manage_options')) {
            return;
        }

        ?>
        <style>
        /* Hide WordPress footer */
        #wpfooter {
            display: none !important;
        }

        /* Hide screen options and help tabs */
        #screen-meta-links {
            display: none !important;
        }

        /* Hide admin notices */
        .notice, .error, .updated {
            display: none !important;
        }

        /* Hide WordPress version in footer */
        #footer-thankyou, #footer-upgrade {
            display: none !important;
        }

        /* Clean up admin bar */
        #wpadminbar .ab-top-menu > li:not(#wp-admin-bar-my-account):not(#wp-admin-bar-site-name) {
            display: none !important;
        }

        /* Hide collapse menu button */
        #collapse-button {
            display: none !important;
        }

        /* Simplify admin interface */
        .wrap h1 .page-title-action {
            display: none !important;
        }

        /* Hide bulk actions that agents shouldn't use */
        .bulkactions {
            display: none !important;
        }

        /* Hide search box */
        .search-box {
            display: none !important;
        }

        /* Hide view switcher */
        .view-switch {
            display: none !important;
        }

        /* Hide pagination info */
        .displaying-num {
            display: none !important;
        }

        /* Customize admin menu for agents */
        #adminmenu {
            background: #f8f9fa;
        }

        #adminmenu .wp-menu-name {
            font-weight: 600;
        }

        /* Highlight Akadly menu item */
        #adminmenu li.current a.current {
            background: #0073aa !important;
            color: white !important;
        }

        /* Clean header */
        .wp-admin #wpadminbar {
            background: #23282d;
        }

        /* Hide update notifications */
        .update-nag, .updated, .error, .notice {
            display: none !important;
        }
        </style>
        <?php
    }

    /**
     * Hide admin notices for agents
     */
    public function hide_admin_notices_for_agents() {
        $agent_manager = new Hozi_Akadly_Agent_Manager();

        // Only apply to agents, not admins
        if (!$agent_manager->is_agent() || current_user_can('manage_options')) {
            return;
        }

        // Remove all admin notices
        remove_all_actions('admin_notices');
        remove_all_actions('all_admin_notices');

        // Add our own clean notice area
        add_action('admin_notices', function() {
            // Only show critical notices if needed
        });
    }
}

// Initialize the class
new Hozi_Akadly_Admin_Menu_Manager();
