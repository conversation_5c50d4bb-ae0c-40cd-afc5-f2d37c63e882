<?php
/**
 * Direct Fix for Messaging Issues
 * This file directly fixes the messaging problems
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

global $wpdb;

echo "<h2>🔧 إصلاح مشاكل نظام المراسلة مباشرة</h2>";

// 1. First, let's update the database schema
echo "<h3>1️⃣ تحديث قاعدة البيانات</h3>";

$table_name = $wpdb->prefix . 'hozi_messages';
$assignments_table = $wpdb->prefix . 'hozi_order_assignments';

// Check and add missing columns to messages table
$columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
$column_names = array_column($columns, 'Field');

if (!in_array('is_archived', $column_names)) {
    $wpdb->query("ALTER TABLE $table_name ADD COLUMN is_archived tinyint(1) DEFAULT 0 AFTER is_deleted");
    echo "✅ Added is_archived column<br>";
}

if (!in_array('archived_at', $column_names)) {
    $wpdb->query("ALTER TABLE $table_name ADD COLUMN archived_at datetime DEFAULT NULL AFTER read_at");
    echo "✅ Added archived_at column<br>";
}

// Check and add missing columns to assignments table
$assignment_columns = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table");
$assignment_column_names = array_column($assignment_columns, 'Field');

if (!in_array('confirmed_by_admin', $assignment_column_names)) {
    $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN confirmed_by_admin tinyint(1) DEFAULT 0 AFTER notes");
    echo "✅ Added confirmed_by_admin column<br>";
}

if (!in_array('admin_user_id', $assignment_column_names)) {
    $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN admin_user_id int(11) DEFAULT NULL AFTER confirmed_by_admin");
    echo "✅ Added admin_user_id column<br>";
}

if (!in_array('original_agent_id', $assignment_column_names)) {
    $wpdb->query("ALTER TABLE $assignments_table ADD COLUMN original_agent_id int(11) DEFAULT NULL AFTER admin_user_id");
    echo "✅ Added original_agent_id column<br>";
}

// 2. Test sending a message to all agents
echo "<h3>2️⃣ اختبار إرسال رسالة لجميع الوكلاء</h3>";

// Get all agents
$agents = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'hozi_akadly_agent',
            'value' => '1',
            'compare' => '='
        )
    )
));

if (empty($agents)) {
    // Try alternative method
    $agents = get_users(array('capability' => 'hozi_view_assigned_orders'));
}

if (empty($agents)) {
    echo "❌ لم يتم العثور على وكلاء<br>";
} else {
    echo "✅ تم العثور على " . count($agents) . " وكلاء<br>";
    
    // Send test message to all agents
    $admin_user_id = 1; // Assuming admin is user ID 1
    $test_subject = "رسالة اختبار - " . date('Y-m-d H:i:s');
    $test_message = "هذه رسالة اختبار للتأكد من وصول الرسائل لجميع الوكلاء.";
    
    $sent_count = 0;
    foreach ($agents as $agent) {
        $result = $wpdb->insert(
            $table_name,
            array(
                'sender_id' => $admin_user_id,
                'recipient_id' => $agent->ID,
                'recipient_type' => 'agent',
                'message_type' => 'general',
                'subject' => $test_subject,
                'message' => $test_message,
                'priority' => 'normal',
                'category' => 'general',
                'is_read' => 0,
                'is_deleted' => 0,
                'is_archived' => 0,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%d', '%d', '%s')
        );
        
        if ($result) {
            $sent_count++;
            echo "✅ تم إرسال رسالة للوكيل: {$agent->display_name} (ID: {$agent->ID})<br>";
        } else {
            echo "❌ فشل إرسال رسالة للوكيل: {$agent->display_name} - " . $wpdb->last_error . "<br>";
        }
    }
    
    echo "<strong>تم إرسال $sent_count رسالة من أصل " . count($agents) . " وكلاء</strong><br>";
}

// 3. Check messages for current user
echo "<h3>3️⃣ فحص الرسائل للمستخدم الحالي</h3>";

$current_user = wp_get_current_user();
echo "المستخدم الحالي: {$current_user->display_name} (ID: {$current_user->ID})<br>";

// Test the exact query used in the system
$user_messages = $wpdb->get_results($wpdb->prepare("
    SELECT m.*, 
           sender.display_name as sender_name,
           recipient.display_name as recipient_name
    FROM $table_name m
    LEFT JOIN {$wpdb->users} sender ON m.sender_id = sender.ID
    LEFT JOIN {$wpdb->users} recipient ON m.recipient_id = recipient.ID
    WHERE (m.recipient_id = %d OR (m.recipient_type = 'all' AND m.sender_id != %d) OR m.sender_id = %d)
    AND m.is_deleted = 0
    AND m.is_archived = 0
    ORDER BY m.created_at DESC
    LIMIT 10
", $current_user->ID, $current_user->ID, $current_user->ID));

echo "عدد الرسائل للمستخدم الحالي: " . count($user_messages) . "<br>";

if ($user_messages) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>من</th><th>إلى</th><th>نوع المستقبل</th><th>الموضوع</th><th>مقروءة</th><th>التاريخ</th></tr>";
    foreach ($user_messages as $msg) {
        echo "<tr>";
        echo "<td>{$msg->id}</td>";
        echo "<td>{$msg->sender_name}</td>";
        echo "<td>{$msg->recipient_name}</td>";
        echo "<td>{$msg->recipient_type}</td>";
        echo "<td>{$msg->subject}</td>";
        echo "<td>" . ($msg->is_read ? 'نعم' : 'لا') . "</td>";
        echo "<td>{$msg->created_at}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ لا توجد رسائل للمستخدم الحالي<br>";
    
    // Check if there are any messages at all for this user
    $any_messages = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*) FROM $table_name 
        WHERE recipient_id = %d OR sender_id = %d
    ", $current_user->ID, $current_user->ID));
    
    echo "إجمالي الرسائل المرتبطة بالمستخدم: $any_messages<br>";
}

// 4. Test unread count
echo "<h3>4️⃣ اختبار عدد الرسائل غير المقروءة</h3>";

$unread_count = $wpdb->get_var($wpdb->prepare("
    SELECT COUNT(*) FROM $table_name 
    WHERE (recipient_id = %d OR (recipient_type = 'all' AND sender_id != %d)) 
    AND is_read = 0 
    AND is_deleted = 0
    AND is_archived = 0
", $current_user->ID, $current_user->ID));

echo "عدد الرسائل غير المقروءة: $unread_count<br>";

// 5. Fix admin confirmation tracking
echo "<h3>5️⃣ إصلاح تتبع تأكيدات المدير</h3>";

// Check recent assignments
$recent_assignments = $wpdb->get_results("
    SELECT oa.*, a.name as agent_name 
    FROM $assignments_table oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    ORDER BY oa.assigned_at DESC 
    LIMIT 5
");

if ($recent_assignments) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>طلب</th><th>وكيل</th><th>حالة التأكيد</th><th>مؤكد بواسطة مدير</th><th>معرف المدير</th><th>الوكيل الأصلي</th></tr>";
    foreach ($recent_assignments as $assignment) {
        echo "<tr>";
        echo "<td>{$assignment->order_id}</td>";
        echo "<td>{$assignment->agent_name} (ID: {$assignment->agent_id})</td>";
        echo "<td>{$assignment->confirmation_status}</td>";
        echo "<td>" . (isset($assignment->confirmed_by_admin) ? ($assignment->confirmed_by_admin ? 'نعم' : 'لا') : 'غير محدد') . "</td>";
        echo "<td>" . (isset($assignment->admin_user_id) ? $assignment->admin_user_id : 'غير محدد') . "</td>";
        echo "<td>" . (isset($assignment->original_agent_id) ? $assignment->original_agent_id : 'غير محدد') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ لا توجد تخصيصات طلبات<br>";
}

echo "<h3>✅ تم الانتهاء من الإصلاح</h3>";
echo "<p><strong>الخطوات التالية:</strong></p>";
echo "<ol>";
echo "<li>تحديث الصفحة في لوحة الوكيل للتحقق من ظهور الرسائل</li>";
echo "<li>اختبار إرسال رسالة جديدة لجميع الوكلاء</li>";
echo "<li>اختبار العمل كوكيل وتأكيد طلب</li>";
echo "</ol>";
?>