<?php
/**
 * EDD Plugin Updater
 */

if (!defined('ABSPATH')) {
    exit;
}

if (!class_exists('EDD_Plugin_Updater')) {

    class EDD_Plugin_Updater {

        private $api_url = '';
        private $api_data = array();
        private $name = '';
        private $slug = '';
        private $version = '';
        private $wp_override = false;
        private $cache_key = '';

        public function __construct($_api_url, $_plugin_file, $_api_data = null) {
            global $edd_plugin_data;

            $this->api_url = trailingslashit($_api_url);
            $this->api_data = $_api_data;
            $this->name = plugin_basename($_plugin_file);
            $this->slug = basename($_plugin_file, '.php');
            $this->version = $_api_data['version'];
            $this->wp_override = isset($_api_data['wp_override']) ? (bool) $_api_data['wp_override'] : false;
            $this->beta = !empty($this->api_data['beta']) ? true : false;
            $this->cache_key = 'edd_plugin_' . md5(serialize($this->slug . $this->api_data['license'] . $this->beta));

            $edd_plugin_data[$this->slug] = $this->api_data;

            add_filter('pre_set_site_transient_update_plugins', array($this, 'check_for_update'));
            add_filter('plugins_api', array($this, 'plugins_api_filter'), 10, 3);
            remove_action('after_plugin_row_' . $this->name, 'wp_plugin_update_row', 10, 2);
            add_action('after_plugin_row_' . $this->name, array($this, 'show_update_notification'), 10, 2);
            add_action('admin_init', array($this, 'show_changelog'));
        }

        public function check_for_update($_transient_data) {
            global $pagenow;

            if (!is_object($_transient_data)) {
                $_transient_data = new stdClass;
            }

            if ('plugins.php' == $pagenow && is_multisite()) {
                return $_transient_data;
            }

            if (!empty($_transient_data->response) && !empty($_transient_data->response[$this->name]) && false === $this->wp_override) {
                return $_transient_data;
            }

            $version_info = $this->get_cached_version_info();

            if (false === $version_info) {
                $version_info = $this->api_request('plugin_latest_version', array('slug' => $this->slug, 'beta' => $this->beta));

                $this->set_version_info_cache($version_info);
            }

            if (false !== $version_info && is_object($version_info) && isset($version_info->new_version)) {
                if (version_compare($this->version, $version_info->new_version, '<')) {
                    $_transient_data->response[$this->name] = $version_info;
                } else {
                    $_transient_data->no_update[$this->name] = $version_info;
                }
            }

            return $_transient_data;
        }

        public function show_update_notification($file, $plugin) {
            if (is_network_admin()) {
                return;
            }

            if (!current_user_can('update_plugins')) {
                return;
            }

            if ($this->name != $file) {
                return;
            }

            remove_action('after_plugin_row_' . $this->name, 'wp_plugin_update_row', 10, 2);

            $version_info = $this->get_cached_version_info();

            if (false === $version_info) {
                $version_info = $this->api_request('plugin_latest_version', array('slug' => $this->slug, 'beta' => $this->beta));
                $this->set_version_info_cache($version_info);
            }

            if (!is_object($version_info)) {
                return;
            }

            if (version_compare($this->version, $version_info->new_version, '<')) {
                echo '<tr class="plugin-update-tr"><td colspan="3" class="plugin-update colspanchange"><div class="update-message notice inline notice-warning notice-alt"><p>';

                $changelog_link = self_admin_url('index.php?edd_sl_action=view_plugin_changelog&plugin=' . $this->name . '&slug=' . $this->slug . '&TB_iframe=true&width=772&height=911');

                if (empty($version_info->download_link)) {
                    printf(
                        __('There is a new version of %1$s available. %2$sView version %3$s details%4$s.', 'easy-digital-downloads'),
                        esc_html($version_info->name),
                        '<a target="_blank" class="thickbox" href="' . esc_url($changelog_link) . '">',
                        esc_html($version_info->new_version),
                        '</a>'
                    );
                } else {
                    printf(
                        __('There is a new version of %1$s available. %2$sView version %3$s details%4$s or %5$supdate now%6$s.', 'easy-digital-downloads'),
                        esc_html($version_info->name),
                        '<a target="_blank" class="thickbox" href="' . esc_url($changelog_link) . '">',
                        esc_html($version_info->new_version),
                        '</a>',
                        '<a href="' . esc_url(wp_nonce_url(self_admin_url('update.php?action=upgrade-plugin&plugin=') . $this->name, 'upgrade-plugin_' . $this->name)) . '">',
                        '</a>'
                    );
                }

                do_action("in_plugin_update_message-{$file}", $plugin, $version_info);

                echo '</p></div></td></tr>';
            }
        }

        public function plugins_api_filter($_data, $_action = '', $_args = null) {
            if ($_action != 'plugin_information') {
                return $_data;
            }

            if (!isset($_args->slug) || ($_args->slug != $this->slug)) {
                return $_data;
            }

            $to_send = array(
                'slug' => $this->slug,
                'is_ssl' => is_ssl(),
                'fields' => array(
                    'banners' => array(),
                    'reviews' => false
                )
            );

            $api_response = $this->api_request('plugin_information', $to_send);

            if (false !== $api_response) {
                $_data = $api_response;
            }

            return $_data;
        }

        public function show_changelog() {
            global $edd_plugin_data;

            if (empty($_REQUEST['edd_sl_action']) || 'view_plugin_changelog' != $_REQUEST['edd_sl_action']) {
                return;
            }

            if (empty($_REQUEST['plugin'])) {
                return;
            }

            if (empty($_REQUEST['slug'])) {
                return;
            }

            if (!current_user_can('update_plugins')) {
                wp_die(__('You do not have permission to install plugin updates', 'easy-digital-downloads'), __('Error', 'easy-digital-downloads'), array('response' => 403));
            }

            $data = $edd_plugin_data[$_REQUEST['slug']];
            $beta = !empty($data['beta']) ? true : false;
            $cache_key = 'edd_plugin_' . md5(serialize($this->slug . $this->api_data['license'] . $beta));
            $version_info = get_transient($cache_key);

            if (false === $version_info) {
                $api_params = array(
                    'edd_action' => 'get_version',
                    'item_name' => isset($data['item_name']) ? $data['item_name'] : false,
                    'item_id' => isset($data['item_id']) ? $data['item_id'] : false,
                    'slug' => $_REQUEST['slug'],
                    'author' => $data['author'],
                    'url' => home_url(),
                    'beta' => $beta
                );

                $request = wp_remote_post($this->api_url, array('timeout' => 15, 'sslverify' => false, 'body' => $api_params));

                if (!is_wp_error($request)) {
                    $version_info = json_decode(wp_remote_retrieve_body($request));
                    set_transient($cache_key, $version_info, DAY_IN_SECONDS);
                }
            }

            if (!empty($version_info) && isset($version_info->sections)) {
                $sections = $version_info->sections;
            } else {
                $sections = false;
            }

            if (!empty($version_info->banners)) {
                $banners = $version_info->banners;
            } else {
                $banners = array();
            }

            $plugin_info = array(
                'name' => isset($version_info->name) ? $version_info->name : '',
                'slug' => $_REQUEST['slug'],
                'version' => isset($version_info->new_version) ? $version_info->new_version : '',
                'author' => isset($version_info->author) ? $version_info->author : '',
                'homepage' => isset($version_info->homepage) ? $version_info->homepage : '',
                'requires' => isset($version_info->requires) ? $version_info->requires : '',
                'tested' => isset($version_info->tested) ? $version_info->tested : '',
                'downloaded' => isset($version_info->downloaded) ? $version_info->downloaded : '',
                'last_updated' => isset($version_info->last_updated) ? $version_info->last_updated : '',
                'sections' => $sections,
                'banners' => $banners
            );

            echo '<div style="background:#fff;padding:10px;margin-right:12px;">';
            echo '<h3>' . $plugin_info['name'] . '</h3>';
            if (isset($sections['changelog'])) {
                echo '<div>' . $sections['changelog'] . '</div>';
            }
            echo '</div>';
            exit;
        }

        private function api_request($_action, $_data) {
            global $wp_version;

            $data = array_merge($this->api_data, $_data);

            if ($data['slug'] != $this->slug) {
                return;
            }

            if ($this->api_url == home_url()) {
                return false;
            }

            $api_params = array(
                'edd_action' => 'get_version',
                'license' => !empty($data['license']) ? $data['license'] : '',
                'item_name' => isset($data['item_name']) ? $data['item_name'] : false,
                'item_id' => isset($data['item_id']) ? $data['item_id'] : false,
                'version' => isset($data['version']) ? $data['version'] : false,
                'slug' => $data['slug'],
                'author' => $data['author'],
                'url' => home_url(),
                'beta' => !empty($data['beta']),
            );

            $request = wp_remote_post($this->api_url, array('timeout' => 15, 'sslverify' => false, 'body' => $api_params));

            if (!is_wp_error($request)) {
                $request = json_decode(wp_remote_retrieve_body($request));
            }

            if ($request && isset($request->sections)) {
                $request->sections = maybe_unserialize($request->sections);
            } else {
                $request = false;
            }

            if ($request && isset($request->banners)) {
                $request->banners = maybe_unserialize($request->banners);
            }

            if (!empty($request->sections)) {
                foreach ($request->sections as $key => $section) {
                    $request->$key = (array) $section;
                }
            }

            return $request;
        }

        public function get_cached_version_info($cache_key = '') {
            if (empty($cache_key)) {
                $cache_key = $this->cache_key;
            }

            $cache = get_option($cache_key);

            if (empty($cache['timeout']) || time() > $cache['timeout']) {
                return false;
            }

            return json_decode($cache['value']);
        }

        public function set_version_info_cache($value = '', $cache_key = '') {
            if (empty($cache_key)) {
                $cache_key = $this->cache_key;
            }

            $data = array(
                'timeout' => strtotime('+3 hours', time()),
                'value' => json_encode($value)
            );

            update_option($cache_key, $data, 'no');
        }
    }
}
