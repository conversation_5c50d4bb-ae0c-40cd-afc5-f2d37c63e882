<?php
/**
 * Admin as Agent V2 - Simple and Bug-Free
 * Handles admin working as agent with proper stats tracking
 */

if (!defined('ABSPATH')) {
    exit;
}

class Hozi_Akadly_Admin_As_Agent_V2 {
    
    private $stats_tracker;
    
    public function __construct() {
        $this->stats_tracker = new Hozi_Akadly_Admin_Stats_Tracker();
        
        // Hook into form submission
        add_action('admin_init', array($this, 'handle_admin_confirmation'));
    }
    
    /**
     * Handle admin confirmation when working as agent
     */
    public function handle_admin_confirmation() {
        // Check if this is an admin-as-agent confirmation
        if (!isset($_POST['admin_as_agent_confirmation']) || !wp_verify_nonce($_POST['hozi_akadly_nonce'], 'hozi_akadly_nonce')) {
            return;
        }
        
        $order_id = intval($_POST['order_id']);
        $status = sanitize_text_field($_POST['status']);
        $notes = sanitize_textarea_field($_POST['notes'] ?? '');
        $agent_id = intval($_POST['agent_id'] ?? 0);
        
        if (!$order_id || !$status || !$agent_id) {
            return;
        }
        
        $admin_user_id = get_current_user_id();
        $admin_user = wp_get_current_user();
        
        // Update order assignment with clear admin tracking
        global $wpdb;
        
        $result = $wpdb->update(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'confirmation_status' => $status,
                'confirmed_at' => current_time('mysql'),
                'notes' => sprintf('[مدير: %s نيابة عن وكيل ID:%d] %s', $admin_user->display_name, $agent_id, $notes),
                'confirmed_by_admin' => 1,
                'admin_user_id' => $admin_user_id,
                'original_agent_id' => $agent_id
            ),
            array('order_id' => $order_id),
            array('%s', '%s', '%s', '%d', '%d', '%d'),
            array('%d')
        );
        
        if ($result) {
            // Track admin action (this counts for admin stats, not agent)
            if ($status === 'confirmed') {
                do_action('hozi_admin_confirmed_order', $admin_user_id, $order_id, $agent_id);
            } elseif ($status === 'rejected') {
                do_action('hozi_admin_rejected_order', $admin_user_id, $order_id, $agent_id);
            }
            
            // Update WooCommerce order
            $order = wc_get_order($order_id);
            if ($order) {
                if ($status === 'confirmed') {
                    $order->update_status('processing', sprintf('تم التأكيد بواسطة المدير %s نيابة عن الوكيل', $admin_user->display_name));
                } elseif ($status === 'rejected') {
                    $order->update_status('cancelled', sprintf('تم الرفض بواسطة المدير %s نيابة عن الوكيل', $admin_user->display_name));
                }
                
                // Add order note
                $order->add_order_note(sprintf(
                    '👨‍💼 إجراء المدير: %s%s%s%s%s',
                    $admin_user->display_name,
                    "\n🎭 نيابة عن الوكيل ID: " . $agent_id,
                    "\n📋 الحالة: " . $status,
                    $notes ? "\n📝 ملاحظة: " . $notes : '',
                    "\n⏰ " . current_time('Y/m/d H:i')
                ), 0);
            }
            
            // Show success message
            add_action('admin_notices', function() use ($admin_user, $status) {
                echo '<div class="notice notice-success"><p>';
                echo sprintf('✅ تم تحديث حالة الطلب إلى "%s" بواسطة المدير: %s', $status, $admin_user->display_name);
                echo '</p></div>';
            });
            
            // Redirect to avoid form resubmission
            wp_redirect(admin_url('admin.php?page=hozi-akadly-admin-as-agent&agent_id=' . $agent_id . '&success=1'));
            exit;
        }
    }
    
    /**
     * Get admin stats for display
     */
    public function get_admin_stats($admin_id) {
        return $this->stats_tracker->get_admin_stats($admin_id);
    }
    
    /**
     * Get recent admin actions
     */
    public function get_recent_admin_actions($admin_id, $limit = 10) {
        return $this->stats_tracker->get_recent_actions($admin_id, $limit);
    }
}