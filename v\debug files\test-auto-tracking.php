<?php
/**
 * Test Auto Tracking System
 * Tests the automatic transition of orders to tracking when status changes to 'completed'
 */

// WordPress environment
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo "<h1>🎯 اختبار النظام التلقائي لانتقال الطلبات للتتبع</h1>";

global $wpdb;

// Get current user info
$current_user_id = get_current_user_id();
$current_user = wp_get_current_user();

echo "<h2>👤 معلومات المستخدم الحالي:</h2>";
echo "<p><strong>اسم المستخدم:</strong> {$current_user->display_name}</p>";
echo "<p><strong>User ID:</strong> {$current_user_id}</p>";

// Get test agent
$test_agent = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}hozi_agents WHERE is_active = 1 LIMIT 1");

if (!$test_agent) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ لا يوجد وكلاء نشطين!</h3>";
    echo "<p>يجب إنشاء وكيل نشط أولاً.</p>";
    echo "</div>";
    exit;
}

echo "<h2>🧪 الوكيل المستخدم للاختبار:</h2>";
echo "<p><strong>اسم الوكيل:</strong> {$test_agent->name}</p>";
echo "<p><strong>Agent ID:</strong> {$test_agent->id}</p>";

// Get recent orders
$recent_orders = $wpdb->get_results("
    SELECT ID, post_status, post_date 
    FROM {$wpdb->prefix}posts 
    WHERE post_type = 'shop_order' 
    AND post_status IN ('wc-processing', 'wc-on-hold', 'wc-pending')
    ORDER BY post_date DESC 
    LIMIT 5
");

echo "<h2>📋 الطلبات المتاحة للاختبار:</h2>";

if (empty($recent_orders)) {
    echo "<p>❌ لا توجد طلبات متاحة للاختبار.</p>";
    exit;
}

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>رقم الطلب</th><th>الحالة</th><th>التاريخ</th><th>إجراءات</th></tr>";

foreach ($recent_orders as $order) {
    echo "<tr>";
    echo "<td>#{$order->ID}</td>";
    echo "<td>{$order->post_status}</td>";
    echo "<td>" . date('Y/m/d H:i', strtotime($order->post_date)) . "</td>";
    echo "<td>";
    echo "<a href='?action=setup_order&order_id={$order->ID}' style='background: #2196f3; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin-right: 5px;'>إعداد للاختبار</a>";
    echo "<a href='?action=test_complete&order_id={$order->ID}' style='background: #4caf50; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>اختبار الإكمال</a>";
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

// Handle actions
if (isset($_GET['action']) && isset($_GET['order_id'])) {
    $action = sanitize_text_field($_GET['action']);
    $order_id = intval($_GET['order_id']);
    
    echo "<h2>🔧 تنفيذ الإجراء:</h2>";
    
    if ($action === 'setup_order') {
        // Setup order for testing
        echo "<h3>⚙️ إعداد الطلب #{$order_id} للاختبار:</h3>";
        
        // 1. Delete existing assignment
        $deleted = $wpdb->delete(
            $wpdb->prefix . 'hozi_order_assignments',
            array('order_id' => $order_id),
            array('%d')
        );
        echo "<p>✅ تم مسح التخصيص السابق: {$deleted} سجل</p>";
        
        // 2. Create confirmed assignment
        $result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'order_id' => $order_id,
                'agent_id' => $test_agent->id,
                'confirmation_status' => 'confirmed',
                'assigned_at' => current_time('mysql'),
                'confirmed_at' => current_time('mysql'),
                'assignment_method' => 'manual',
                'archived' => 0,
                'notes' => 'تم إعداده للاختبار التلقائي'
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%d', '%s')
        );
        
        if ($result) {
            echo "<p>✅ تم إنشاء تخصيص مؤكد للطلب #{$order_id}</p>";
            echo "<p>🎯 الطلب جاهز الآن لاختبار الانتقال التلقائي للتتبع</p>";
        } else {
            echo "<p>❌ فشل في إنشاء التخصيص: " . $wpdb->last_error . "</p>";
        }
        
    } elseif ($action === 'test_complete') {
        // Test completing the order
        echo "<h3>🎯 اختبار إكمال الطلب #{$order_id}:</h3>";
        
        // Check if order has confirmed assignment
        $assignment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}hozi_order_assignments 
             WHERE order_id = %d AND confirmation_status = 'confirmed'",
            $order_id
        ));
        
        if (!$assignment) {
            echo "<p>❌ الطلب غير مؤكد! يجب إعداده أولاً.</p>";
        } else {
            echo "<p>✅ الطلب مؤكد من الوكيل ID: {$assignment->agent_id}</p>";
            
            // Check existing tracking
            $existing_tracking = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d",
                $order_id
            ));
            
            if ($existing_tracking) {
                echo "<p>⚠️ الطلب له تتبع موجود مسبقاً: {$existing_tracking->status}</p>";
            } else {
                echo "<p>✅ الطلب ليس له تتبع - مناسب للاختبار</p>";
            }
            
            // Change order status to completed
            $order = wc_get_order($order_id);
            if ($order) {
                echo "<p>📝 الحالة الحالية: {$order->get_status()}</p>";
                
                // Update to completed
                $order->update_status('completed', 'اختبار النظام التلقائي لأكدلي');
                
                echo "<p>🎯 تم تغيير حالة الطلب إلى 'completed'</p>";
                
                // Check if tracking was added automatically
                sleep(1); // Wait a moment
                
                $new_tracking = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}hozi_order_tracking WHERE order_id = %d ORDER BY created_at DESC LIMIT 1",
                    $order_id
                ));
                
                if ($new_tracking) {
                    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                    echo "<h4>🎉 نجح النظام التلقائي!</h4>";
                    echo "<p><strong>تم إضافة التتبع تلقائياً:</strong></p>";
                    echo "<p>• الحالة: {$new_tracking->status}</p>";
                    echo "<p>• الوكيل: {$new_tracking->agent_id}</p>";
                    echo "<p>• الملاحظات: {$new_tracking->notes}</p>";
                    echo "<p>• التاريخ: {$new_tracking->created_at}</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
                    echo "<h4>❌ فشل النظام التلقائي!</h4>";
                    echo "<p>لم يتم إضافة التتبع تلقائياً. يجب فحص الكود.</p>";
                    echo "</div>";
                }
            } else {
                echo "<p>❌ فشل في الحصول على بيانات الطلب</p>";
            }
        }
    }
}

echo "<h2>📊 حالة النظام:</h2>";

// Check if order tracker is initialized
if (class_exists('Hozi_Akadly_Order_Tracker')) {
    echo "<p>✅ كلاس Order Tracker موجود</p>";
    
    // Check if hook is registered
    $hooks = $GLOBALS['wp_filter']['woocommerce_order_status_changed'] ?? null;
    if ($hooks) {
        echo "<p>✅ Hook woocommerce_order_status_changed مسجل</p>";
        
        // Check if our handler is in the hooks
        $found_handler = false;
        foreach ($hooks->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Hozi_Akadly_Order_Tracker' &&
                    $callback['function'][1] === 'handle_woocommerce_status_change') {
                    $found_handler = true;
                    echo "<p>✅ Handler handle_woocommerce_status_change مسجل بأولوية {$priority}</p>";
                    break 2;
                }
            }
        }
        
        if (!$found_handler) {
            echo "<p>❌ Handler handle_woocommerce_status_change غير مسجل</p>";
        }
    } else {
        echo "<p>❌ Hook woocommerce_order_status_changed غير مسجل</p>";
    }
} else {
    echo "<p>❌ كلاس Order Tracker غير موجود</p>";
}

echo "<h2>🔄 إعادة تحميل الصفحة:</h2>";
echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>🔄 إعادة تحميل</a></p>";
?>
