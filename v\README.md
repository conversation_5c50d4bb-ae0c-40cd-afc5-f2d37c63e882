# Hozi Akadly - أكدلي
## نظام تأكيد الطلبيات الشامل لـ WooCommerce

### 📋 نظرة عامة
**Hozi Akadly** هو إضافة WordPress متخصصة لإدارة وتأكيد طلبات WooCommerce من خلال نظام توزيع ذكي للوكلاء. تم تصميم هذه الإضافة خصيصاً للمتاجر الإلكترونية التي تحتاج إلى تأكيد هاتفي للطلبات.

### ✨ الميزات الرئيسية (MVP)

#### 🎯 **نظام توزيع الطلبات**
- **توزيع دوري تلقائي (Round Robin)**: توزيع عادل للطلبات بين الوكلاء
- **توزيع يدوي**: إمكانية تخصيص الطلبات يدوياً من قبل المشرف
- **تخصيص تلقائي**: تخصيص الطلبات الجديدة تلقائياً حسب الإعدادات

#### 👥 **إدارة الوكلاء**
- إنشاء وإدارة حسابات وكلاء التأكيد
- تفعيل/تعطيل الوكلاء
- تحديد الحد الأقصى للطلبات اليومية لكل وكيل
- تتبع إحصائيات الأداء لكل وكيل

#### 📱 **واجهة الوكيل**
- واجهة مخصصة لعرض الطلبات المخصصة
- عرض تفاصيل العميل والطلب بشكل واضح
- أزرار سريعة لتأكيد/رفض/إعادة جدولة الطلبات
- إمكانية إضافة ملاحظات للطلبات

#### 📊 **حالات التأكيد**
- **في انتظار التأكيد**: الحالة الافتراضية للطلبات الجديدة
- **تم التأكيد**: عند موافقة العميل على الطلب
- **تم الرفض**: عند رفض العميل للطلب
- **العميل لم يرد**: عند عدم رد العميل على المكالمة
- **إعادة الاتصال لاحقاً**: لجدولة مكالمة أخرى

### 🛠️ المتطلبات التقنية

#### **متطلبات النظام**
- WordPress 5.0 أو أحدث
- WooCommerce 5.0 أو أحدث
- PHP 7.4 أو أحدث
- MySQL 5.6 أو أحدث

#### **المتطلبات الموصى بها**
- WordPress 6.0+
- WooCommerce 8.0+
- PHP 8.0+
- MySQL 8.0+

### 📦 التثبيت

1. **تحميل الإضافة**
   ```bash
   git clone https://github.com/your-repo/hozi-akadly.git
   ```

2. **رفع الملفات**
   - ارفع مجلد `hozi-akadly` إلى `/wp-content/plugins/`

3. **تفعيل الإضافة**
   - اذهب إلى لوحة تحكم WordPress
   - انتقل إلى الإضافات > الإضافات المثبتة
   - فعّل إضافة "Hozi Akadly - أكدلي"

4. **إعداد قاعدة البيانات**
   - سيتم إنشاء الجداول المطلوبة تلقائياً عند التفعيل

5. **🔑 تفعيل الترخيص**
   - انتقل إلى **أكدلي > الترخيص**
   - أدخل مفتاح الترخيص الذي حصلت عليه من [Hostazi](https://hostazi.shop)
   - اضغط على "تفعيل الترخيص"
   - تأكد من ظهور رسالة "تم تفعيل الترخيص بنجاح"

### ⚙️ الإعداد الأولي

#### **1. إعداد الوكلاء**
1. انتقل إلى **أكدلي > الوكلاء**
2. اضغط على "إضافة وكيل جديد"
3. اختر مستخدم موجود أو أنشئ مستخدم جديد
4. املأ بيانات الوكيل (الاسم، الهاتف، الحد الأقصى للطلبات)

#### **2. ضبط الإعدادات**
1. انتقل إلى **أكدلي > الإعدادات**
2. اختر طريقة التوزيع (دوري تلقائي أو يدوي)
3. فعّل/عطّل التخصيص التلقائي للطلبات الجديدة
4. حدد حالات الطلبات المراد تخصيصها

#### **3. اختبار النظام**
1. أنشئ طلب تجريبي في WooCommerce
2. تحقق من تخصيص الطلب للوكيل
3. ادخل كوكيل وجرب واجهة التأكيد

### 🎮 كيفية الاستخدام

#### **للمشرفين:**
1. **مراقبة لوحة التحكم**: عرض الإحصائيات العامة والنشاط الأخير
2. **إدارة الوكلاء**: إضافة/تعديل/تعطيل الوكلاء
3. **توزيع الطلبات**: تخصيص الطلبات يدوياً أو تلقائياً
4. **مراجعة التقارير**: متابعة أداء الوكلاء والإحصائيات

#### **للوكلاء:**
1. **تسجيل الدخول**: استخدام حساب WordPress العادي
2. **عرض الطلبات**: الانتقال إلى **أكدلي > طلباتي**
3. **تأكيد الطلبات**: استخدام الأزرار السريعة لتحديث حالة الطلب
4. **إضافة ملاحظات**: توثيق تفاصيل المكالمة

#### **للعملاء (من لوحة الإدارة):**
1. **عرض الطلبات**: من لوحة الإدارة **أكدلي > طلبات العملاء**
2. **البحث بالبريد الإلكتروني**: للوصول لطلبات عميل محدد
3. **فلترة الطلبات**: حسب حالة التأكيد
4. **تتبع التفاصيل**: رؤية الوكيل المخصص والملاحظات

### 🗄️ هيكل قاعدة البيانات

#### **الجداول المنشأة:**
- `wp_hozi_agents`: معلومات الوكلاء
- `wp_hozi_order_assignments`: تخصيصات الطلبات
- `wp_hozi_confirmation_logs`: سجل جميع الإجراءات

### 🔧 التخصيص والتطوير

#### **إضافة حالات جديدة:**
```php
// في functions.php أو إضافة مخصصة
add_filter('hozi_akadly_confirmation_statuses', function($statuses) {
    $statuses['custom_status'] = 'حالة مخصصة';
    return $statuses;
});
```

#### **تخصيص واجهة الوكيل:**
```php
// تخصيص عرض معلومات الطلب
add_filter('hozi_akadly_order_display_fields', function($fields) {
    $fields['custom_field'] = 'حقل مخصص';
    return $fields;
});
```

### 🚀 الميزات المستقبلية

#### **المرحلة الثانية:**
- [ ] نصوص المنتجات لمساعدة الوكلاء
- [ ] نظام البيع الإضافي (Upselling)
- [ ] توجيه الطلبات للوكيل نفسه للعميل المتكرر
- [ ] تقارير أداء متقدمة

#### **المرحلة الثالثة:**
- [ ] تكامل مع أنظمة CRM خارجية
- [ ] واجهة اتصال متكاملة
- [ ] تحليلات متقدمة ولوحة معلومات
- [ ] تطبيق جوال للوكلاء

### 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشكلة:
1. تحقق من متطلبات النظام
2. فعّل وضع التصحيح في WordPress
3. راجع سجلات الأخطاء
4. أنشئ تقرير مشكلة مع التفاصيل

### 🔑 نظام الترخيص

#### **متطلبات الترخيص:**
- **ترخيص صالح مطلوب**: هذه الإضافة تتطلب ترخيص صالح من [Hostazi](https://hostazi.shop/downloads/akadly/)
- **تفعيل إجباري**: لا يمكن استخدام الإضافة بدون تفعيل الترخيص
- **تحديثات تلقائية**: الترخيص الصالح يوفر تحديثات تلقائية

#### **كيفية الحصول على ترخيص:**
1. **زيارة المتجر**: اذهب إلى [https://hostazi.shop/downloads/akadly/](https://hostazi.shop/downloads/akadly/)
2. **شراء المنتج**: اشتر ترخيص "Akadly"
3. **استلام المفتاح**: ستحصل على مفتاح الترخيص عبر البريد الإلكتروني
4. **التفعيل**: أدخل المفتاح في **أكدلي > الترخيص**

#### **أنواع التراخيص:**
- **ترخيص موقع واحد**: للاستخدام في موقع واحد فقط
- **ترخيص مواقع متعددة**: للاستخدام في عدة مواقع
- **ترخيص مطور**: للمطورين والوكالات

#### **الدعم والتحديثات:**
- **دعم فني مجاني**: لمدة سنة واحدة مع كل ترخيص
- **تحديثات مجانية**: تحديثات الأمان والميزات الجديدة
- **تجديد الترخيص**: يمكن تجديد الترخيص للحصول على دعم إضافي

### 📄 الترخيص التقني

هذه الإضافة مرخصة تحت رخصة GPL v2 أو أحدث مع متطلبات ترخيص تجاري إضافية.

### 👨‍💻 المطور

**Hostazi** - متخصصون في حلول التجارة الإلكترونية
- 🌐 الموقع: [https://hostazi.shop](https://hostazi.shop)
- 💼 خدماتنا: تطوير إضافات WooCommerce المخصصة
- 🛠️ تخصصنا: حلول التجارة الإلكترونية المتقدمة

### 📞 الدعم الفني

للحصول على الدعم الفني:
- 🌐 الموقع الرسمي: [https://hostazi.shop](https://hostazi.shop)
- 📧 تواصل معنا عبر الموقع للدعم والاستفسارات
- 📚 دليل الاستخدام: راجع ملفات التوثيق المرفقة

---

**ملاحظة**: هذا هو الإصدار الأول (MVP) من الإضافة. المزيد من الميزات قادمة في التحديثات القادمة!
