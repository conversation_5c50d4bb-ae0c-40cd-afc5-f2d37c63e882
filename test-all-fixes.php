<?php
/**
 * Test All Messaging Fixes
 * Comprehensive test for all messaging system fixes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

global $wpdb;

echo "<h1>🧪 اختبار شامل لإصلاحات نظام المراسلة</h1>";

// Test 1: Database Schema
echo "<h2>1️⃣ اختبار قاعدة البيانات</h2>";

$messages_table = $wpdb->prefix . 'hozi_messages';
$assignments_table = $wpdb->prefix . 'hozi_order_assignments';

// Check messages table
$messages_columns = $wpdb->get_results("SHOW COLUMNS FROM $messages_table");
$messages_column_names = array_column($messages_columns, 'Field');

echo "<h3>جدول الرسائل:</h3>";
$required_message_columns = ['is_archived', 'archived_at'];
foreach ($required_message_columns as $col) {
    if (in_array($col, $messages_column_names)) {
        echo "✅ العمود موجود: $col<br>";
    } else {
        echo "❌ العمود مفقود: $col<br>";
    }
}

// Check assignments table
$assignments_columns = $wpdb->get_results("SHOW COLUMNS FROM $assignments_table");
$assignments_column_names = array_column($assignments_columns, 'Field');

echo "<h3>جدول التخصيصات:</h3>";
$required_assignment_columns = ['confirmed_by_admin', 'admin_user_id', 'original_agent_id'];
foreach ($required_assignment_columns as $col) {
    if (in_array($col, $assignments_column_names)) {
        echo "✅ العمود موجود: $col<br>";
    } else {
        echo "❌ العمود مفقود: $col<br>";
    }
}

// Test 2: Send Message to All Agents
echo "<h2>2️⃣ اختبار إرسال رسالة لجميع الوكلاء</h2>";

// Get agents
$agents = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'hozi_akadly_agent',
            'value' => '1',
            'compare' => '='
        )
    )
));

if (empty($agents)) {
    $agents = get_users(array('capability' => 'hozi_view_assigned_orders'));
}

echo "عدد الوكلاء الموجودين: " . count($agents) . "<br>";

if (!empty($agents)) {
    // Send test message
    $test_subject = "اختبار شامل - " . date('H:i:s');
    $test_message = "رسالة اختبار للتحقق من وصول الرسائل لجميع الوكلاء.";
    $admin_id = 1;
    
    $sent_count = 0;
    foreach ($agents as $agent) {
        $result = $wpdb->insert(
            $messages_table,
            array(
                'sender_id' => $admin_id,
                'recipient_id' => $agent->ID,
                'recipient_type' => 'agent',
                'message_type' => 'general',
                'subject' => $test_subject,
                'message' => $test_message,
                'priority' => 'normal',
                'category' => 'general',
                'is_read' => 0,
                'is_deleted' => 0,
                'is_archived' => 0,
                'created_at' => current_time('mysql')
            )
        );
        
        if ($result) {
            $sent_count++;
        }
    }
    
    echo "تم إرسال $sent_count رسالة من أصل " . count($agents) . " وكلاء<br>";
    
    if ($sent_count == count($agents)) {
        echo "✅ نجح إرسال الرسائل لجميع الوكلاء<br>";
    } else {
        echo "❌ فشل في إرسال بعض الرسائل<br>";
    }
} else {
    echo "❌ لم يتم العثور على وكلاء<br>";
}

// Test 3: Check Message Visibility for Each Agent
echo "<h2>3️⃣ اختبار رؤية الرسائل للوكلاء</h2>";

foreach ($agents as $agent) {
    $agent_messages = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*) FROM $messages_table 
        WHERE (recipient_id = %d OR (recipient_type = 'all' AND sender_id != %d) OR sender_id = %d)
        AND is_deleted = 0
        AND is_archived = 0
    ", $agent->ID, $agent->ID, $agent->ID));
    
    $unread_count = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*) FROM $messages_table 
        WHERE (recipient_id = %d OR (recipient_type = 'all' AND sender_id != %d)) 
        AND is_read = 0 
        AND is_deleted = 0
        AND is_archived = 0
    ", $agent->ID, $agent->ID));
    
    echo "الوكيل {$agent->display_name}: $agent_messages رسالة إجمالي، $unread_count غير مقروءة<br>";
}

// Test 4: Test Admin Confirmation Tracking
echo "<h2>4️⃣ اختبار تتبع تأكيدات المدير</h2>";

// Check if there are any assignments
$total_assignments = $wpdb->get_var("SELECT COUNT(*) FROM $assignments_table");
echo "إجمالي التخصيصات: $total_assignments<br>";

if ($total_assignments > 0) {
    // Check admin confirmations
    $admin_confirmations = $wpdb->get_var("SELECT COUNT(*) FROM $assignments_table WHERE confirmed_by_admin = 1");
    echo "التأكيدات بواسطة المدير: $admin_confirmations<br>";
    
    // Show recent assignments with admin tracking
    $recent_assignments = $wpdb->get_results("
        SELECT order_id, agent_id, confirmation_status, confirmed_by_admin, admin_user_id, original_agent_id
        FROM $assignments_table 
        ORDER BY assigned_at DESC 
        LIMIT 5
    ");
    
    if ($recent_assignments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>طلب</th><th>وكيل</th><th>حالة</th><th>مؤكد بواسطة مدير</th><th>معرف المدير</th><th>الوكيل الأصلي</th></tr>";
        foreach ($recent_assignments as $assignment) {
            echo "<tr>";
            echo "<td>{$assignment->order_id}</td>";
            echo "<td>{$assignment->agent_id}</td>";
            echo "<td>{$assignment->confirmation_status}</td>";
            echo "<td>" . ($assignment->confirmed_by_admin ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . ($assignment->admin_user_id ?: 'غير محدد') . "</td>";
            echo "<td>" . ($assignment->original_agent_id ?: 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "❌ لا توجد تخصيصات طلبات للاختبار<br>";
}

// Test 5: Test Archive Functionality
echo "<h2>5️⃣ اختبار وظائف الأرشفة</h2>";

// Check if there are any archived messages
$archived_count = $wpdb->get_var("SELECT COUNT(*) FROM $messages_table WHERE is_archived = 1");
echo "عدد الرسائل المؤرشفة: $archived_count<br>";

// Test archiving a message
if ($sent_count > 0) {
    $latest_message = $wpdb->get_row("SELECT * FROM $messages_table ORDER BY created_at DESC LIMIT 1");
    if ($latest_message) {
        // Archive it
        $wpdb->update(
            $messages_table,
            array('is_archived' => 1, 'archived_at' => current_time('mysql')),
            array('id' => $latest_message->id)
        );
        
        echo "✅ تم أرشفة رسالة اختبار (ID: {$latest_message->id})<br>";
        
        // Test unarchiving
        $wpdb->update(
            $messages_table,
            array('is_archived' => 0, 'archived_at' => null),
            array('id' => $latest_message->id)
        );
        
        echo "✅ تم إلغاء أرشفة الرسالة<br>";
    }
}

// Test 6: Overall System Health
echo "<h2>6️⃣ الحالة العامة للنظام</h2>";

$total_messages = $wpdb->get_var("SELECT COUNT(*) FROM $messages_table");
$total_unread = $wpdb->get_var("SELECT COUNT(*) FROM $messages_table WHERE is_read = 0 AND is_deleted = 0 AND is_archived = 0");
$total_archived = $wpdb->get_var("SELECT COUNT(*) FROM $messages_table WHERE is_archived = 1");

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📊 إحصائيات النظام:</h3>";
echo "• إجمالي الرسائل: $total_messages<br>";
echo "• الرسائل غير المقروءة: $total_unread<br>";
echo "• الرسائل المؤرشفة: $total_archived<br>";
echo "• عدد الوكلاء: " . count($agents) . "<br>";
echo "• إجمالي تخصيصات الطلبات: $total_assignments<br>";
echo "</div>";

// Final recommendations
echo "<h2>✅ التوصيات النهائية</h2>";
echo "<ol>";
echo "<li><strong>للوكلاء:</strong> تحديث صفحة لوحة الوكيل للتحقق من ظهور الرسائل</li>";
echo "<li><strong>للمدير:</strong> اختبار إرسال رسالة جديدة من واجهة المراسلة</li>";
echo "<li><strong>للإحصائيات:</strong> اختبار العمل كوكيل وتأكيد طلب للتحقق من تسجيل الإحصائيات للمدير</li>";
echo "<li><strong>للأرشفة:</strong> اختبار أرشفة وإلغاء أرشفة الرسائل</li>";
echo "</ol>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;'>";
echo "<h3>🎉 تم الانتهاء من الاختبار الشامل</h3>";
echo "<p>جميع المكونات تم اختبارها. يرجى التحقق من النتائج أعلاه والتأكد من عمل جميع الوظائف بشكل صحيح.</p>";
echo "</div>";
?>