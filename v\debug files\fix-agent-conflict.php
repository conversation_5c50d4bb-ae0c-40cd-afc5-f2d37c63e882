<?php
/**
 * Fix Agent ID Conflict
 * حل تضارب معرفات الوكلاء
 */

// WordPress environment
require_once('wp-config.php');

if (!current_user_can('manage_options')) {
    die('غير مصرح لك بالوصول');
}

echo "<h1>🔧 حل تضارب معرفات الوكلاء</h1>";

global $wpdb;

// Show all agents
echo "<h2>👥 جميع الوكلاء في النظام:</h2>";

$all_agents = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}hozi_agents ORDER BY id");

if ($all_agents) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID الوكيل</th><th>اسم الوكيل</th><th>ID المستخدم</th><th>الحالة</th><th>نشط</th><th>تاريخ الإنشاء</th></tr>";
    
    foreach ($all_agents as $agent) {
        $status_color = ($agent->status === 'active') ? 'green' : 'red';
        $active_color = ($agent->is_active == 1) ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>{$agent->id}</td>";
        echo "<td>{$agent->name}</td>";
        echo "<td>{$agent->user_id}</td>";
        echo "<td style='color: {$status_color};'>{$agent->status}</td>";
        echo "<td style='color: {$active_color};'>" . ($agent->is_active ? 'نعم' : 'لا') . "</td>";
        echo "<td>" . ($agent->created_at ? date('Y/m/d H:i', strtotime($agent->created_at)) : 'غير محدد') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا يوجد وكلاء في النظام.</p>";
}

// Show order assignments
echo "<h2>📋 تخصيصات الطلبات:</h2>";

$assignments = $wpdb->get_results("
    SELECT
        oa.id,
        oa.order_id,
        oa.agent_id,
        oa.confirmation_status,
        oa.assigned_at,
        oa.confirmed_at,
        a.name as agent_name,
        a.user_id as agent_user_id
    FROM {$wpdb->prefix}hozi_order_assignments oa
    LEFT JOIN {$wpdb->prefix}hozi_agents a ON oa.agent_id = a.id
    ORDER BY oa.assigned_at DESC
    LIMIT 10
");

if ($assignments) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID التخصيص</th><th>رقم الطلب</th><th>ID الوكيل</th><th>اسم الوكيل</th><th>ID المستخدم</th><th>حالة التأكيد</th><th>تاريخ التخصيص</th></tr>";
    
    foreach ($assignments as $assignment) {
        $status_color = ($assignment->confirmation_status === 'confirmed') ? 'green' : 'orange';
        
        echo "<tr>";
        echo "<td>{$assignment->id}</td>";
        echo "<td>#{$assignment->order_id}</td>";
        echo "<td>{$assignment->agent_id}</td>";
        echo "<td>{$assignment->agent_name}</td>";
        echo "<td>{$assignment->agent_user_id}</td>";
        echo "<td style='color: {$status_color};'>{$assignment->confirmation_status}</td>";
        echo "<td>" . date('Y/m/d H:i', strtotime($assignment->assigned_at)) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد تخصيصات طلبات.</p>";
}

// Get current user info
$current_user = wp_get_current_user();
echo "<h2>🎯 المستخدم الحالي:</h2>";
echo "<p><strong>ID المستخدم:</strong> {$current_user->ID}</p>";
echo "<p><strong>اسم المستخدم:</strong> {$current_user->user_login}</p>";

// Check if we need to fix the assignment
$current_agent = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}hozi_agents WHERE user_id = %d",
    $current_user->ID
));

if ($current_agent) {
    echo "<p><strong>الوكيل المرتبط:</strong> {$current_agent->name} (ID: {$current_agent->id})</p>";
    
    // Check orders for current agent
    $current_agent_orders = $wpdb->get_results($wpdb->prepare(
        "SELECT
            oa.order_id,
            oa.confirmation_status,
            oa.confirmed_at,
            oa.archived
        FROM {$wpdb->prefix}hozi_order_assignments oa
        WHERE oa.agent_id = %d
        ORDER BY oa.assigned_at DESC",
        $current_agent->id
    ));
    
    echo "<h3>📋 طلبات الوكيل الحالي:</h3>";
    echo "<p><strong>عدد الطلبات:</strong> " . count($current_agent_orders) . "</p>";
    
    if ($current_agent_orders) {
        $confirmed_count = 0;
        foreach ($current_agent_orders as $order) {
            if ($order->confirmation_status === 'confirmed' && ($order->archived === null || $order->archived == 0)) {
                $confirmed_count++;
            }
        }
        echo "<p><strong>الطلبات المؤكدة غير المؤرشفة:</strong> {$confirmed_count}</p>";
    }
}

// Handle fixing assignment
if (isset($_POST['fix_assignment'])) {
    $order_id = intval($_POST['order_id']);
    $new_agent_id = intval($_POST['new_agent_id']);
    
    echo "<h2>🔧 إصلاح تخصيص الطلب:</h2>";
    
    $result = $wpdb->update(
        $wpdb->prefix . 'hozi_order_assignments',
        array('agent_id' => $new_agent_id),
        array('order_id' => $order_id),
        array('%d'),
        array('%d')
    );
    
    if ($result !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>✅ تم إصلاح التخصيص بنجاح!</h3>";
        echo "<p>تم نقل الطلب #{$order_id} إلى الوكيل ID: {$new_agent_id}</p>";
        echo "</div>";
        
        // Refresh the page to show updated data
        echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إصلاح التخصيص: " . $wpdb->last_error . "</p>";
    }
}

// Handle creating new test order for current agent
if (isset($_POST['create_test_order'])) {
    echo "<h2>🆕 إنشاء طلب اختبار للوكيل الحالي:</h2>";
    
    try {
        // Create a test order
        $order = wc_create_order();
        
        // Add a simple product
        $products = wc_get_products(array('limit' => 1, 'status' => 'publish'));
        if (!empty($products)) {
            $product = $products[0];
            $order->add_product($product, 1);
        } else {
            // Create a simple test product
            $product = new WC_Product_Simple();
            $product->set_name('منتج اختبار - ' . $current_user->user_login);
            $product->set_status('publish');
            $product->set_price(150);
            $product->set_regular_price(150);
            $product->save();
            $order->add_product($product, 1);
        }
        
        // Set billing details
        $order->set_billing_first_name('عميل');
        $order->set_billing_last_name('اختبار ' . $current_user->user_login);
        $order->set_billing_phone('0987654321');
        $order->set_billing_email('test-' . $current_user->user_login . '@example.com');
        $order->set_billing_address_1('عنوان اختبار للوكيل ' . $current_agent->name);
        $order->set_billing_city('الرياض');
        $order->set_billing_country('SA');
        
        $order->calculate_totals();
        $order->set_status('processing');
        $order->save();
        
        $order_id = $order->get_id();
        
        // Assign to current agent
        $assignment_result = $wpdb->insert(
            $wpdb->prefix . 'hozi_order_assignments',
            array(
                'order_id' => $order_id,
                'agent_id' => $current_agent->id,
                'confirmation_status' => 'confirmed',
                'assigned_at' => current_time('mysql'),
                'confirmed_at' => current_time('mysql'),
                'assignment_method' => 'manual',
                'archived' => 0,
                'notes' => 'طلب اختبار للوكيل ' . $current_agent->name
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s', '%d', '%s')
        );
        
        if ($assignment_result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
            echo "<h3>✅ تم إنشاء طلب اختبار بنجاح!</h3>";
            echo "<p><strong>رقم الطلب:</strong> #{$order_id}</p>";
            echo "<p><strong>الوكيل:</strong> {$current_agent->name}</p>";
            echo "<p><strong>الحالة:</strong> مؤكد</p>";
            echo "</div>";
            
            // Trigger auto transfer to tracking
            $order->set_status('on-hold');
            $order->save();
            
            echo "<p>✅ تم تغيير حالة الطلب إلى on-hold لتفعيل النقل التلقائي</p>";
            
        } else {
            echo "<p style='color: red;'>❌ فشل في تخصيص الطلب: " . $wpdb->last_error . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    }
}

?>

<hr>
<h2>🛠️ إجراءات الإصلاح:</h2>

<?php if ($current_agent && $assignments): ?>
    <h3>📝 نقل طلب موجود للوكيل الحالي:</h3>
    <form method="post" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <p>نقل طلب موجود إلى الوكيل الحالي (<?php echo $current_agent->name; ?>):</p>
        <select name="order_id" required>
            <option value="">اختر طلب</option>
            <?php foreach ($assignments as $assignment): ?>
                <option value="<?php echo $assignment->order_id; ?>">
                    #<?php echo $assignment->order_id; ?> - <?php echo $assignment->agent_name; ?> (<?php echo $assignment->confirmation_status; ?>)
                </option>
            <?php endforeach; ?>
        </select>
        <input type="hidden" name="new_agent_id" value="<?php echo $current_agent->id; ?>">
        <button type="submit" name="fix_assignment" style="background: #007cba; color: white; padding: 8px 15px; border: none; border-radius: 4px; margin-left: 10px;">
            🔧 نقل الطلب
        </button>
    </form>
<?php endif; ?>

<?php if ($current_agent): ?>
    <h3>🆕 إنشاء طلب اختبار جديد:</h3>
    <form method="post" style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <p>إنشاء طلب اختبار جديد للوكيل الحالي (<?php echo $current_agent->name; ?>):</p>
        <button type="submit" name="create_test_order" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
            ✨ إنشاء طلب اختبار
        </button>
    </form>
<?php else: ?>
    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <h3>⚠️ المستخدم الحالي ليس وكيل</h3>
        <p>يجب إنشاء وكيل مرتبط بالمستخدم الحالي أولاً.</p>
    </div>
<?php endif; ?>

<h2>🔗 روابط مفيدة:</h2>
<p><a href="admin.php?page=hozi-akadly-delivery-tracking" target="_blank" style="background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">📦 صفحة متابعة التوصيل</a></p>
<p><a href="admin.php?page=hozi-akadly-agents" target="_blank" style="background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">👥 إدارة الوكلاء</a></p>
<p><a href="check-error-logs.php" target="_blank" style="background: #6c757d; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;">📋 فحص السجلات</a></p>
